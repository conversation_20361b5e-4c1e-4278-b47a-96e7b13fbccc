package com.cn.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cn.annotation.Authority;
import com.cn.common.Result;
import com.cn.constants.Constants;
import com.cn.entity.AuthorityType;
import com.cn.entity.Good;
import com.cn.entity.Standard;
import com.cn.entity.dto.GoodDTO;
import com.cn.service.GoodService;
import com.cn.service.StandardService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/api/good")
public class GoodController {
    @Resource
    private GoodService goodService;
    @Resource
    private StandardService standardService;

    /**
     * 127.0.0.1:9197//api/good/page
     * aram pageNum
     * @param pageSize
     * @param searchText
     * @param categoryId
     * @return
     */
    @RequestMapping(value = "/page",method = RequestMethod.GET)
    public Result findPage(
                            @RequestParam(required = false, defaultValue = "1") Integer pageNum,
                            @RequestParam(required = false, defaultValue = "10") Integer pageSize,
                            @RequestParam(required = false, defaultValue = "") String searchText,
                            @RequestParam(required = false) Integer categoryId) {

        IPage<GoodDTO> page = goodService.findPage(pageNum, pageSize, searchText, categoryId);
        return Result.success(page);
    }

    /**
     * 127.0.0.1:9197//api/good/findFrontGoods
     * 前端首页推荐商品
     * @return
     */
    @RequestMapping(value = "findFrontGoods")
    public  Result findFrontGoods(){
        List<GoodDTO> frontGoods = goodService.findFrontGoods();
        return Result.success(frontGoods);
    }


    /**
     * 保存商品的规格信息
     * @param standards
     * @param goodId
     * @return
     */
    @PostMapping("/standard")
    public Result saveStandard(@RequestBody List<Standard> standards, @RequestParam int goodId) {
        //先删除全部旧记录
        standardService.deleteAll(goodId);
        //然后插入新记录
        for (Standard standard : standards) {
            standard.setGoodId(goodId);
            if(!standardService.save(standard)){
                return Result.error(Constants.CODE_500,"保存失败");
            }
        }
        return Result.success();
    }

    /**
     * 保存商品信息
     * @param good
     * @return
     */
    @PostMapping("/save")
    public Result save(@RequestBody Good good) {
        System.out.println(good);
        return Result.success(goodService.saveOrUpdateGood(good));
    }

    /**
     *修改商品
     * @param good
     * @return
     */
    @Authority(AuthorityType.requireAuthority)
    @PutMapping("/update")
    public Result update(@RequestBody Good good) {
        goodService.update(good);
        return Result.success();
    }

    /**
     * 根据id删除商品信息
     * @param id
     * @return
     */
    @Authority(AuthorityType.requireAuthority)
    @DeleteMapping("/{id}")
    public Result delete(@PathVariable Long id) {
        goodService.deleteGood(id);
        return Result.success();
    }

    /**
     * 根据id查询
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public Result findById(@PathVariable Long id) {
        return Result.success(goodService.getGoodById(id));
    }

    //获取商品的规格信息
    @GetMapping("/standard/{id}")
    public Result getStandard(@PathVariable int id) {
        return Result.success(goodService.getStandard(id));
    }
    //查询推荐商品，即recommend=1
    @GetMapping
    public Result findAll() {

        return Result.success(goodService.findFrontGoods());
    }
    //查询销量排行
    @GetMapping("/rank")
    public Result getSaleRank(@RequestParam int num){
        return Result.success(goodService.getSaleRank(num));
    }


}
