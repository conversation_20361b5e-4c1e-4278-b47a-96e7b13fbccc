<template>
  <div class="login-demo">
    <div class="login-container">
      <div class="login-card">
        <h2 class="login-title">在线购物商城</h2>
        <p class="login-subtitle">请登录您的账户</p>
        
        <el-form 
          :model="loginForm" 
          :rules="rules" 
          ref="loginFormRef"
          class="login-form"
          @submit.prevent="handleLogin"
        >
          <el-form-item prop="username">
            <el-input
              v-model="loginForm.username"
              placeholder="请输入用户名"
              size="large"
              prefix-icon="User"
            />
          </el-form-item>
          
          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              type="password"
              placeholder="请输入密码"
              size="large"
              prefix-icon="Lock"
              show-password
              @keyup.enter="handleLogin"
            />
          </el-form-item>
          
          <el-form-item>
            <el-button 
              type="primary" 
              size="large" 
              class="login-btn"
              :loading="loading"
              @click="handleLogin"
            >
              {{ loading ? '登录中...' : '登录' }}
            </el-button>
          </el-form-item>
        </el-form>
        
        <div class="demo-info">
          <el-alert
            title="演示账户"
            type="info"
            :closable="false"
            show-icon
          >
            <template #default>
              <p><strong>用户名:</strong> admin</p>
              <p><strong>密码:</strong> 123456</p>
            </template>
          </el-alert>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { useUserStore } from '@/store/index'

export default {
  name: 'LoginDemo',
  
  data() {
    return {
      loading: false,
      loginForm: {
        username: 'admin',
        password: '123456'
      },
      rules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
        ]
      }
    }
  },
  
  created() {
    // 检查是否已经登录
    const userStore = useUserStore();
    userStore.restoreLoginState();
    if (userStore.isAuthenticated) {
      console.log('用户已登录，跳转到商品页');
      this.$router.push('/goods');
    }
  },
  
  methods: {
    async handleLogin() {
      try {
        // 表单验证
        await this.$refs.loginFormRef.validate();
        
        this.loading = true;
        
        // 模拟登录请求
        const response = await this.mockLogin();
        
        if (response.success) {
          this.$message.success('登录成功！');
          
          // 保存用户信息
          const userStore = useUserStore();
          const user = {
            username: this.loginForm.username,
            role: 'user',
            id: Date.now(),
            avatar: ''
          };
          const token = 'demo-token-' + Date.now();
          
          userStore.login(user, token);
          
          // 跳转到商品页
          this.$router.push('/goods');
        } else {
          this.$message.error(response.message || '登录失败');
        }
      } catch (error) {
        console.error('登录错误:', error);
        this.$message.error('登录失败，请重试');
      } finally {
        this.loading = false;
      }
    },
    
    // 模拟登录API
    async mockLogin() {
      return new Promise((resolve) => {
        setTimeout(() => {
          if (this.loginForm.username === 'admin' && this.loginForm.password === '123456') {
            resolve({
              success: true,
              data: {
                username: this.loginForm.username,
                role: 'admin'
              }
            });
          } else {
            resolve({
              success: false,
              message: '用户名或密码错误'
            });
          }
        }, 1000);
      });
    }
  }
}
</script>

<style scoped>
.login-demo {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.login-container {
  width: 100%;
  max-width: 400px;
}

.login-card {
  background: white;
  border-radius: 16px;
  padding: 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.login-title {
  text-align: center;
  font-size: 28px;
  color: #333;
  margin: 0 0 8px;
  font-weight: bold;
}

.login-subtitle {
  text-align: center;
  color: #666;
  margin: 0 0 30px;
  font-size: 16px;
}

.login-form {
  margin-bottom: 20px;
}

.login-btn {
  width: 100%;
  height: 48px;
  font-size: 16px;
  font-weight: bold;
}

.demo-info {
  margin-top: 20px;
}

.demo-info p {
  margin: 5px 0;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .login-card {
    padding: 30px 20px;
  }
  
  .login-title {
    font-size: 24px;
  }
}
</style>
