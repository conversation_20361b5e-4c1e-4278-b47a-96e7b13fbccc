<template>
  <div class="carousel-container">
    <div class="carousel-wrapper" v-if="carouselList.length > 0">
      <div class="carousel-content" :style="{ transform: `translateX(-${currentIndex * 100}%)` }">
        <div
          class="carousel-item"
          v-for="item in carouselList"
          :key="item.id"
          @click="handleCarouselClick(item)"
        >
          <img :src="item.img" :alt="item.goodName" class="carousel-image" />
          <div class="carousel-overlay">
            <h3 class="carousel-title">{{ item.goodName }}</h3>
          </div>
        </div>
      </div>
      
      <!-- 指示器 -->
      <div class="carousel-indicators">
        <span 
          v-for="(item, index) in carouselList" 
          :key="index"
          :class="['indicator', { active: index === currentIndex }]"
          @click="goToSlide(index)"
        ></span>
      </div>
      
      <!-- 导航按钮 -->
      <button class="carousel-btn prev-btn" @click="prevSlide" v-if="carouselList.length > 1">
        <i class="el-icon-arrow-left"></i>
      </button>
      <button class="carousel-btn next-btn" @click="nextSlide" v-if="carouselList.length > 1">
        <i class="el-icon-arrow-right"></i>
      </button>
    </div>
    
    <!-- 加载状态 -->
    <div v-else-if="loading" class="carousel-loading">
      <el-skeleton animated>
        <template #template>
          <el-skeleton-item variant="image" style="width: 100%; height: 300px;" />
        </template>
      </el-skeleton>
    </div>
  </div>
</template>

<script>
import request from '@/utils/request.js'

export default {
  name: 'CarouselComponent',
  
  data() {
    return {
      carouselList: [],
      currentIndex: 0,
      loading: true,
      autoPlayTimer: null,
      autoPlayInterval: 4000 // 4秒自动切换
    }
  },
  
  mounted() {
    this.fetchCarouselData()

    // 防止 ResizeObserver 错误
    this.$nextTick(() => {
      // 确保DOM完全渲染后再执行相关操作
    })
  },
  
  beforeUnmount() {
    this.clearAutoPlay()
  },
  
  methods: {
    // 获取轮播图数据
    async fetchCarouselData() {
      try {
        this.loading = true
        const response = await request.get('/carousel')
        this.carouselList = Array.isArray(response) ? response : []
        
        if (this.carouselList.length > 0) {
          this.startAutoPlay()
        }
      } catch (error) {
        console.error('获取轮播图数据失败:', error)
        this.carouselList = []
      } finally {
        this.loading = false
      }
    },
    
    // 下一张
    nextSlide() {
      if (this.carouselList.length === 0) return
      this.currentIndex = (this.currentIndex + 1) % this.carouselList.length
    },
    
    // 上一张
    prevSlide() {
      if (this.carouselList.length === 0) return
      this.currentIndex = this.currentIndex === 0 ? this.carouselList.length - 1 : this.currentIndex - 1
    },
    
    // 跳转到指定幻灯片
    goToSlide(index) {
      this.currentIndex = index
      this.resetAutoPlay()
    },
    
    // 开始自动播放
    startAutoPlay() {
      if (this.carouselList.length <= 1) return
      this.autoPlayTimer = setInterval(() => {
        this.nextSlide()
      }, this.autoPlayInterval)
    },
    
    // 清除自动播放
    clearAutoPlay() {
      if (this.autoPlayTimer) {
        clearInterval(this.autoPlayTimer)
        this.autoPlayTimer = null
      }
    },
    
    // 重置自动播放
    resetAutoPlay() {
      this.clearAutoPlay()
      this.startAutoPlay()
    },
    
    // 点击轮播图项目
    handleCarouselClick(item) {
      // 可以跳转到商品详情页或触发其他事件
      this.$emit('carousel-click', item)
      console.log('点击轮播图商品:', item.goodName)
    }
  }
}
</script>

<style scoped>
.carousel-container {
  width: 100%;
  margin-bottom: 30px;
}

.carousel-wrapper {
  position: relative;
  width: 100%;
  height: 300px;
  overflow: hidden;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.carousel-content {
  display: flex;
  width: 100%;
  height: 100%;
  transition: transform 0.5s ease-in-out;
  will-change: transform;
}

.carousel-item {
  flex: 0 0 100%;
  position: relative;
  cursor: pointer;
}

.carousel-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.carousel-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  color: white;
  padding: 20px;
}

.carousel-title {
  margin: 0;
  font-size: 24px;
  font-weight: bold;
}

.carousel-indicators {
  position: absolute;
  bottom: 15px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 8px;
}

.indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: background 0.3s;
}

.indicator.active {
  background: white;
}

.carousel-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.3s;
  font-size: 18px;
}

.carousel-btn:hover {
  background: rgba(0, 0, 0, 0.7);
}

.prev-btn {
  left: 15px;
}

.next-btn {
  right: 15px;
}

.carousel-loading {
  width: 100%;
  height: 300px;
  border-radius: 12px;
  overflow: hidden;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .carousel-wrapper {
    height: 200px;
  }
  
  .carousel-title {
    font-size: 18px;
  }
  
  .carousel-btn {
    width: 35px;
    height: 35px;
    font-size: 16px;
  }
  
  .prev-btn {
    left: 10px;
  }
  
  .next-btn {
    right: 10px;
  }
}
</style>
