{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { useUserStore } from '@/store/index';\nimport { User, ArrowDown, List, ShoppingCart, SwitchButton } from '@element-plus/icons-vue';\nexport default {\n  name: 'MainLayout',\n  components: {\n    User,\n    ArrowDown,\n    List,\n    ShoppingCart,\n    SwitchButton\n  },\n  data() {\n    return {\n      activeIndex: 'goods',\n      userStore: null\n    };\n  },\n  computed: {\n    isLoggedIn() {\n      return this.userStore?.isAuthenticated || false;\n    },\n    username() {\n      return this.userStore?.username || '用户';\n    },\n    userAvatar() {\n      // 可以从用户信息中获取头像URL\n      return this.userStore?.userInfo?.avatar || '';\n    }\n  },\n  methods: {\n    handleMenuSelect(key) {\n      this.activeIndex = key;\n      switch (key) {\n        case 'home':\n          this.$router.push('/home');\n          break;\n        case 'goods':\n          this.$router.push('/goods');\n          break;\n        case 'test':\n          this.$router.push('/test');\n          break;\n        case 'category':\n          this.$message.info('分类页面开发中...');\n          break;\n        case 'cart':\n          this.$message.info('购物车页面开发中...');\n          break;\n      }\n    },\n    // 处理用户下拉菜单命令\n    handleUserCommand(command) {\n      switch (command) {\n        case 'profile':\n          this.$message.info('个人中心功能开发中...');\n          break;\n        case 'orders':\n          this.$message.info('我的订单功能开发中...');\n          break;\n        case 'cart':\n          this.$message.info('购物车功能开发中...');\n          break;\n        case 'logout':\n          this.handleLogout();\n          break;\n      }\n    },\n    // 退出登录\n    handleLogout() {\n      this.$confirm('确定要退出登录吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        // 清除登录状态\n        this.userStore.logout();\n        this.$message.success('已退出登录');\n\n        // 跳转到登录页\n        this.$router.push('/login');\n      }).catch(() => {\n        // 取消退出\n      });\n    },\n    goToLogin() {\n      this.$router.push('/login');\n    },\n    goToRegister() {\n      this.$router.push('/register');\n    }\n  },\n  mounted() {\n    // 根据当前路由设置活动菜单项\n    const path = this.$route.path;\n    if (path.includes('/goods')) {\n      this.activeIndex = 'goods';\n    } else if (path === '/') {\n      this.activeIndex = 'home';\n    }\n  }\n};", "map": {"version": 3, "names": ["useUserStore", "User", "ArrowDown", "List", "ShoppingCart", "SwitchButton", "name", "components", "data", "activeIndex", "userStore", "computed", "isLoggedIn", "isAuthenticated", "username", "userAvatar", "userInfo", "avatar", "methods", "handleMenuSelect", "key", "$router", "push", "$message", "info", "handleUserCommand", "command", "handleLogout", "$confirm", "confirmButtonText", "cancelButtonText", "type", "then", "logout", "success", "catch", "goToLogin", "goToRegister", "mounted", "path", "$route", "includes"], "sources": ["D:\\2025_down\\project\\shoppingOnline_2025_91\\shoppingOnline_front-2025-91\\src\\views\\MainLayout.vue"], "sourcesContent": ["<template>\n  <div class=\"main-layout\">\n    <!-- 顶部导航栏 -->\n    <header class=\"header\">\n      <div class=\"header-content\">\n        <div class=\"logo\">\n          <img src=\"@/resource/logo.png\" alt=\"商城Logo\" class=\"logo-img\" />\n          <span class=\"logo-text\">在线购物商城</span>\n        </div>\n        \n        <!-- 导航菜单 -->\n        <nav class=\"nav-menu\">\n          <el-menu\n            mode=\"horizontal\"\n            :default-active=\"activeIndex\"\n            class=\"nav-menu-el\"\n            @select=\"handleMenuSelect\"\n          >\n            <el-menu-item index=\"home\">首页</el-menu-item>\n            <el-menu-item index=\"goods\">商品</el-menu-item>\n            <el-menu-item index=\"test\">功能测试</el-menu-item>\n            <el-menu-item index=\"category\">分类</el-menu-item>\n            <el-menu-item index=\"cart\">购物车</el-menu-item>\n          </el-menu>\n        </nav>\n        \n        <!-- 用户操作区 -->\n        <div class=\"user-actions\">\n          <div v-if=\"isLoggedIn\" class=\"user-info\">\n            <el-dropdown @command=\"handleUserCommand\">\n              <span class=\"user-dropdown\">\n                <el-avatar :size=\"32\" :src=\"userAvatar\">\n                  <el-icon><User /></el-icon>\n                </el-avatar>\n                <span class=\"username\">{{ username }}</span>\n                <el-icon class=\"dropdown-icon\"><ArrowDown /></el-icon>\n              </span>\n              <template #dropdown>\n                <el-dropdown-menu>\n                  <el-dropdown-item command=\"profile\">\n                    <el-icon><User /></el-icon>\n                    个人中心\n                  </el-dropdown-item>\n                  <el-dropdown-item command=\"orders\">\n                    <el-icon><List /></el-icon>\n                    我的订单\n                  </el-dropdown-item>\n                  <el-dropdown-item command=\"cart\">\n                    <el-icon><ShoppingCart /></el-icon>\n                    购物车\n                  </el-dropdown-item>\n                  <el-dropdown-item divided command=\"logout\">\n                    <el-icon><SwitchButton /></el-icon>\n                    退出登录\n                  </el-dropdown-item>\n                </el-dropdown-menu>\n              </template>\n            </el-dropdown>\n          </div>\n          <div v-else class=\"login-actions\">\n            <el-button type=\"text\" @click=\"goToLogin\">登录</el-button>\n            <el-button type=\"primary\" @click=\"goToRegister\">注册</el-button>\n          </div>\n        </div>\n      </div>\n    </header>\n    \n    <!-- 主要内容区域 -->\n    <main class=\"main-content\">\n      <router-view />\n    </main>\n    \n    <!-- 底部 -->\n    <footer class=\"footer\">\n      <div class=\"footer-content\">\n        <p>&copy; 2025 在线购物商城. All rights reserved.</p>\n      </div>\n    </footer>\n  </div>\n</template>\n\n<script>\nimport { useUserStore } from '@/store/index'\nimport { User, ArrowDown, List, ShoppingCart, SwitchButton } from '@element-plus/icons-vue'\n\nexport default {\n  name: 'MainLayout',\n\n  components: {\n    User,\n    ArrowDown,\n    List,\n    ShoppingCart,\n    SwitchButton\n  },\n\n  data() {\n    return {\n      activeIndex: 'goods',\n      userStore: null\n    }\n  },\n\n  computed: {\n    isLoggedIn() {\n      return this.userStore?.isAuthenticated || false\n    },\n    username() {\n      return this.userStore?.username || '用户'\n    },\n    userAvatar() {\n      // 可以从用户信息中获取头像URL\n      return this.userStore?.userInfo?.avatar || ''\n    }\n  },\n  \n  methods: {\n    handleMenuSelect(key) {\n      this.activeIndex = key;\n      switch (key) {\n        case 'home':\n          this.$router.push('/home');\n          break;\n        case 'goods':\n          this.$router.push('/goods');\n          break;\n        case 'test':\n          this.$router.push('/test');\n          break;\n        case 'category':\n          this.$message.info('分类页面开发中...');\n          break;\n        case 'cart':\n          this.$message.info('购物车页面开发中...');\n          break;\n      }\n    },\n\n    // 处理用户下拉菜单命令\n    handleUserCommand(command) {\n      switch (command) {\n        case 'profile':\n          this.$message.info('个人中心功能开发中...');\n          break;\n        case 'orders':\n          this.$message.info('我的订单功能开发中...');\n          break;\n        case 'cart':\n          this.$message.info('购物车功能开发中...');\n          break;\n        case 'logout':\n          this.handleLogout();\n          break;\n      }\n    },\n\n    // 退出登录\n    handleLogout() {\n      this.$confirm('确定要退出登录吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        // 清除登录状态\n        this.userStore.logout();\n\n        this.$message.success('已退出登录');\n\n        // 跳转到登录页\n        this.$router.push('/login');\n      }).catch(() => {\n        // 取消退出\n      });\n    },\n\n    goToLogin() {\n      this.$router.push('/login');\n    },\n\n    goToRegister() {\n      this.$router.push('/register');\n    }\n  },\n  \n  mounted() {\n    // 根据当前路由设置活动菜单项\n    const path = this.$route.path;\n    if (path.includes('/goods')) {\n      this.activeIndex = 'goods';\n    } else if (path === '/') {\n      this.activeIndex = 'home';\n    }\n  }\n}\n</script>\n\n<style scoped>\n.main-layout {\n  min-height: 100vh;\n  display: flex;\n  flex-direction: column;\n}\n\n.header {\n  background: white;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  position: sticky;\n  top: 0;\n  z-index: 1000;\n}\n\n.header-content {\n  max-width: 1200px;\n  margin: 0 auto;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0 16px;\n  height: 64px;\n}\n\n.logo {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.logo-img {\n  height: 40px;\n  width: auto;\n}\n\n.logo-text {\n  font-size: 20px;\n  font-weight: bold;\n  color: #333;\n}\n\n.nav-menu-el {\n  border-bottom: none;\n}\n\n.user-actions {\n  display: flex;\n  gap: 8px;\n}\n\n.main-content {\n  flex: 1;\n  background: #f5f5f5;\n  min-height: calc(100vh - 64px - 60px);\n}\n\n.footer {\n  background: #333;\n  color: white;\n  text-align: center;\n  padding: 20px 0;\n}\n\n.footer-content {\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .header-content {\n    padding: 0 12px;\n  }\n  \n  .logo-text {\n    display: none;\n  }\n  \n  .nav-menu {\n    display: none;\n  }\n  \n  .user-actions {\n    gap: 4px;\n  }\n}\n</style>\n"], "mappings": ";AAkFA,SAASA,YAAW,QAAS,eAAc;AAC3C,SAASC,IAAI,EAAEC,SAAS,EAAEC,IAAI,EAAEC,YAAY,EAAEC,YAAW,QAAS,yBAAwB;AAE1F,eAAe;EACbC,IAAI,EAAE,YAAY;EAElBC,UAAU,EAAE;IACVN,IAAI;IACJC,SAAS;IACTC,IAAI;IACJC,YAAY;IACZC;EACF,CAAC;EAEDG,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,WAAW,EAAE,OAAO;MACpBC,SAAS,EAAE;IACb;EACF,CAAC;EAEDC,QAAQ,EAAE;IACRC,UAAUA,CAAA,EAAG;MACX,OAAO,IAAI,CAACF,SAAS,EAAEG,eAAc,IAAK,KAAI;IAChD,CAAC;IACDC,QAAQA,CAAA,EAAG;MACT,OAAO,IAAI,CAACJ,SAAS,EAAEI,QAAO,IAAK,IAAG;IACxC,CAAC;IACDC,UAAUA,CAAA,EAAG;MACX;MACA,OAAO,IAAI,CAACL,SAAS,EAAEM,QAAQ,EAAEC,MAAK,IAAK,EAAC;IAC9C;EACF,CAAC;EAEDC,OAAO,EAAE;IACPC,gBAAgBA,CAACC,GAAG,EAAE;MACpB,IAAI,CAACX,WAAU,GAAIW,GAAG;MACtB,QAAQA,GAAG;QACT,KAAK,MAAM;UACT,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC,OAAO,CAAC;UAC1B;QACF,KAAK,OAAO;UACV,IAAI,CAACD,OAAO,CAACC,IAAI,CAAC,QAAQ,CAAC;UAC3B;QACF,KAAK,MAAM;UACT,IAAI,CAACD,OAAO,CAACC,IAAI,CAAC,OAAO,CAAC;UAC1B;QACF,KAAK,UAAU;UACb,IAAI,CAACC,QAAQ,CAACC,IAAI,CAAC,YAAY,CAAC;UAChC;QACF,KAAK,MAAM;UACT,IAAI,CAACD,QAAQ,CAACC,IAAI,CAAC,aAAa,CAAC;UACjC;MACJ;IACF,CAAC;IAED;IACAC,iBAAiBA,CAACC,OAAO,EAAE;MACzB,QAAQA,OAAO;QACb,KAAK,SAAS;UACZ,IAAI,CAACH,QAAQ,CAACC,IAAI,CAAC,cAAc,CAAC;UAClC;QACF,KAAK,QAAQ;UACX,IAAI,CAACD,QAAQ,CAACC,IAAI,CAAC,cAAc,CAAC;UAClC;QACF,KAAK,MAAM;UACT,IAAI,CAACD,QAAQ,CAACC,IAAI,CAAC,aAAa,CAAC;UACjC;QACF,KAAK,QAAQ;UACX,IAAI,CAACG,YAAY,CAAC,CAAC;UACnB;MACJ;IACF,CAAC;IAED;IACAA,YAAYA,CAAA,EAAG;MACb,IAAI,CAACC,QAAQ,CAAC,WAAW,EAAE,IAAI,EAAE;QAC/BC,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBC,IAAI,EAAE;MACR,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;QACZ;QACA,IAAI,CAACtB,SAAS,CAACuB,MAAM,CAAC,CAAC;QAEvB,IAAI,CAACV,QAAQ,CAACW,OAAO,CAAC,OAAO,CAAC;;QAE9B;QACA,IAAI,CAACb,OAAO,CAACC,IAAI,CAAC,QAAQ,CAAC;MAC7B,CAAC,CAAC,CAACa,KAAK,CAAC,MAAM;QACb;MAAA,CACD,CAAC;IACJ,CAAC;IAEDC,SAASA,CAAA,EAAG;MACV,IAAI,CAACf,OAAO,CAACC,IAAI,CAAC,QAAQ,CAAC;IAC7B,CAAC;IAEDe,YAAYA,CAAA,EAAG;MACb,IAAI,CAAChB,OAAO,CAACC,IAAI,CAAC,WAAW,CAAC;IAChC;EACF,CAAC;EAEDgB,OAAOA,CAAA,EAAG;IACR;IACA,MAAMC,IAAG,GAAI,IAAI,CAACC,MAAM,CAACD,IAAI;IAC7B,IAAIA,IAAI,CAACE,QAAQ,CAAC,QAAQ,CAAC,EAAE;MAC3B,IAAI,CAAChC,WAAU,GAAI,OAAO;IAC5B,OAAO,IAAI8B,IAAG,KAAM,GAAG,EAAE;MACvB,IAAI,CAAC9B,WAAU,GAAI,MAAM;IAC3B;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}