<template>
  <div class="search-container">
    <div class="search-wrapper">
      <el-input
        v-model="searchText"
        placeholder="搜索商品..."
        size="large"
        class="search-input"
        @keyup.enter="handleSearch"
        @input="handleInput"
        clearable
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
        <template #suffix>
          <el-button 
            type="primary" 
            @click="handleSearch"
            :loading="searching"
            class="search-btn"
          >
            {{ searching ? '搜索中...' : '搜索' }}
          </el-button>
        </template>
      </el-input>
      
      <!-- 搜索建议下拉框 -->
      <div v-if="showSuggestions && suggestions.length > 0" class="suggestions-dropdown">
        <div 
          v-for="(suggestion, index) in suggestions" 
          :key="index"
          class="suggestion-item"
          @click="selectSuggestion(suggestion)"
        >
          <el-icon><Search /></el-icon>
          <span>{{ suggestion.name }}</span>
          <span class="suggestion-price">¥{{ suggestion.price }}</span>
        </div>
      </div>
    </div>
    
    <!-- 热门搜索标签 -->
    <div class="hot-search" v-if="hotKeywords.length > 0">
      <span class="hot-label">热门搜索：</span>
      <el-tag 
        v-for="keyword in hotKeywords" 
        :key="keyword"
        class="hot-tag"
        @click="searchByKeyword(keyword)"
        effect="plain"
      >
        {{ keyword }}
      </el-tag>
    </div>
  </div>
</template>

<script>
import { Search } from '@element-plus/icons-vue'
import request from '@/utils/request.js'

export default {
  name: 'SearchBox',
  
  components: {
    Search
  },
  
  data() {
    return {
      searchText: '',
      searching: false,
      showSuggestions: false,
      suggestions: [],
      hotKeywords: ['手机', '电脑', '耳机', '键盘', '鼠标'],
      debounceTimer: null
    }
  },
  
  methods: {
    // 处理搜索
    async handleSearch() {
      if (!this.searchText.trim()) {
        this.$message.warning('请输入搜索关键词')
        return
      }
      
      this.searching = true
      this.showSuggestions = false
      
      try {
        // 触发搜索事件，传递搜索文本给父组件
        this.$emit('search', this.searchText.trim())
        
        // 可以在这里调用搜索API
        const response = await request.get('/good/page', {
          searchText: this.searchText.trim(),
          pageNum: 1,
          pageSize: 20
        })
        
        // 将搜索结果传递给父组件
        this.$emit('search-results', response)
        
      } catch (error) {
        console.error('搜索失败:', error)
        this.$message.error('搜索失败，请重试')
      } finally {
        this.searching = false
      }
    },
    
    // 处理输入变化
    handleInput() {
      // 清除之前的定时器
      if (this.debounceTimer) {
        clearTimeout(this.debounceTimer)
      }
      
      // 设置防抖，500ms后执行搜索建议
      this.debounceTimer = setTimeout(() => {
        if (this.searchText.trim().length > 0) {
          this.fetchSuggestions()
        } else {
          this.showSuggestions = false
        }
      }, 500)
    },
    
    // 获取搜索建议
    async fetchSuggestions() {
      try {
        const response = await request.get('/good/page', {
          searchText: this.searchText.trim(),
          pageNum: 1,
          pageSize: 5
        })
        
        if (response && response.records) {
          this.suggestions = response.records.slice(0, 5) // 最多显示5个建议
          this.showSuggestions = true
        }
      } catch (error) {
        console.error('获取搜索建议失败:', error)
        this.showSuggestions = false
      }
    },
    
    // 选择搜索建议
    selectSuggestion(suggestion) {
      this.searchText = suggestion.name
      this.showSuggestions = false
      this.handleSearch()
    },
    
    // 通过热门关键词搜索
    searchByKeyword(keyword) {
      this.searchText = keyword
      this.handleSearch()
    },
    
    // 清空搜索
    clearSearch() {
      this.searchText = ''
      this.showSuggestions = false
      this.$emit('clear-search')
    }
  },
  
  mounted() {
    // 点击外部关闭建议框
    document.addEventListener('click', (e) => {
      if (!this.$el.contains(e.target)) {
        this.showSuggestions = false
      }
    })
  },
  
  beforeUnmount() {
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer)
    }
  }
}
</script>

<style scoped>
.search-container {
  width: 100%;
  max-width: 600px;
  margin: 0 auto 20px;
}

.search-wrapper {
  position: relative;
}

.search-input {
  width: 100%;
}

.search-input :deep(.el-input__inner) {
  border-radius: 25px;
  padding-left: 45px;
  padding-right: 120px;
  height: 50px;
  font-size: 16px;
}

.search-btn {
  border-radius: 20px;
  margin-right: 5px;
}

.suggestions-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  max-height: 300px;
  overflow-y: auto;
}

.suggestion-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  transition: background 0.2s;
  gap: 8px;
}

.suggestion-item:hover {
  background: #f5f7fa;
}

.suggestion-item:not(:last-child) {
  border-bottom: 1px solid #f0f0f0;
}

.suggestion-price {
  margin-left: auto;
  color: #e60000;
  font-weight: bold;
}

.hot-search {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 15px;
  padding: 0 5px;
}

.hot-label {
  color: #666;
  font-size: 14px;
  white-space: nowrap;
}

.hot-tag {
  cursor: pointer;
  transition: all 0.2s;
}

.hot-tag:hover {
  background: #409eff;
  color: white;
  border-color: #409eff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-input :deep(.el-input__inner) {
    height: 45px;
    font-size: 14px;
    padding-right: 100px;
  }
  
  .search-btn {
    font-size: 14px;
  }
  
  .hot-search {
    margin-top: 10px;
  }
  
  .hot-label {
    font-size: 12px;
  }
}
</style>
