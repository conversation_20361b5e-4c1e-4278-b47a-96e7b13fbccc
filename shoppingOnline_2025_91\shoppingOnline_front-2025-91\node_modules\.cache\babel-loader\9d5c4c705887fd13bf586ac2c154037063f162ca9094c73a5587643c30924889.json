{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { useUserStore } from '@/store/index';\nimport request from '@/utils/request';\nexport default {\n  name: 'LoginDemo',\n  data() {\n    return {\n      loading: false,\n      loginForm: {\n        username: 'admin',\n        password: '123456'\n      },\n      rules: {\n        username: [{\n          required: true,\n          message: '请输入用户名',\n          trigger: 'blur'\n        }],\n        password: [{\n          required: true,\n          message: '请输入密码',\n          trigger: 'blur'\n        }, {\n          min: 6,\n          message: '密码长度不能少于6位',\n          trigger: 'blur'\n        }]\n      }\n    };\n  },\n  created() {\n    // 检查是否已经登录\n    const userStore = useUserStore();\n    userStore.restoreLoginState();\n    if (userStore.isAuthenticated) {\n      console.log('用户已登录，跳转到商品页');\n      this.$router.push('/goods');\n    }\n  },\n  methods: {\n    async handleLogin() {\n      try {\n        // 表单验证\n        await this.$refs.loginFormRef.validate();\n        this.loading = true;\n\n        // 模拟登录请求\n        const response = await this.mockLogin();\n        if (response.success) {\n          this.$message.success('登录成功！');\n\n          // 保存用户信息\n          const userStore = useUserStore();\n          const user = {\n            username: this.loginForm.username,\n            role: 'user',\n            id: Date.now(),\n            avatar: ''\n          };\n          const token = 'demo-token-' + Date.now();\n          userStore.login(user, token);\n\n          // 跳转到商品页\n          this.$router.push('/goods');\n        } else {\n          this.$message.error(response.message || '登录失败');\n        }\n      } catch (error) {\n        console.error('登录错误:', error);\n        this.$message.error('登录失败，请重试');\n      } finally {\n        this.loading = false;\n      }\n    },\n    // 模拟登录API\n    async mockLogin() {\n      return new Promise(resolve => {\n        setTimeout(() => {\n          if (this.loginForm.username === 'admin' && this.loginForm.password === '123456') {\n            resolve({\n              success: true,\n              data: {\n                username: this.loginForm.username,\n                role: 'admin'\n              }\n            });\n          } else {\n            resolve({\n              success: false,\n              message: '用户名或密码错误'\n            });\n          }\n        }, 1000);\n      });\n    }\n  }\n};", "map": {"version": 3, "names": ["useUserStore", "request", "name", "data", "loading", "loginForm", "username", "password", "rules", "required", "message", "trigger", "min", "created", "userStore", "restoreLoginState", "isAuthenticated", "console", "log", "$router", "push", "methods", "handleLogin", "$refs", "loginFormRef", "validate", "response", "mockLogin", "success", "$message", "user", "role", "id", "Date", "now", "avatar", "token", "login", "error", "Promise", "resolve", "setTimeout"], "sources": ["D:\\2025_down\\project\\shoppingOnline_2025_91\\shoppingOnline_front-2025-91\\src\\views\\LoginDemo.vue"], "sourcesContent": ["<template>\n  <div class=\"login-demo\">\n    <div class=\"login-container\">\n      <div class=\"login-card\">\n        <h2 class=\"login-title\">在线购物商城</h2>\n        <p class=\"login-subtitle\">请登录您的账户</p>\n        \n        <el-form \n          :model=\"loginForm\" \n          :rules=\"rules\" \n          ref=\"loginFormRef\"\n          class=\"login-form\"\n          @submit.prevent=\"handleLogin\"\n        >\n          <el-form-item prop=\"username\">\n            <el-input\n              v-model=\"loginForm.username\"\n              placeholder=\"请输入用户名\"\n              size=\"large\"\n              prefix-icon=\"User\"\n            />\n          </el-form-item>\n          \n          <el-form-item prop=\"password\">\n            <el-input\n              v-model=\"loginForm.password\"\n              type=\"password\"\n              placeholder=\"请输入密码\"\n              size=\"large\"\n              prefix-icon=\"Lock\"\n              show-password\n              @keyup.enter=\"handleLogin\"\n            />\n          </el-form-item>\n          \n          <el-form-item>\n            <el-button \n              type=\"primary\" \n              size=\"large\" \n              class=\"login-btn\"\n              :loading=\"loading\"\n              @click=\"handleLogin\"\n            >\n              {{ loading ? '登录中...' : '登录' }}\n            </el-button>\n          </el-form-item>\n        </el-form>\n        \n        <div class=\"demo-info\">\n          <el-alert\n            title=\"演示账户\"\n            type=\"info\"\n            :closable=\"false\"\n            show-icon\n          >\n            <template #default>\n              <p><strong>用户名:</strong> admin</p>\n              <p><strong>密码:</strong> 123456</p>\n            </template>\n          </el-alert>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { useUserStore } from '@/store/index'\nimport request from '@/utils/request'\n\nexport default {\n  name: 'LoginDemo',\n  \n  data() {\n    return {\n      loading: false,\n      loginForm: {\n        username: 'admin',\n        password: '123456'\n      },\n      rules: {\n        username: [\n          { required: true, message: '请输入用户名', trigger: 'blur' }\n        ],\n        password: [\n          { required: true, message: '请输入密码', trigger: 'blur' },\n          { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }\n        ]\n      }\n    }\n  },\n  \n  created() {\n    // 检查是否已经登录\n    const userStore = useUserStore();\n    userStore.restoreLoginState();\n    if (userStore.isAuthenticated) {\n      console.log('用户已登录，跳转到商品页');\n      this.$router.push('/goods');\n    }\n  },\n  \n  methods: {\n    async handleLogin() {\n      try {\n        // 表单验证\n        await this.$refs.loginFormRef.validate();\n        \n        this.loading = true;\n        \n        // 模拟登录请求\n        const response = await this.mockLogin();\n        \n        if (response.success) {\n          this.$message.success('登录成功！');\n          \n          // 保存用户信息\n          const userStore = useUserStore();\n          const user = {\n            username: this.loginForm.username,\n            role: 'user',\n            id: Date.now(),\n            avatar: ''\n          };\n          const token = 'demo-token-' + Date.now();\n          \n          userStore.login(user, token);\n          \n          // 跳转到商品页\n          this.$router.push('/goods');\n        } else {\n          this.$message.error(response.message || '登录失败');\n        }\n      } catch (error) {\n        console.error('登录错误:', error);\n        this.$message.error('登录失败，请重试');\n      } finally {\n        this.loading = false;\n      }\n    },\n    \n    // 模拟登录API\n    async mockLogin() {\n      return new Promise((resolve) => {\n        setTimeout(() => {\n          if (this.loginForm.username === 'admin' && this.loginForm.password === '123456') {\n            resolve({\n              success: true,\n              data: {\n                username: this.loginForm.username,\n                role: 'admin'\n              }\n            });\n          } else {\n            resolve({\n              success: false,\n              message: '用户名或密码错误'\n            });\n          }\n        }, 1000);\n      });\n    }\n  }\n}\n</script>\n\n<style scoped>\n.login-demo {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 20px;\n}\n\n.login-container {\n  width: 100%;\n  max-width: 400px;\n}\n\n.login-card {\n  background: white;\n  border-radius: 16px;\n  padding: 40px;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n}\n\n.login-title {\n  text-align: center;\n  font-size: 28px;\n  color: #333;\n  margin: 0 0 8px;\n  font-weight: bold;\n}\n\n.login-subtitle {\n  text-align: center;\n  color: #666;\n  margin: 0 0 30px;\n  font-size: 16px;\n}\n\n.login-form {\n  margin-bottom: 20px;\n}\n\n.login-btn {\n  width: 100%;\n  height: 48px;\n  font-size: 16px;\n  font-weight: bold;\n}\n\n.demo-info {\n  margin-top: 20px;\n}\n\n.demo-info p {\n  margin: 5px 0;\n  font-size: 14px;\n}\n\n/* 响应式设计 */\n@media (max-width: 480px) {\n  .login-card {\n    padding: 30px 20px;\n  }\n  \n  .login-title {\n    font-size: 24px;\n  }\n}\n</style>\n"], "mappings": ";AAmEA,SAASA,YAAW,QAAS,eAAc;AAC3C,OAAOC,OAAM,MAAO,iBAAgB;AAEpC,eAAe;EACbC,IAAI,EAAE,WAAW;EAEjBC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,OAAO,EAAE,KAAK;MACdC,SAAS,EAAE;QACTC,QAAQ,EAAE,OAAO;QACjBC,QAAQ,EAAE;MACZ,CAAC;MACDC,KAAK,EAAE;QACLF,QAAQ,EAAE,CACR;UAAEG,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,QAAQ;UAAEC,OAAO,EAAE;QAAO,EACtD;QACDJ,QAAQ,EAAE,CACR;UAAEE,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAO,CAAC,EACrD;UAAEC,GAAG,EAAE,CAAC;UAAEF,OAAO,EAAE,YAAY;UAAEC,OAAO,EAAE;QAAO;MAErD;IACF;EACF,CAAC;EAEDE,OAAOA,CAAA,EAAG;IACR;IACA,MAAMC,SAAQ,GAAId,YAAY,CAAC,CAAC;IAChCc,SAAS,CAACC,iBAAiB,CAAC,CAAC;IAC7B,IAAID,SAAS,CAACE,eAAe,EAAE;MAC7BC,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;MAC3B,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC,QAAQ,CAAC;IAC7B;EACF,CAAC;EAEDC,OAAO,EAAE;IACP,MAAMC,WAAWA,CAAA,EAAG;MAClB,IAAI;QACF;QACA,MAAM,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,QAAQ,CAAC,CAAC;QAExC,IAAI,CAACrB,OAAM,GAAI,IAAI;;QAEnB;QACA,MAAMsB,QAAO,GAAI,MAAM,IAAI,CAACC,SAAS,CAAC,CAAC;QAEvC,IAAID,QAAQ,CAACE,OAAO,EAAE;UACpB,IAAI,CAACC,QAAQ,CAACD,OAAO,CAAC,OAAO,CAAC;;UAE9B;UACA,MAAMd,SAAQ,GAAId,YAAY,CAAC,CAAC;UAChC,MAAM8B,IAAG,GAAI;YACXxB,QAAQ,EAAE,IAAI,CAACD,SAAS,CAACC,QAAQ;YACjCyB,IAAI,EAAE,MAAM;YACZC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;YACdC,MAAM,EAAE;UACV,CAAC;UACD,MAAMC,KAAI,GAAI,aAAY,GAAIH,IAAI,CAACC,GAAG,CAAC,CAAC;UAExCpB,SAAS,CAACuB,KAAK,CAACP,IAAI,EAAEM,KAAK,CAAC;;UAE5B;UACA,IAAI,CAACjB,OAAO,CAACC,IAAI,CAAC,QAAQ,CAAC;QAC7B,OAAO;UACL,IAAI,CAACS,QAAQ,CAACS,KAAK,CAACZ,QAAQ,CAAChB,OAAM,IAAK,MAAM,CAAC;QACjD;MACF,EAAE,OAAO4B,KAAK,EAAE;QACdrB,OAAO,CAACqB,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;QAC7B,IAAI,CAACT,QAAQ,CAACS,KAAK,CAAC,UAAU,CAAC;MACjC,UAAU;QACR,IAAI,CAAClC,OAAM,GAAI,KAAK;MACtB;IACF,CAAC;IAED;IACA,MAAMuB,SAASA,CAAA,EAAG;MAChB,OAAO,IAAIY,OAAO,CAAEC,OAAO,IAAK;QAC9BC,UAAU,CAAC,MAAM;UACf,IAAI,IAAI,CAACpC,SAAS,CAACC,QAAO,KAAM,OAAM,IAAK,IAAI,CAACD,SAAS,CAACE,QAAO,KAAM,QAAQ,EAAE;YAC/EiC,OAAO,CAAC;cACNZ,OAAO,EAAE,IAAI;cACbzB,IAAI,EAAE;gBACJG,QAAQ,EAAE,IAAI,CAACD,SAAS,CAACC,QAAQ;gBACjCyB,IAAI,EAAE;cACR;YACF,CAAC,CAAC;UACJ,OAAO;YACLS,OAAO,CAAC;cACNZ,OAAO,EAAE,KAAK;cACdlB,OAAO,EAAE;YACX,CAAC,CAAC;UACJ;QACF,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,CAAC;IACJ;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}