<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cn.mapper.CartMapper">
    <resultMap id="cartResultMap" type="hashmap">
        <result column="id" property="id"></result>
        <result column="user_id" property="userId"></result>
        <result column="good_id" property="goodId"></result>
        <result column="name" property="goodName"></result>
        <result column="imgs" property="img"></result>
        <result column="standard" property="standard"></result>
        <result column="store" property="store"></result>
        <result column="price" property="price"></result>
        <result column="discount" property="discount"></result>
        <result column="count" property="count"></result>
        <result column="create_time" property="createTime"></result>
    </resultMap>

    <select id="selectByUserId" resultMap="cartResultMap">
        SELECT cart.*,good_standard.price,good.imgs,`name`,discount,store
        FROM cart,good_standard,good
        WHERE cart.good_id = good.id AND cart.good_id = good_standard.good_id AND cart.standard = good_standard.`value`
              AND cart.user_id = #{userId}
        ORDER BY cart.create_time DESC

    </select>
</mapper>
