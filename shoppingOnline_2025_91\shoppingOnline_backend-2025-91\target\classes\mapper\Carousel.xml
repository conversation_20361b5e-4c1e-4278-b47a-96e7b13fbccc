<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cn.mapper.CarouselMapper">

    <select id="getAllCarousel" resultType="com.cn.entity.Carousel">
        select carousel.*,good.name as good_name,good.imgs as img from carousel,good where good.id = carousel.good_id order by show_order asc
    </select>
</mapper>
