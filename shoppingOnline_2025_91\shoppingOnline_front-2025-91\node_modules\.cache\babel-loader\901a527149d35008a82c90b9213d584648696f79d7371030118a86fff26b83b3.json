{"ast": null, "code": "import { openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1"], "sources": ["D:\\2025_down\\project\\shoppingOnline_2025_91\\shoppingOnline_front-2025-91\\src\\views\\Home.vue"], "sourcesContent": ["<!--  -->\r\n<template>\r\n  <div class=\"\"></div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'XxxView', // ✅ 推荐使用多词组件名\r\n\r\n  // 引入的组件需要注册\r\n  components: {},\r\n\r\n  // 数据区\r\n  data() {\r\n    return {\r\n      \r\n    };\r\n  },\r\n\r\n  // 计算属性\r\n  computed: {},\r\n\r\n  // 监听数据变化\r\n  watch: {},\r\n\r\n  // 方法集合\r\n  methods: {\r\n    \r\n  },\r\n\r\n  // 生命周期 - 创建完成\r\n  created() {\r\n    // \r\n  },\r\n\r\n  // 生命周期 - 挂载完成\r\n  mounted() {\r\n    // \r\n  },\r\n\r\n  // 生命周期 - 更新之前\r\n  beforeUpdate() {},\r\n\r\n  // 生命周期 - 更新之后\r\n  updated() {},\r\n\r\n  // 生命周期 - 卸载前\r\n  beforeUnmount() {\r\n    // 替代 beforeDestroy\r\n    // \r\n  },\r\n\r\n  // 生命周期 - 卸载后\r\n  unmounted() {\r\n    // 替代 destroyed\r\n    // \r\n  },\r\n\r\n  // keep-alive 缓存组件激活时触发\r\n  activated() {},\r\n\r\n  // keep-alive 缓存组件失活时触发\r\n  deactivated() {}\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n\r\n</style>"], "mappings": ";;EAEOA,KAAK,EAAC;AAAE;;uBAAbC,mBAAA,CAAoB,OAApBC,UAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}