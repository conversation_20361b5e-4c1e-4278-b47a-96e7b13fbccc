{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { Grid, Box } from '@element-plus/icons-vue';\nimport request from '@/utils/request.js';\nimport ProductDetail from '@/components/ProductDetail.vue';\nexport default {\n  name: 'CategoryView',\n  components: {\n    Grid,\n    Box,\n    ProductDetail\n  },\n  data() {\n    return {\n      loading: false,\n      categories: [],\n      selectedCategory: null,\n      selectedCategoryName: '',\n      goodsList: [],\n      showDetailDialog: false,\n      selectedProduct: {}\n    };\n  },\n  created() {\n    // 先获取分类数据，然后再获取商品数据，避免同时进行多个异步操作导致的闪烁\n    this.fetchCategories().then(() => {\n      this.fetchGoods();\n    });\n  },\n  methods: {\n    // 获取分类数据\n    async fetchCategories() {\n      try {\n        const response = await request.get('/categoryAPI');\n        if (Array.isArray(response)) {\n          this.categories = response;\n          console.log('获取分类数据成功:', this.categories);\n        } else {\n          // 使用默认分类\n          this.categories = this.getDefaultCategories();\n        }\n        return Promise.resolve();\n      } catch (error) {\n        console.error('获取分类数据失败:', error);\n        this.categories = this.getDefaultCategories();\n        return Promise.resolve();\n      }\n    },\n    // 获取默认分类数据\n    getDefaultCategories() {\n      return [{\n        id: 1,\n        name: '服装'\n      }, {\n        id: 2,\n        name: '鞋类'\n      }, {\n        id: 3,\n        name: '酒类'\n      }, {\n        id: 4,\n        name: '日用百货'\n      }, {\n        id: 5,\n        name: '家用电器'\n      }, {\n        id: 6,\n        name: '数码产品'\n      }];\n    },\n    // 选择分类\n    selectCategory(category) {\n      if (category === null) {\n        this.selectedCategory = null;\n        this.selectedCategoryName = '';\n      } else {\n        this.selectedCategory = category.id;\n        this.selectedCategoryName = category.name;\n      }\n      this.fetchGoods();\n    },\n    // 清除分类筛选\n    clearCategory() {\n      this.selectedCategory = null;\n      this.selectedCategoryName = '';\n      this.fetchGoods();\n    },\n    // 获取商品数据\n    async fetchGoods() {\n      try {\n        this.loading = true;\n        // 先清空商品列表，避免显示旧数据\n        this.goodsList = [];\n        let params = {};\n\n        // 如果有分类筛选，添加分类参数\n        if (this.selectedCategory) {\n          params.categoryId = this.selectedCategory;\n        }\n        const response = await request.get('/good', {\n          params\n        });\n        if (Array.isArray(response) && response.length > 0) {\n          // 使用后端数据，并为每个商品添加图片和确保描述存在\n          // 先存储数据，然后在nextTick中更新，减少闪烁\n          const processedGoods = response.map(item => {\n            // 为每个分类提供对应的图片（使用实际存在的图片）\n            const imageMap = {\n              1: '/images/women-top.jpg',\n              // 服装\n              2: '/images/casual-shoes.jpg',\n              // 鞋类\n              3: '/images/whiskey.jpg',\n              // 酒类\n              4: '/images/women-top.jpg',\n              // 日用百货（使用女上衣图片）\n              5: '/images/casual-shoes.jpg',\n              // 家用电器（使用休闲鞋图片）\n              6: '/images/whiskey.jpg' // 数码产品（使用威士忌图片）\n            };\n\n            // 根据商品名称匹配图片\n            let productImage = item.imgs;\n            if (!productImage) {\n              // 根据商品名称匹配对应图片\n              if (item.name && item.name.includes('女上衣')) {\n                productImage = '/images/women-top.jpg';\n              } else if (item.name && item.name.includes('休闲鞋')) {\n                productImage = '/images/casual-shoes.jpg';\n              } else if (item.name && item.name.includes('威士忌')) {\n                productImage = '/images/whiskey.jpg';\n              } else {\n                // 使用分类对应的图片，或者循环使用\n                productImage = imageMap[item.categoryId] || imageMap[index % 3 + 1];\n              }\n            }\n            return {\n              ...item,\n              imgs: productImage,\n              description: item.description || this.getDefaultDescription(item.name),\n              // 使用默认描述\n              price: item.price || (Math.random() * 200 + 50).toFixed(2) // 确保有价格\n            };\n          });\n\n          // 直接设置数据，不使用nextTick避免额外的延迟\n          this.goodsList = processedGoods;\n          console.log('分类页面获取商品数据成功:', this.goodsList);\n        } else {\n          // 使用模拟数据\n          const mockGoods = this.getMockProducts();\n          this.$nextTick(() => {\n            this.goodsList = mockGoods;\n          });\n        }\n      } catch (error) {\n        console.error('获取商品数据失败:', error);\n        const mockGoods = this.getMockProducts();\n        this.$nextTick(() => {\n          this.goodsList = mockGoods;\n        });\n      } finally {\n        // 延迟关闭loading状态，确保数据已经渲染完成\n        setTimeout(() => {\n          this.loading = false;\n        }, 100);\n      }\n    },\n    // 获取默认商品描述\n    getDefaultDescription(productName) {\n      const descriptions = {\n        '女上衣': '时尚女性上衣，舒适面料，多种颜色可选，适合日常穿着和商务场合，版型修身显瘦',\n        '休闲鞋': '舒适透气的休闲运动鞋，适合日常穿着，防滑耐磨，多种尺码可选，时尚百搭',\n        '威士忌 大瓶': '优质威士忌，口感醇厚，适合收藏和品鉴，酒精度40%，容量750ml，包装精美',\n        '墨镜': '时尚太阳镜，UV400防护，轻量化设计，多种款式可选',\n        '桌椅套装': '实木桌椅套装，环保材质，结实耐用，适合家庭使用',\n        '儿童简笔画册': '儿童启蒙绘画册，内容丰富，培养孩子创造力和想象力',\n        '英文版图书': '原版英文图书，提高英语阅读能力，内容精彩有趣',\n        '衬衫': '商务休闲衬衫，免烫面料，版型合身，适合各种场合'\n      };\n      return descriptions[productName] || '优质商品，品质保证，欢迎选购';\n    },\n    // 获取模拟商品数据\n    getMockProducts() {\n      // 为每个分类添加足够的模拟商品\n      const allProducts = [\n      // 服装分类\n      {\n        id: 1,\n        name: '女上衣',\n        description: '时尚女性上衣，舒适面料，多种颜色可选，适合日常穿着和商务场合',\n        imgs: '/images/women-top.jpg',\n        price: 102.00,\n        categoryId: 1\n      }, {\n        id: 8,\n        name: '男士衬衫',\n        description: '优质棉质男士衬衫，修身版型，多种颜色可选，适合商务和休闲场合',\n        imgs: '/images/women-top.jpg',\n        price: 129.00,\n        categoryId: 1\n      }, {\n        id: 9,\n        name: '牛仔裤',\n        description: '经典款式牛仔裤，弹力面料，舒适耐穿，多尺码可选',\n        imgs: '/images/women-top.jpg',\n        price: 159.00,\n        categoryId: 1\n      },\n      // 鞋类分类\n      {\n        id: 2,\n        name: '休闲鞋',\n        description: '舒适透气的休闲运动鞋，适合日常穿着，防滑耐磨，多种尺码可选',\n        imgs: '/images/casual-shoes.jpg',\n        price: 162.00,\n        categoryId: 2\n      }, {\n        id: 10,\n        name: '正装皮鞋',\n        description: '头层牛皮正装皮鞋，舒适透气，经典款式，适合商务场合',\n        imgs: '/images/casual-shoes.jpg',\n        price: 399.00,\n        categoryId: 2\n      }, {\n        id: 11,\n        name: '运动鞋',\n        description: '专业跑步鞋，缓震设计，透气网面，提供舒适的运动体验',\n        imgs: '/images/casual-shoes.jpg',\n        price: 499.00,\n        categoryId: 2\n      },\n      // 酒类分类\n      {\n        id: 3,\n        name: '威士忌 大瓶',\n        description: '优质威士忌，口感醇厚，适合收藏和品鉴，酒精度40%，容量750ml',\n        imgs: '/images/whiskey.jpg',\n        price: 427.50,\n        categoryId: 3\n      }, {\n        id: 12,\n        name: '红酒',\n        description: '法国进口红酒，醇厚果香，单宁柔和，适合搭配牛排等红肉',\n        imgs: '/images/whiskey.jpg',\n        price: 298.00,\n        categoryId: 3\n      }, {\n        id: 13,\n        name: '啤酒',\n        description: '精酿啤酒，原麦汁浓度高，口感醇厚，泡沫丰富',\n        imgs: '/images/whiskey.jpg',\n        price: 88.00,\n        categoryId: 3\n      },\n      // 日用百货分类\n      {\n        id: 14,\n        name: '毛巾套装',\n        description: '纯棉毛巾套装，柔软吸水，不掉毛，多色可选',\n        imgs: '/images/women-top.jpg',\n        price: 59.90,\n        categoryId: 4\n      }, {\n        id: 15,\n        name: '洗发水',\n        description: '去屑控油洗发水，天然成分，温和不刺激，适合各种发质',\n        imgs: '/images/women-top.jpg',\n        price: 89.00,\n        categoryId: 4\n      }, {\n        id: 16,\n        name: '牙膏',\n        description: '美白牙膏，含氟配方，有效防蛀，清新口气',\n        imgs: '/images/women-top.jpg',\n        price: 29.90,\n        categoryId: 4\n      },\n      // 家用电器分类\n      {\n        id: 17,\n        name: '电饭煲',\n        description: '智能电饭煲，多种烹饪模式，大容量，不粘内胆',\n        imgs: '/images/women-top.jpg',\n        price: 399.00,\n        categoryId: 5\n      }, {\n        id: 18,\n        name: '微波炉',\n        description: '家用微波炉，多种功能，智能控制，操作简便',\n        imgs: '/images/women-top.jpg',\n        price: 599.00,\n        categoryId: 5\n      }, {\n        id: 19,\n        name: '电风扇',\n        description: '落地电风扇，静音设计，多档风速，广角送风',\n        imgs: '/images/women-top.jpg',\n        price: 199.00,\n        categoryId: 5\n      },\n      // 数码产品分类\n      {\n        id: 20,\n        name: '智能手机',\n        description: '新款智能手机，全面屏设计，高性能处理器，拍照清晰',\n        imgs: '/images/women-top.jpg',\n        price: 2999.00,\n        categoryId: 6\n      }, {\n        id: 21,\n        name: '无线耳机',\n        description: '真无线蓝牙耳机，主动降噪，长续航，音质出色',\n        imgs: '/images/women-top.jpg',\n        price: 799.00,\n        categoryId: 6\n      }, {\n        id: 22,\n        name: '智能手表',\n        description: '多功能智能手表，健康监测，运动追踪，防水设计',\n        imgs: '/images/women-top.jpg',\n        price: 1299.00,\n        categoryId: 6\n      }];\n\n      // 根据选中的分类筛选商品\n      if (this.selectedCategory) {\n        return allProducts.filter(product => product.categoryId === this.selectedCategory);\n      }\n      return allProducts;\n    },\n    // 显示商品详情\n    showProductDetail(good) {\n      this.selectedProduct = good;\n      this.showDetailDialog = true;\n    },\n    // 添加到购物车\n    addToCart(good) {\n      console.log('加入购物车:', good.name);\n      this.$message.success(`已加入购物车：${good.name}`);\n    },\n    // 立即购买\n    buyNow(good) {\n      console.log('立即购买:', good.name);\n      this.$message.info(`立即购买：${good.name}`);\n    },\n    // 图片加载错误处理\n    handleImageError(event) {\n      event.target.src = '/images/women-top.jpg';\n    },\n    // 跳转到商品页面\n    goToGoodsPage() {\n      if (this.selectedCategory) {\n        this.$router.push({\n          path: '/goods',\n          query: {\n            categoryId: this.selectedCategory\n          }\n        });\n      } else {\n        this.$router.push('/goods');\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["Grid", "Box", "request", "ProductDetail", "name", "components", "data", "loading", "categories", "selectedCate<PERSON><PERSON>", "selectedCategoryName", "goodsList", "showDetailDialog", "selectedProduct", "created", "fetchCategories", "then", "fetchGoods", "methods", "response", "get", "Array", "isArray", "console", "log", "getDefaultCategories", "Promise", "resolve", "error", "id", "selectCategory", "category", "clearCategory", "params", "categoryId", "length", "processedGoods", "map", "item", "imageMap", "productImage", "imgs", "includes", "index", "description", "getDefaultDescription", "price", "Math", "random", "toFixed", "mockGoods", "getMockProducts", "$nextTick", "setTimeout", "productName", "descriptions", "allProducts", "filter", "product", "showProductDetail", "good", "addToCart", "$message", "success", "buyNow", "info", "handleImageError", "event", "target", "src", "goToGoodsPage", "$router", "push", "path", "query"], "sources": ["D:\\2025_down\\project\\shoppingOnline_2025_91\\shoppingOnline_front-2025-91\\src\\views\\Category.vue"], "sourcesContent": ["<template>\n  <div class=\"category-container\">\n    <!-- 页面标题 -->\n    <h1 class=\"page-title\">商品分类</h1>\n\n    <!-- 分类列表 -->\n    <div class=\"category-section\">\n      <el-card class=\"category-card\">\n        <div class=\"category-header\">\n          <h2>选择分类</h2>\n          <el-button \n            v-if=\"selectedCategory\" \n            type=\"info\" \n            size=\"small\" \n            @click=\"clearCategory\"\n          >\n            清除筛选\n          </el-button>\n        </div>\n        \n        <div class=\"category-list\">\n          <el-button\n            :class=\"['category-btn', { 'active': selectedCategory === null }]\"\n            @click=\"selectCategory(null)\"\n            size=\"large\"\n          >\n            <el-icon><Grid /></el-icon>\n            全部商品\n          </el-button>\n          \n          <el-button\n            v-for=\"category in categories\"\n            :key=\"category.id\"\n            :class=\"['category-btn', { 'active': selectedCategory === category.id }]\"\n            @click=\"selectCategory(category)\"\n            size=\"large\"\n          >\n            <el-icon><Box /></el-icon>\n            {{ category.name }}\n          </el-button>\n        </div>\n      </el-card>\n    </div>\n\n    <!-- 当前分类信息 -->\n    <div v-if=\"selectedCategory\" class=\"current-category\">\n      <el-alert\n        :title=\"`当前分类：${selectedCategoryName}`\"\n        type=\"info\"\n        :closable=\"false\"\n        show-icon\n      />\n    </div>\n\n    <!-- 加载状态 -->\n    <div v-if=\"loading\" class=\"loading\">\n      <el-skeleton :rows=\"3\" animated />\n    </div>\n\n    <!-- 商品列表 -->\n    <div v-else-if=\"goodsList.length > 0\" class=\"goods-section\">\n      <div class=\"goods-header\">\n        <h3>{{ selectedCategoryName || '全部' }}商品 ({{ goodsList.length }})</h3>\n        <el-button type=\"primary\" @click=\"goToGoodsPage\">查看更多</el-button>\n      </div>\n      \n      <div class=\"goods-list\" :class=\"{ 'loading-transition': loading }\">\n        <div\n          class=\"goods-card\"\n          v-for=\"good in goodsList\"\n          :key=\"good.id\"\n          @click=\"showProductDetail(good)\"\n        >\n          <img\n            :src=\"good.imgs\"\n            :alt=\"good.name\"\n            class=\"goods-image\"\n            @error=\"handleImageError\"\n          />\n          <div class=\"goods-info\">\n            <h4 class=\"goods-name\">{{ good.name }}</h4>\n            <p class=\"goods-desc\">{{ good.description || '暂无商品描述' }}</p>\n            <p class=\"goods-price\">¥{{ good.price }}</p>\n            <div class=\"goods-actions\">\n              <el-button \n                type=\"primary\" \n                size=\"small\" \n                @click.stop=\"addToCart(good)\"\n              >\n                加入购物车\n              </el-button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 无数据 -->\n    <div v-else class=\"no-data\">\n      <el-empty description=\"该分类暂无商品\" />\n    </div>\n\n    <!-- 商品详情弹窗 -->\n    <ProductDetail\n      v-model=\"showDetailDialog\"\n      :product=\"selectedProduct\"\n      @add-to-cart=\"addToCart\"\n      @buy-now=\"buyNow\"\n    />\n  </div>\n</template>\n\n<script>\nimport { Grid, Box } from '@element-plus/icons-vue'\nimport request from '@/utils/request.js'\nimport ProductDetail from '@/components/ProductDetail.vue'\n\nexport default {\n  name: 'CategoryView',\n  \n  components: {\n    Grid,\n    Box,\n    ProductDetail\n  },\n  \n  data() {\n    return {\n      loading: false,\n      categories: [],\n      selectedCategory: null,\n      selectedCategoryName: '',\n      goodsList: [],\n      showDetailDialog: false,\n      selectedProduct: {}\n    }\n  },\n  \n  created() {\n    // 先获取分类数据，然后再获取商品数据，避免同时进行多个异步操作导致的闪烁\n    this.fetchCategories().then(() => {\n      this.fetchGoods()\n    })\n  },\n  \n  methods: {\n    // 获取分类数据\n    async fetchCategories() {\n      try {\n        const response = await request.get('/categoryAPI')\n        \n        if (Array.isArray(response)) {\n          this.categories = response\n          console.log('获取分类数据成功:', this.categories)\n        } else {\n          // 使用默认分类\n          this.categories = this.getDefaultCategories()\n        }\n        return Promise.resolve()\n      } catch (error) {\n        console.error('获取分类数据失败:', error)\n        this.categories = this.getDefaultCategories()\n        return Promise.resolve()\n      }\n    },\n    \n    // 获取默认分类数据\n    getDefaultCategories() {\n      return [\n        { id: 1, name: '服装' },\n        { id: 2, name: '鞋类' },\n        { id: 3, name: '酒类' },\n        { id: 4, name: '日用百货' },\n        { id: 5, name: '家用电器' },\n        { id: 6, name: '数码产品' }\n      ]\n    },\n    \n    // 选择分类\n    selectCategory(category) {\n      if (category === null) {\n        this.selectedCategory = null\n        this.selectedCategoryName = ''\n      } else {\n        this.selectedCategory = category.id\n        this.selectedCategoryName = category.name\n      }\n      this.fetchGoods()\n    },\n    \n    // 清除分类筛选\n    clearCategory() {\n      this.selectedCategory = null\n      this.selectedCategoryName = ''\n      this.fetchGoods()\n    },\n    \n    // 获取商品数据\n    async fetchGoods() {\n      try {\n        this.loading = true\n        // 先清空商品列表，避免显示旧数据\n        this.goodsList = []\n\n        let params = {}\n\n        // 如果有分类筛选，添加分类参数\n        if (this.selectedCategory) {\n          params.categoryId = this.selectedCategory\n        }\n\n        const response = await request.get('/good', { params })\n        \n        if (Array.isArray(response) && response.length > 0) {\n          // 使用后端数据，并为每个商品添加图片和确保描述存在\n          // 先存储数据，然后在nextTick中更新，减少闪烁\n          const processedGoods = response.map((item) => {\n            // 为每个分类提供对应的图片（使用实际存在的图片）\n            const imageMap = {\n              1: '/images/women-top.jpg', // 服装\n              2: '/images/casual-shoes.jpg', // 鞋类\n              3: '/images/whiskey.jpg', // 酒类\n              4: '/images/women-top.jpg', // 日用百货（使用女上衣图片）\n              5: '/images/casual-shoes.jpg', // 家用电器（使用休闲鞋图片）\n              6: '/images/whiskey.jpg' // 数码产品（使用威士忌图片）\n            }\n\n            // 根据商品名称匹配图片\n            let productImage = item.imgs\n            if (!productImage) {\n              // 根据商品名称匹配对应图片\n              if (item.name && item.name.includes('女上衣')) {\n                productImage = '/images/women-top.jpg'\n              } else if (item.name && item.name.includes('休闲鞋')) {\n                productImage = '/images/casual-shoes.jpg'\n              } else if (item.name && item.name.includes('威士忌')) {\n                productImage = '/images/whiskey.jpg'\n              } else {\n                // 使用分类对应的图片，或者循环使用\n                productImage = imageMap[item.categoryId] || imageMap[((index % 3) + 1)]\n              }\n            }\n            return {\n              ...item,\n              imgs: productImage,\n              description: item.description || this.getDefaultDescription(item.name), // 使用默认描述\n              price: item.price || (Math.random() * 200 + 50).toFixed(2) // 确保有价格\n            }\n          })\n          \n          // 直接设置数据，不使用nextTick避免额外的延迟\n          this.goodsList = processedGoods\n          console.log('分类页面获取商品数据成功:', this.goodsList)\n        } else {\n          // 使用模拟数据\n          const mockGoods = this.getMockProducts()\n          this.$nextTick(() => {\n            this.goodsList = mockGoods\n          })\n        }\n      } catch (error) {\n        console.error('获取商品数据失败:', error)\n        const mockGoods = this.getMockProducts()\n        this.$nextTick(() => {\n          this.goodsList = mockGoods\n        })\n      } finally {\n        // 延迟关闭loading状态，确保数据已经渲染完成\n        setTimeout(() => {\n          this.loading = false\n        }, 100)\n      }\n    },\n    \n    // 获取默认商品描述\n    getDefaultDescription(productName) {\n      const descriptions = {\n        '女上衣': '时尚女性上衣，舒适面料，多种颜色可选，适合日常穿着和商务场合，版型修身显瘦',\n        '休闲鞋': '舒适透气的休闲运动鞋，适合日常穿着，防滑耐磨，多种尺码可选，时尚百搭',\n        '威士忌 大瓶': '优质威士忌，口感醇厚，适合收藏和品鉴，酒精度40%，容量750ml，包装精美',\n        '墨镜': '时尚太阳镜，UV400防护，轻量化设计，多种款式可选',\n        '桌椅套装': '实木桌椅套装，环保材质，结实耐用，适合家庭使用',\n        '儿童简笔画册': '儿童启蒙绘画册，内容丰富，培养孩子创造力和想象力',\n        '英文版图书': '原版英文图书，提高英语阅读能力，内容精彩有趣',\n        '衬衫': '商务休闲衬衫，免烫面料，版型合身，适合各种场合'\n      }\n      return descriptions[productName] || '优质商品，品质保证，欢迎选购'\n    },\n\n    // 获取模拟商品数据\n    getMockProducts() {\n      // 为每个分类添加足够的模拟商品\n      const allProducts = [\n        // 服装分类\n        {\n          id: 1,\n          name: '女上衣',\n          description: '时尚女性上衣，舒适面料，多种颜色可选，适合日常穿着和商务场合',\n          imgs: '/images/women-top.jpg',\n          price: 102.00,\n          categoryId: 1\n        },\n        {\n          id: 8,\n          name: '男士衬衫',\n          description: '优质棉质男士衬衫，修身版型，多种颜色可选，适合商务和休闲场合',\n          imgs: '/images/women-top.jpg',\n          price: 129.00,\n          categoryId: 1\n        },\n        {\n          id: 9,\n          name: '牛仔裤',\n          description: '经典款式牛仔裤，弹力面料，舒适耐穿，多尺码可选',\n          imgs: '/images/women-top.jpg',\n          price: 159.00,\n          categoryId: 1\n        },\n        \n        // 鞋类分类\n        {\n          id: 2,\n          name: '休闲鞋',\n          description: '舒适透气的休闲运动鞋，适合日常穿着，防滑耐磨，多种尺码可选',\n          imgs: '/images/casual-shoes.jpg',\n          price: 162.00,\n          categoryId: 2\n        },\n        {\n          id: 10,\n          name: '正装皮鞋',\n          description: '头层牛皮正装皮鞋，舒适透气，经典款式，适合商务场合',\n          imgs: '/images/casual-shoes.jpg',\n          price: 399.00,\n          categoryId: 2\n        },\n        {\n          id: 11,\n          name: '运动鞋',\n          description: '专业跑步鞋，缓震设计，透气网面，提供舒适的运动体验',\n          imgs: '/images/casual-shoes.jpg',\n          price: 499.00,\n          categoryId: 2\n        },\n        \n        // 酒类分类\n        {\n          id: 3,\n          name: '威士忌 大瓶',\n          description: '优质威士忌，口感醇厚，适合收藏和品鉴，酒精度40%，容量750ml',\n          imgs: '/images/whiskey.jpg',\n          price: 427.50,\n          categoryId: 3\n        },\n        {\n          id: 12,\n          name: '红酒',\n          description: '法国进口红酒，醇厚果香，单宁柔和，适合搭配牛排等红肉',\n          imgs: '/images/whiskey.jpg',\n          price: 298.00,\n          categoryId: 3\n        },\n        {\n          id: 13,\n          name: '啤酒',\n          description: '精酿啤酒，原麦汁浓度高，口感醇厚，泡沫丰富',\n          imgs: '/images/whiskey.jpg',\n          price: 88.00,\n          categoryId: 3\n        },\n        \n        // 日用百货分类\n        {\n          id: 14,\n          name: '毛巾套装',\n          description: '纯棉毛巾套装，柔软吸水，不掉毛，多色可选',\n          imgs: '/images/women-top.jpg',\n          price: 59.90,\n          categoryId: 4\n        },\n        {\n          id: 15,\n          name: '洗发水',\n          description: '去屑控油洗发水，天然成分，温和不刺激，适合各种发质',\n          imgs: '/images/women-top.jpg',\n          price: 89.00,\n          categoryId: 4\n        },\n        {\n          id: 16,\n          name: '牙膏',\n          description: '美白牙膏，含氟配方，有效防蛀，清新口气',\n          imgs: '/images/women-top.jpg',\n          price: 29.90,\n          categoryId: 4\n        },\n        \n        // 家用电器分类\n        {\n          id: 17,\n          name: '电饭煲',\n          description: '智能电饭煲，多种烹饪模式，大容量，不粘内胆',\n          imgs: '/images/women-top.jpg',\n          price: 399.00,\n          categoryId: 5\n        },\n        {\n          id: 18,\n          name: '微波炉',\n          description: '家用微波炉，多种功能，智能控制，操作简便',\n          imgs: '/images/women-top.jpg',\n          price: 599.00,\n          categoryId: 5\n        },\n        {\n          id: 19,\n          name: '电风扇',\n          description: '落地电风扇，静音设计，多档风速，广角送风',\n          imgs: '/images/women-top.jpg',\n          price: 199.00,\n          categoryId: 5\n        },\n        \n        // 数码产品分类\n        {\n          id: 20,\n          name: '智能手机',\n          description: '新款智能手机，全面屏设计，高性能处理器，拍照清晰',\n          imgs: '/images/women-top.jpg',\n          price: 2999.00,\n          categoryId: 6\n        },\n        {\n          id: 21,\n          name: '无线耳机',\n          description: '真无线蓝牙耳机，主动降噪，长续航，音质出色',\n          imgs: '/images/women-top.jpg',\n          price: 799.00,\n          categoryId: 6\n        },\n        {\n          id: 22,\n          name: '智能手表',\n          description: '多功能智能手表，健康监测，运动追踪，防水设计',\n          imgs: '/images/women-top.jpg',\n          price: 1299.00,\n          categoryId: 6\n        }\n      ]\n      \n      // 根据选中的分类筛选商品\n      if (this.selectedCategory) {\n        return allProducts.filter(product => product.categoryId === this.selectedCategory)\n      }\n      \n      return allProducts\n    },\n    \n    // 显示商品详情\n    showProductDetail(good) {\n      this.selectedProduct = good\n      this.showDetailDialog = true\n    },\n    \n    // 添加到购物车\n    addToCart(good) {\n      console.log('加入购物车:', good.name)\n      this.$message.success(`已加入购物车：${good.name}`)\n    },\n    \n    // 立即购买\n    buyNow(good) {\n      console.log('立即购买:', good.name)\n      this.$message.info(`立即购买：${good.name}`)\n    },\n    \n    // 图片加载错误处理\n    handleImageError(event) {\n      event.target.src = '/images/women-top.jpg'\n    },\n    \n    // 跳转到商品页面\n    goToGoodsPage() {\n      if (this.selectedCategory) {\n        this.$router.push({\n          path: '/goods',\n          query: { categoryId: this.selectedCategory }\n        })\n      } else {\n        this.$router.push('/goods')\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.category-container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 20px;\n}\n\n.page-title {\n  text-align: center;\n  color: #333;\n  margin-bottom: 30px;\n  font-size: 28px;\n}\n\n.category-section {\n  margin-bottom: 30px;\n}\n\n.category-card {\n  border-radius: 12px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n}\n\n.category-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.category-header h2 {\n  margin: 0;\n  color: #333;\n}\n\n.category-list {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\n  gap: 15px;\n}\n\n.category-btn {\n  height: 60px;\n  border-radius: 8px;\n  border: 2px solid #e0e0e0;\n  background: white;\n  transition: all 0.3s ease;\n}\n\n.category-btn:hover {\n  border-color: #409eff;\n  color: #409eff;\n}\n\n.category-btn.active {\n  border-color: #409eff;\n  background: #409eff;\n  color: white;\n}\n\n.current-category {\n  margin-bottom: 20px;\n}\n\n/* 加载状态优化 */\n.loading {\n  padding: 40px 0;\n  /* 添加最小高度，避免布局跳动 */\n  min-height: 400px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.goods-section {\n  margin-top: 20px;\n}\n\n.goods-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.goods-header h3 {\n  margin: 0;\n  color: #333;\n}\n\n.goods-list {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\n  gap: 20px;\n  /* 添加过渡效果 */\n  transition: all 0.3s ease-in-out;\n  opacity: 1;\n  /* 添加最小高度，避免布局跳动 */\n  min-height: 400px;\n}\n\n/* 数据加载时的过渡效果 */\n.goods-list.loading-transition {\n  opacity: 0.7;\n  transform: scale(0.98);\n}\n\n.goods-card {\n  border: 1px solid #e0e0e0;\n  border-radius: 12px;\n  overflow: hidden;\n  background: white;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  transition: all 0.3s ease;\n  cursor: pointer;\n  /* 添加动画效果 */\n  animation: fadeIn 0.6s ease-out;\n  /* 添加will-change优化性能 */\n  will-change: transform, opacity;\n}\n\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n.goods-card:hover {\n  transform: translateY(-4px);\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);\n}\n\n.goods-image {\n  width: 100%;\n  height: 200px;\n  object-fit: cover;\n}\n\n.goods-info {\n  padding: 15px;\n}\n\n.goods-name {\n  font-size: 16px;\n  font-weight: bold;\n  margin: 0 0 8px;\n  color: #333;\n}\n\n.goods-desc {\n  font-size: 14px;\n  color: #666;\n  margin: 0 0 10px;\n  line-height: 1.4;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n}\n\n.goods-price {\n  font-size: 18px;\n  font-weight: bold;\n  color: #e60000;\n  margin: 0 0 12px;\n}\n\n.goods-actions {\n  text-align: center;\n}\n\n/* 无数据状态优化 */\n.no-data {\n  text-align: center;\n  padding: 60px 0;\n  /* 添加最小高度，避免布局跳动 */\n  min-height: 400px;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .category-list {\n    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));\n    gap: 10px;\n  }\n  \n  .category-btn {\n    height: 50px;\n    font-size: 14px;\n  }\n  \n  .goods-list {\n    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\n    gap: 15px;\n  }\n}\n</style>\n"], "mappings": ";;;AAiHA,SAASA,IAAI,EAAEC,GAAE,QAAS,yBAAwB;AAClD,OAAOC,OAAM,MAAO,oBAAmB;AACvC,OAAOC,aAAY,MAAO,gCAA+B;AAEzD,eAAe;EACbC,IAAI,EAAE,cAAc;EAEpBC,UAAU,EAAE;IACVL,IAAI;IACJC,GAAG;IACHE;EACF,CAAC;EAEDG,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,OAAO,EAAE,KAAK;MACdC,UAAU,EAAE,EAAE;MACdC,gBAAgB,EAAE,IAAI;MACtBC,oBAAoB,EAAE,EAAE;MACxBC,SAAS,EAAE,EAAE;MACbC,gBAAgB,EAAE,KAAK;MACvBC,eAAe,EAAE,CAAC;IACpB;EACF,CAAC;EAEDC,OAAOA,CAAA,EAAG;IACR;IACA,IAAI,CAACC,eAAe,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;MAChC,IAAI,CAACC,UAAU,CAAC;IAClB,CAAC;EACH,CAAC;EAEDC,OAAO,EAAE;IACP;IACA,MAAMH,eAAeA,CAAA,EAAG;MACtB,IAAI;QACF,MAAMI,QAAO,GAAI,MAAMjB,OAAO,CAACkB,GAAG,CAAC,cAAc;QAEjD,IAAIC,KAAK,CAACC,OAAO,CAACH,QAAQ,CAAC,EAAE;UAC3B,IAAI,CAACX,UAAS,GAAIW,QAAO;UACzBI,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE,IAAI,CAAChB,UAAU;QAC1C,OAAO;UACL;UACA,IAAI,CAACA,UAAS,GAAI,IAAI,CAACiB,oBAAoB,CAAC;QAC9C;QACA,OAAOC,OAAO,CAACC,OAAO,CAAC;MACzB,EAAE,OAAOC,KAAK,EAAE;QACdL,OAAO,CAACK,KAAK,CAAC,WAAW,EAAEA,KAAK;QAChC,IAAI,CAACpB,UAAS,GAAI,IAAI,CAACiB,oBAAoB,CAAC;QAC5C,OAAOC,OAAO,CAACC,OAAO,CAAC;MACzB;IACF,CAAC;IAED;IACAF,oBAAoBA,CAAA,EAAG;MACrB,OAAO,CACL;QAAEI,EAAE,EAAE,CAAC;QAAEzB,IAAI,EAAE;MAAK,CAAC,EACrB;QAAEyB,EAAE,EAAE,CAAC;QAAEzB,IAAI,EAAE;MAAK,CAAC,EACrB;QAAEyB,EAAE,EAAE,CAAC;QAAEzB,IAAI,EAAE;MAAK,CAAC,EACrB;QAAEyB,EAAE,EAAE,CAAC;QAAEzB,IAAI,EAAE;MAAO,CAAC,EACvB;QAAEyB,EAAE,EAAE,CAAC;QAAEzB,IAAI,EAAE;MAAO,CAAC,EACvB;QAAEyB,EAAE,EAAE,CAAC;QAAEzB,IAAI,EAAE;MAAO,EACxB;IACF,CAAC;IAED;IACA0B,cAAcA,CAACC,QAAQ,EAAE;MACvB,IAAIA,QAAO,KAAM,IAAI,EAAE;QACrB,IAAI,CAACtB,gBAAe,GAAI,IAAG;QAC3B,IAAI,CAACC,oBAAmB,GAAI,EAAC;MAC/B,OAAO;QACL,IAAI,CAACD,gBAAe,GAAIsB,QAAQ,CAACF,EAAC;QAClC,IAAI,CAACnB,oBAAmB,GAAIqB,QAAQ,CAAC3B,IAAG;MAC1C;MACA,IAAI,CAACa,UAAU,CAAC;IAClB,CAAC;IAED;IACAe,aAAaA,CAAA,EAAG;MACd,IAAI,CAACvB,gBAAe,GAAI,IAAG;MAC3B,IAAI,CAACC,oBAAmB,GAAI,EAAC;MAC7B,IAAI,CAACO,UAAU,CAAC;IAClB,CAAC;IAED;IACA,MAAMA,UAAUA,CAAA,EAAG;MACjB,IAAI;QACF,IAAI,CAACV,OAAM,GAAI,IAAG;QAClB;QACA,IAAI,CAACI,SAAQ,GAAI,EAAC;QAElB,IAAIsB,MAAK,GAAI,CAAC;;QAEd;QACA,IAAI,IAAI,CAACxB,gBAAgB,EAAE;UACzBwB,MAAM,CAACC,UAAS,GAAI,IAAI,CAACzB,gBAAe;QAC1C;QAEA,MAAMU,QAAO,GAAI,MAAMjB,OAAO,CAACkB,GAAG,CAAC,OAAO,EAAE;UAAEa;QAAO,CAAC;QAEtD,IAAIZ,KAAK,CAACC,OAAO,CAACH,QAAQ,KAAKA,QAAQ,CAACgB,MAAK,GAAI,CAAC,EAAE;UAClD;UACA;UACA,MAAMC,cAAa,GAAIjB,QAAQ,CAACkB,GAAG,CAAEC,IAAI,IAAK;YAC5C;YACA,MAAMC,QAAO,GAAI;cACf,CAAC,EAAE,uBAAuB;cAAE;cAC5B,CAAC,EAAE,0BAA0B;cAAE;cAC/B,CAAC,EAAE,qBAAqB;cAAE;cAC1B,CAAC,EAAE,uBAAuB;cAAE;cAC5B,CAAC,EAAE,0BAA0B;cAAE;cAC/B,CAAC,EAAE,qBAAoB,CAAE;YAC3B;;YAEA;YACA,IAAIC,YAAW,GAAIF,IAAI,CAACG,IAAG;YAC3B,IAAI,CAACD,YAAY,EAAE;cACjB;cACA,IAAIF,IAAI,CAAClC,IAAG,IAAKkC,IAAI,CAAClC,IAAI,CAACsC,QAAQ,CAAC,KAAK,CAAC,EAAE;gBAC1CF,YAAW,GAAI,uBAAsB;cACvC,OAAO,IAAIF,IAAI,CAAClC,IAAG,IAAKkC,IAAI,CAAClC,IAAI,CAACsC,QAAQ,CAAC,KAAK,CAAC,EAAE;gBACjDF,YAAW,GAAI,0BAAyB;cAC1C,OAAO,IAAIF,IAAI,CAAClC,IAAG,IAAKkC,IAAI,CAAClC,IAAI,CAACsC,QAAQ,CAAC,KAAK,CAAC,EAAE;gBACjDF,YAAW,GAAI,qBAAoB;cACrC,OAAO;gBACL;gBACAA,YAAW,GAAID,QAAQ,CAACD,IAAI,CAACJ,UAAU,KAAKK,QAAQ,CAAGI,KAAI,GAAI,CAAC,GAAI,CAAC,CAAC;cACxE;YACF;YACA,OAAO;cACL,GAAGL,IAAI;cACPG,IAAI,EAAED,YAAY;cAClBI,WAAW,EAAEN,IAAI,CAACM,WAAU,IAAK,IAAI,CAACC,qBAAqB,CAACP,IAAI,CAAClC,IAAI,CAAC;cAAE;cACxE0C,KAAK,EAAER,IAAI,CAACQ,KAAI,IAAK,CAACC,IAAI,CAACC,MAAM,CAAC,IAAI,GAAE,GAAI,EAAE,EAAEC,OAAO,CAAC,CAAC,EAAE;YAC7D;UACF,CAAC;;UAED;UACA,IAAI,CAACtC,SAAQ,GAAIyB,cAAa;UAC9Bb,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,IAAI,CAACb,SAAS;QAC7C,OAAO;UACL;UACA,MAAMuC,SAAQ,GAAI,IAAI,CAACC,eAAe,CAAC;UACvC,IAAI,CAACC,SAAS,CAAC,MAAM;YACnB,IAAI,CAACzC,SAAQ,GAAIuC,SAAQ;UAC3B,CAAC;QACH;MACF,EAAE,OAAOtB,KAAK,EAAE;QACdL,OAAO,CAACK,KAAK,CAAC,WAAW,EAAEA,KAAK;QAChC,MAAMsB,SAAQ,GAAI,IAAI,CAACC,eAAe,CAAC;QACvC,IAAI,CAACC,SAAS,CAAC,MAAM;UACnB,IAAI,CAACzC,SAAQ,GAAIuC,SAAQ;QAC3B,CAAC;MACH,UAAU;QACR;QACAG,UAAU,CAAC,MAAM;UACf,IAAI,CAAC9C,OAAM,GAAI,KAAI;QACrB,CAAC,EAAE,GAAG;MACR;IACF,CAAC;IAED;IACAsC,qBAAqBA,CAACS,WAAW,EAAE;MACjC,MAAMC,YAAW,GAAI;QACnB,KAAK,EAAE,uCAAuC;QAC9C,KAAK,EAAE,oCAAoC;QAC3C,QAAQ,EAAE,wCAAwC;QAClD,IAAI,EAAE,4BAA4B;QAClC,MAAM,EAAE,yBAAyB;QACjC,QAAQ,EAAE,0BAA0B;QACpC,OAAO,EAAE,wBAAwB;QACjC,IAAI,EAAE;MACR;MACA,OAAOA,YAAY,CAACD,WAAW,KAAK,gBAAe;IACrD,CAAC;IAED;IACAH,eAAeA,CAAA,EAAG;MAChB;MACA,MAAMK,WAAU,GAAI;MAClB;MACA;QACE3B,EAAE,EAAE,CAAC;QACLzB,IAAI,EAAE,KAAK;QACXwC,WAAW,EAAE,gCAAgC;QAC7CH,IAAI,EAAE,uBAAuB;QAC7BK,KAAK,EAAE,MAAM;QACbZ,UAAU,EAAE;MACd,CAAC,EACD;QACEL,EAAE,EAAE,CAAC;QACLzB,IAAI,EAAE,MAAM;QACZwC,WAAW,EAAE,gCAAgC;QAC7CH,IAAI,EAAE,uBAAuB;QAC7BK,KAAK,EAAE,MAAM;QACbZ,UAAU,EAAE;MACd,CAAC,EACD;QACEL,EAAE,EAAE,CAAC;QACLzB,IAAI,EAAE,KAAK;QACXwC,WAAW,EAAE,yBAAyB;QACtCH,IAAI,EAAE,uBAAuB;QAC7BK,KAAK,EAAE,MAAM;QACbZ,UAAU,EAAE;MACd,CAAC;MAED;MACA;QACEL,EAAE,EAAE,CAAC;QACLzB,IAAI,EAAE,KAAK;QACXwC,WAAW,EAAE,+BAA+B;QAC5CH,IAAI,EAAE,0BAA0B;QAChCK,KAAK,EAAE,MAAM;QACbZ,UAAU,EAAE;MACd,CAAC,EACD;QACEL,EAAE,EAAE,EAAE;QACNzB,IAAI,EAAE,MAAM;QACZwC,WAAW,EAAE,2BAA2B;QACxCH,IAAI,EAAE,0BAA0B;QAChCK,KAAK,EAAE,MAAM;QACbZ,UAAU,EAAE;MACd,CAAC,EACD;QACEL,EAAE,EAAE,EAAE;QACNzB,IAAI,EAAE,KAAK;QACXwC,WAAW,EAAE,2BAA2B;QACxCH,IAAI,EAAE,0BAA0B;QAChCK,KAAK,EAAE,MAAM;QACbZ,UAAU,EAAE;MACd,CAAC;MAED;MACA;QACEL,EAAE,EAAE,CAAC;QACLzB,IAAI,EAAE,QAAQ;QACdwC,WAAW,EAAE,mCAAmC;QAChDH,IAAI,EAAE,qBAAqB;QAC3BK,KAAK,EAAE,MAAM;QACbZ,UAAU,EAAE;MACd,CAAC,EACD;QACEL,EAAE,EAAE,EAAE;QACNzB,IAAI,EAAE,IAAI;QACVwC,WAAW,EAAE,4BAA4B;QACzCH,IAAI,EAAE,qBAAqB;QAC3BK,KAAK,EAAE,MAAM;QACbZ,UAAU,EAAE;MACd,CAAC,EACD;QACEL,EAAE,EAAE,EAAE;QACNzB,IAAI,EAAE,IAAI;QACVwC,WAAW,EAAE,uBAAuB;QACpCH,IAAI,EAAE,qBAAqB;QAC3BK,KAAK,EAAE,KAAK;QACZZ,UAAU,EAAE;MACd,CAAC;MAED;MACA;QACEL,EAAE,EAAE,EAAE;QACNzB,IAAI,EAAE,MAAM;QACZwC,WAAW,EAAE,sBAAsB;QACnCH,IAAI,EAAE,uBAAuB;QAC7BK,KAAK,EAAE,KAAK;QACZZ,UAAU,EAAE;MACd,CAAC,EACD;QACEL,EAAE,EAAE,EAAE;QACNzB,IAAI,EAAE,KAAK;QACXwC,WAAW,EAAE,2BAA2B;QACxCH,IAAI,EAAE,uBAAuB;QAC7BK,KAAK,EAAE,KAAK;QACZZ,UAAU,EAAE;MACd,CAAC,EACD;QACEL,EAAE,EAAE,EAAE;QACNzB,IAAI,EAAE,IAAI;QACVwC,WAAW,EAAE,qBAAqB;QAClCH,IAAI,EAAE,uBAAuB;QAC7BK,KAAK,EAAE,KAAK;QACZZ,UAAU,EAAE;MACd,CAAC;MAED;MACA;QACEL,EAAE,EAAE,EAAE;QACNzB,IAAI,EAAE,KAAK;QACXwC,WAAW,EAAE,uBAAuB;QACpCH,IAAI,EAAE,uBAAuB;QAC7BK,KAAK,EAAE,MAAM;QACbZ,UAAU,EAAE;MACd,CAAC,EACD;QACEL,EAAE,EAAE,EAAE;QACNzB,IAAI,EAAE,KAAK;QACXwC,WAAW,EAAE,sBAAsB;QACnCH,IAAI,EAAE,uBAAuB;QAC7BK,KAAK,EAAE,MAAM;QACbZ,UAAU,EAAE;MACd,CAAC,EACD;QACEL,EAAE,EAAE,EAAE;QACNzB,IAAI,EAAE,KAAK;QACXwC,WAAW,EAAE,sBAAsB;QACnCH,IAAI,EAAE,uBAAuB;QAC7BK,KAAK,EAAE,MAAM;QACbZ,UAAU,EAAE;MACd,CAAC;MAED;MACA;QACEL,EAAE,EAAE,EAAE;QACNzB,IAAI,EAAE,MAAM;QACZwC,WAAW,EAAE,0BAA0B;QACvCH,IAAI,EAAE,uBAAuB;QAC7BK,KAAK,EAAE,OAAO;QACdZ,UAAU,EAAE;MACd,CAAC,EACD;QACEL,EAAE,EAAE,EAAE;QACNzB,IAAI,EAAE,MAAM;QACZwC,WAAW,EAAE,uBAAuB;QACpCH,IAAI,EAAE,uBAAuB;QAC7BK,KAAK,EAAE,MAAM;QACbZ,UAAU,EAAE;MACd,CAAC,EACD;QACEL,EAAE,EAAE,EAAE;QACNzB,IAAI,EAAE,MAAM;QACZwC,WAAW,EAAE,wBAAwB;QACrCH,IAAI,EAAE,uBAAuB;QAC7BK,KAAK,EAAE,OAAO;QACdZ,UAAU,EAAE;MACd,EACF;;MAEA;MACA,IAAI,IAAI,CAACzB,gBAAgB,EAAE;QACzB,OAAO+C,WAAW,CAACC,MAAM,CAACC,OAAM,IAAKA,OAAO,CAACxB,UAAS,KAAM,IAAI,CAACzB,gBAAgB;MACnF;MAEA,OAAO+C,WAAU;IACnB,CAAC;IAED;IACAG,iBAAiBA,CAACC,IAAI,EAAE;MACtB,IAAI,CAAC/C,eAAc,GAAI+C,IAAG;MAC1B,IAAI,CAAChD,gBAAe,GAAI,IAAG;IAC7B,CAAC;IAED;IACAiD,SAASA,CAACD,IAAI,EAAE;MACdrC,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEoC,IAAI,CAACxD,IAAI;MAC/B,IAAI,CAAC0D,QAAQ,CAACC,OAAO,CAAC,UAAUH,IAAI,CAACxD,IAAI,EAAE;IAC7C,CAAC;IAED;IACA4D,MAAMA,CAACJ,IAAI,EAAE;MACXrC,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEoC,IAAI,CAACxD,IAAI;MAC9B,IAAI,CAAC0D,QAAQ,CAACG,IAAI,CAAC,QAAQL,IAAI,CAACxD,IAAI,EAAE;IACxC,CAAC;IAED;IACA8D,gBAAgBA,CAACC,KAAK,EAAE;MACtBA,KAAK,CAACC,MAAM,CAACC,GAAE,GAAI,uBAAsB;IAC3C,CAAC;IAED;IACAC,aAAaA,CAAA,EAAG;MACd,IAAI,IAAI,CAAC7D,gBAAgB,EAAE;QACzB,IAAI,CAAC8D,OAAO,CAACC,IAAI,CAAC;UAChBC,IAAI,EAAE,QAAQ;UACdC,KAAK,EAAE;YAAExC,UAAU,EAAE,IAAI,CAACzB;UAAiB;QAC7C,CAAC;MACH,OAAO;QACL,IAAI,CAAC8D,OAAO,CAACC,IAAI,CAAC,QAAQ;MAC5B;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}