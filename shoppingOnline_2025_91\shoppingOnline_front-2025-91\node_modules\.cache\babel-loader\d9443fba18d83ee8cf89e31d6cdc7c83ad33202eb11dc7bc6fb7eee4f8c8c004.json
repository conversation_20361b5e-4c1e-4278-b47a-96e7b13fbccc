{"ast": null, "code": "import { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, withKeys as _withKeys, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, withModifiers as _withModifiers, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"login-demo\"\n};\nconst _hoisted_2 = {\n  class: \"login-container\"\n};\nconst _hoisted_3 = {\n  class: \"login-card\"\n};\nconst _hoisted_4 = {\n  class: \"demo-info\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_el_form_item = _resolveComponent(\"el-form-item\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_form = _resolveComponent(\"el-form\");\n  const _component_el_alert = _resolveComponent(\"el-alert\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_cache[3] || (_cache[3] = _createElementVNode(\"h2\", {\n    class: \"login-title\"\n  }, \"在线购物商城\", -1 /* CACHED */)), _cache[4] || (_cache[4] = _createElementVNode(\"p\", {\n    class: \"login-subtitle\"\n  }, \"请登录您的账户\", -1 /* CACHED */)), _createVNode(_component_el_form, {\n    model: $data.loginForm,\n    rules: $data.rules,\n    ref: \"loginFormRef\",\n    class: \"login-form\",\n    onSubmit: _withModifiers($options.handleLogin, [\"prevent\"])\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_form_item, {\n      prop: \"username\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        modelValue: $data.loginForm.username,\n        \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $data.loginForm.username = $event),\n        placeholder: \"请输入用户名\",\n        size: \"large\",\n        \"prefix-icon\": \"User\"\n      }, null, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_form_item, {\n      prop: \"password\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        modelValue: $data.loginForm.password,\n        \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $data.loginForm.password = $event),\n        type: \"password\",\n        placeholder: \"请输入密码\",\n        size: \"large\",\n        \"prefix-icon\": \"Lock\",\n        \"show-password\": \"\",\n        onKeyup: _withKeys($options.handleLogin, [\"enter\"])\n      }, null, 8 /* PROPS */, [\"modelValue\", \"onKeyup\"])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_form_item, null, {\n      default: _withCtx(() => [_createVNode(_component_el_button, {\n        type: \"primary\",\n        size: \"large\",\n        class: \"login-btn\",\n        loading: $data.loading,\n        onClick: $options.handleLogin\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($data.loading ? '登录中...' : '登录'), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"loading\", \"onClick\"])]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"model\", \"rules\", \"onSubmit\"]), _createElementVNode(\"div\", _hoisted_4, [_createVNode(_component_el_alert, {\n    title: \"演示账户\",\n    type: \"info\",\n    closable: false,\n    \"show-icon\": \"\"\n  }, {\n    default: _withCtx(() => [...(_cache[2] || (_cache[2] = [_createElementVNode(\"p\", null, [_createElementVNode(\"strong\", null, \"用户名:\"), _createTextVNode(\" admin\")], -1 /* CACHED */), _createElementVNode(\"p\", null, [_createElementVNode(\"strong\", null, \"密码:\"), _createTextVNode(\" 123456\")], -1 /* CACHED */)]))]),\n    _: 1 /* STABLE */\n  })])])])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_createVNode", "_component_el_form", "model", "$data", "loginForm", "rules", "ref", "onSubmit", "_withModifiers", "$options", "handleLogin", "_component_el_form_item", "prop", "_component_el_input", "username", "$event", "placeholder", "size", "password", "type", "onKeyup", "_with<PERSON><PERSON><PERSON>", "_component_el_button", "loading", "onClick", "_hoisted_4", "_component_el_alert", "title", "closable", "default", "_withCtx", "_cache"], "sources": ["D:\\2025_down\\project\\shoppingOnline_2025_91\\shoppingOnline_front-2025-91\\src\\views\\LoginDemo.vue"], "sourcesContent": ["<template>\n  <div class=\"login-demo\">\n    <div class=\"login-container\">\n      <div class=\"login-card\">\n        <h2 class=\"login-title\">在线购物商城</h2>\n        <p class=\"login-subtitle\">请登录您的账户</p>\n        \n        <el-form \n          :model=\"loginForm\" \n          :rules=\"rules\" \n          ref=\"loginFormRef\"\n          class=\"login-form\"\n          @submit.prevent=\"handleLogin\"\n        >\n          <el-form-item prop=\"username\">\n            <el-input\n              v-model=\"loginForm.username\"\n              placeholder=\"请输入用户名\"\n              size=\"large\"\n              prefix-icon=\"User\"\n            />\n          </el-form-item>\n          \n          <el-form-item prop=\"password\">\n            <el-input\n              v-model=\"loginForm.password\"\n              type=\"password\"\n              placeholder=\"请输入密码\"\n              size=\"large\"\n              prefix-icon=\"Lock\"\n              show-password\n              @keyup.enter=\"handleLogin\"\n            />\n          </el-form-item>\n          \n          <el-form-item>\n            <el-button \n              type=\"primary\" \n              size=\"large\" \n              class=\"login-btn\"\n              :loading=\"loading\"\n              @click=\"handleLogin\"\n            >\n              {{ loading ? '登录中...' : '登录' }}\n            </el-button>\n          </el-form-item>\n        </el-form>\n        \n        <div class=\"demo-info\">\n          <el-alert\n            title=\"演示账户\"\n            type=\"info\"\n            :closable=\"false\"\n            show-icon\n          >\n            <template #default>\n              <p><strong>用户名:</strong> admin</p>\n              <p><strong>密码:</strong> 123456</p>\n            </template>\n          </el-alert>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { useUserStore } from '@/store/index'\nimport request from '@/utils/request'\n\nexport default {\n  name: 'LoginDemo',\n  \n  data() {\n    return {\n      loading: false,\n      loginForm: {\n        username: 'admin',\n        password: '123456'\n      },\n      rules: {\n        username: [\n          { required: true, message: '请输入用户名', trigger: 'blur' }\n        ],\n        password: [\n          { required: true, message: '请输入密码', trigger: 'blur' },\n          { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }\n        ]\n      }\n    }\n  },\n  \n  created() {\n    // 检查是否已经登录\n    const userStore = useUserStore();\n    userStore.restoreLoginState();\n    if (userStore.isAuthenticated) {\n      console.log('用户已登录，跳转到商品页');\n      this.$router.push('/goods');\n    }\n  },\n  \n  methods: {\n    async handleLogin() {\n      try {\n        // 表单验证\n        await this.$refs.loginFormRef.validate();\n        \n        this.loading = true;\n        \n        // 模拟登录请求\n        const response = await this.mockLogin();\n        \n        if (response.success) {\n          this.$message.success('登录成功！');\n          \n          // 保存用户信息\n          const userStore = useUserStore();\n          const user = {\n            username: this.loginForm.username,\n            role: 'user',\n            id: Date.now(),\n            avatar: ''\n          };\n          const token = 'demo-token-' + Date.now();\n          \n          userStore.login(user, token);\n          \n          // 跳转到商品页\n          this.$router.push('/goods');\n        } else {\n          this.$message.error(response.message || '登录失败');\n        }\n      } catch (error) {\n        console.error('登录错误:', error);\n        this.$message.error('登录失败，请重试');\n      } finally {\n        this.loading = false;\n      }\n    },\n    \n    // 模拟登录API\n    async mockLogin() {\n      return new Promise((resolve) => {\n        setTimeout(() => {\n          if (this.loginForm.username === 'admin' && this.loginForm.password === '123456') {\n            resolve({\n              success: true,\n              data: {\n                username: this.loginForm.username,\n                role: 'admin'\n              }\n            });\n          } else {\n            resolve({\n              success: false,\n              message: '用户名或密码错误'\n            });\n          }\n        }, 1000);\n      });\n    }\n  }\n}\n</script>\n\n<style scoped>\n.login-demo {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 20px;\n}\n\n.login-container {\n  width: 100%;\n  max-width: 400px;\n}\n\n.login-card {\n  background: white;\n  border-radius: 16px;\n  padding: 40px;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n}\n\n.login-title {\n  text-align: center;\n  font-size: 28px;\n  color: #333;\n  margin: 0 0 8px;\n  font-weight: bold;\n}\n\n.login-subtitle {\n  text-align: center;\n  color: #666;\n  margin: 0 0 30px;\n  font-size: 16px;\n}\n\n.login-form {\n  margin-bottom: 20px;\n}\n\n.login-btn {\n  width: 100%;\n  height: 48px;\n  font-size: 16px;\n  font-weight: bold;\n}\n\n.demo-info {\n  margin-top: 20px;\n}\n\n.demo-info p {\n  margin: 5px 0;\n  font-size: 14px;\n}\n\n/* 响应式设计 */\n@media (max-width: 480px) {\n  .login-card {\n    padding: 30px 20px;\n  }\n  \n  .login-title {\n    font-size: 24px;\n  }\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAY;;EAChBA,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAY;;EA6ChBA,KAAK,EAAC;AAAW;;;;;;;uBA/C5BC,mBAAA,CA8DM,OA9DNC,UA8DM,GA7DJC,mBAAA,CA4DM,OA5DNC,UA4DM,GA3DJD,mBAAA,CA0DM,OA1DNE,UA0DM,G,0BAzDJF,mBAAA,CAAmC;IAA/BH,KAAK,EAAC;EAAa,GAAC,QAAM,qB,0BAC9BG,mBAAA,CAAqC;IAAlCH,KAAK,EAAC;EAAgB,GAAC,SAAO,qBAEjCM,YAAA,CAuCUC,kBAAA;IAtCPC,KAAK,EAAEC,KAAA,CAAAC,SAAS;IAChBC,KAAK,EAAEF,KAAA,CAAAE,KAAK;IACbC,GAAG,EAAC,cAAc;IAClBZ,KAAK,EAAC,YAAY;IACjBa,QAAM,EAAAC,cAAA,CAAUC,QAAA,CAAAC,WAAW;;sBAE5B,MAOe,CAPfV,YAAA,CAOeW,uBAAA;MAPDC,IAAI,EAAC;IAAU;wBAC3B,MAKE,CALFZ,YAAA,CAKEa,mBAAA;oBAJSV,KAAA,CAAAC,SAAS,CAACU,QAAQ;mEAAlBX,KAAA,CAAAC,SAAS,CAACU,QAAQ,GAAAC,MAAA;QAC3BC,WAAW,EAAC,QAAQ;QACpBC,IAAI,EAAC,OAAO;QACZ,aAAW,EAAC;;;QAIhBjB,YAAA,CAUeW,uBAAA;MAVDC,IAAI,EAAC;IAAU;wBAC3B,MAQE,CARFZ,YAAA,CAQEa,mBAAA;oBAPSV,KAAA,CAAAC,SAAS,CAACc,QAAQ;mEAAlBf,KAAA,CAAAC,SAAS,CAACc,QAAQ,GAAAH,MAAA;QAC3BI,IAAI,EAAC,UAAU;QACfH,WAAW,EAAC,OAAO;QACnBC,IAAI,EAAC,OAAO;QACZ,aAAW,EAAC,MAAM;QAClB,eAAa,EAAb,EAAa;QACZG,OAAK,EAAAC,SAAA,CAAQZ,QAAA,CAAAC,WAAW;;;QAI7BV,YAAA,CAUeW,uBAAA;wBATb,MAQY,CARZX,YAAA,CAQYsB,oBAAA;QAPVH,IAAI,EAAC,SAAS;QACdF,IAAI,EAAC,OAAO;QACZvB,KAAK,EAAC,WAAW;QAChB6B,OAAO,EAAEpB,KAAA,CAAAoB,OAAO;QAChBC,OAAK,EAAEf,QAAA,CAAAC;;0BAER,MAA+B,C,kCAA5BP,KAAA,CAAAoB,OAAO,mC;;;;;;qDAKhB1B,mBAAA,CAYM,OAZN4B,UAYM,GAXJzB,YAAA,CAUW0B,mBAAA;IATTC,KAAK,EAAC,MAAM;IACZR,IAAI,EAAC,MAAM;IACVS,QAAQ,EAAE,KAAK;IAChB,WAAS,EAAT;;IAEWC,OAAO,EAAAC,QAAA,CAChB,MAAkC,KAAAC,MAAA,QAAAA,MAAA,OAAlClC,mBAAA,CAAkC,YAA/BA,mBAAA,CAAqB,gBAAb,MAAI,G,iBAAS,QAAM,E,oBAC9BA,mBAAA,CAAkC,YAA/BA,mBAAA,CAAoB,gBAAZ,KAAG,G,iBAAS,SAAO,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}