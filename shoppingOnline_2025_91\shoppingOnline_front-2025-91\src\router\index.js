import { createRouter, createWebHistory } from 'vue-router'
import request from '../utils/request'


//requireAuth: 是否需要检查登录
const routes = [
  {
    path: '/',
    name: 'MainLayout',
    component: () => import('@/views/MainLayout.vue'),
    redirect: '/login',
    meta: { requireLogin: false },
    children: [
      {
        path: 'goods',
        name: "GoodsView",
        component: () => import('@/views/goods.vue'),
        meta: { title: '商品列表', requireLogin: true }
      },
      {
        path: 'home',
        name: "HomeView",
        component: () => import('@/views/Home.vue'),
        meta: { title: '首页', requireLogin: true }
      },
      {
        path: 'test',
        name: "TestPage",
        component: () => import('@/views/TestPage.vue'),
        meta: { title: '功能测试', requireLogin: true }
      },
      {
        path: 'category',
        name: "Category",
        component: () => import('@/views/Category.vue'),
        meta: { title: '商品分类', requireLogin: true }
      }
    ]
  },
   
    //前台
  // {
  //   path: '/',
  //   name: 'front',
  //   redirect: "/topview",
  //   component: () => import('../views/front/Front.vue'),
  //   meta: {title:'在线商城', path: '在线商城', requireAuth: false},
  //   children: [
      

  //   ]
  // },
    //后台
  // {
  //   path: '/manage',
  //   name: 'manage',
  //   component: () => import('../views/manage/Manage.vue'),
  //   redirect: "/manage/home",
  //   meta: {title:'后台', path: '后台',requireAuth: true},
  //   children: [
     

  //   ]
  // },
  {
    path: '/login',
    name: 'LoginView',
    meta: {
      title: '登录',
      requireAuth: false,
    },
    component: () => import(/* webpackChunkName: "about" */ '../views/Login.vue')
  },
  {
    path: '/login-demo',
    name: 'LoginDemo',
    meta: {
      title: '演示登录',
      requireAuth: false,
    },
    component: () => import('../views/LoginDemo.vue')
  },
  {
    path: '/register',
    name: 'RegisterView',
    meta: {
      title: '注册',requireAuth: false,
    },
    component: () => import(/* webpackChunkName: "about" */ '../views/Register.vue')
  },
  {
    path: '/*',
    name: 'NotFound',
    meta: {
      title: '找不到页面'
    },
    component: () => import(/* webpackChunkName: "about" */ '../views/404NotFound.vue')
  },
]
// 创建路由器
const router = createRouter({
  history: createWebHistory(), // 替代 mode: 'history'
  routes
})


//beforeEach是router的钩子函数，在进入路由前执行
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = to.meta.title
  } else {
    document.title = '在线购物商城'
  }

  // 检查是否需要登录
  if (to.meta.requireLogin === true) {
    if (!isLogin()) {
      next('/login')
      return
    }
  }

  // 检查是否需要权限验证
  if (to.meta.requireAuth === true) {
    // 在后台获得该用户的身份
    request.post("http://localhost:9197/role").then(res => {
      if (res.code === '200') {
        const role = res.data
        console.log('您的身份是：' + role)

        if (role === 'admin') {
          next()
        } else if (role === 'user') {
          alert("您没有权限")
          next("/")
        }
      } else {
        // 查询身份失败
        alert(res.msg)
        next('/login')
      }
    }).catch(() => {
      next('/login')
    })
  } else {
    next()
  }
})

function isLogin() {
  // 检查多种登录状态标识
  const isLoggedIn = localStorage.getItem("isLoggedIn") === 'true'
  const token = localStorage.getItem("token")
  const userInfo = localStorage.getItem("userInfo")

  return isLoggedIn && (token || userInfo)
}
/*import 'vue-vibe'*/
export default router
