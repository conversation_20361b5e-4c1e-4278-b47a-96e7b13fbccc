/*
 * @Description: 
 * @Author: JACK
 * @Date: 2025年8月29日11:12:13
 */
import { defineStore } from 'pinia'

// 定义主要的 store
export const useMainStore = defineStore({
  id: 'main',
  state: () => ({
    baseApi: 'http://localhost:9197'
  }),
  getters: {
    // 同步计算属性
  },
  actions: {
    // 可以包含异步逻辑
  }
})

// 定义用户状态 store
export const useUserStore = defineStore({
  id: 'user',
  state: () => ({
    isLoggedIn: false,
    userInfo: null,
    token: null
  }),
  getters: {
    // 获取用户名
    username: (state) => state.userInfo?.username || '',
    // 获取用户角色
    userRole: (state) => state.userInfo?.role || '',
    // 检查是否已登录
    isAuthenticated: (state) => state.isLoggedIn && !!state.token
  },
  actions: {
    // 登录
    login(userInfo, token) {
      this.isLoggedIn = true
      this.userInfo = userInfo
      this.token = token

      // 保存到localStorage
      localStorage.setItem('isLoggedIn', 'true')
      localStorage.setItem('userInfo', JSON.stringify(userInfo))
      localStorage.setItem('token', token)
    },

    // 登出
    logout() {
      this.isLoggedIn = false
      this.userInfo = null
      this.token = null

      // 清除localStorage
      localStorage.removeItem('isLoggedIn')
      localStorage.removeItem('userInfo')
      localStorage.removeItem('token')
      localStorage.removeItem('username') // 兼容旧版本
    },

    // 从localStorage恢复登录状态
    restoreLoginState() {
      const isLoggedIn = localStorage.getItem('isLoggedIn') === 'true'
      const userInfo = localStorage.getItem('userInfo')
      const token = localStorage.getItem('token')

      if (isLoggedIn && userInfo && token) {
        this.isLoggedIn = true
        this.userInfo = JSON.parse(userInfo)
        this.token = token
      }
    }
  }
})