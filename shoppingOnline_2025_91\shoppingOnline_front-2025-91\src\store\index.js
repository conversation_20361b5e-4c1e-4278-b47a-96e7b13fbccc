/*
 * @Description: 
 * @Author: JACK
 * @Date: 2025年8月29日11:12:13
 */
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

// 定义主要的 store
export const useMainStore = defineStore('main', () => {
  const baseApi = 'http://localhost:9197'

  return {
    baseApi
  }
})

// 定义用户状态 store
export const useUserStore = defineStore('user', () => {
  // 状态
  const isLoggedIn = ref(false)
  const userInfo = ref(null)
  const token = ref(null)

  // 计算属性
  const username = computed(() => userInfo.value?.username || '')
  const userRole = computed(() => userInfo.value?.role || '')
  const isAuthenticated = computed(() => isLoggedIn.value && !!token.value)

  // 方法
  const login = (user, userToken) => {
    isLoggedIn.value = true
    userInfo.value = user
    token.value = userToken

    // 保存到localStorage
    localStorage.setItem('isLoggedIn', 'true')
    localStorage.setItem('userInfo', JSON.stringify(user))
    localStorage.setItem('token', userToken)
  }

  const logout = () => {
    isLoggedIn.value = false
    userInfo.value = null
    token.value = null

    // 清除localStorage
    localStorage.removeItem('isLoggedIn')
    localStorage.removeItem('userInfo')
    localStorage.removeItem('token')
    localStorage.removeItem('username') // 兼容旧版本
  }

  const restoreLoginState = () => {
    const savedIsLoggedIn = localStorage.getItem('isLoggedIn') === 'true'
    const savedUserInfo = localStorage.getItem('userInfo')
    const savedToken = localStorage.getItem('token')

    if (savedIsLoggedIn && savedUserInfo && savedToken) {
      isLoggedIn.value = true
      userInfo.value = JSON.parse(savedUserInfo)
      token.value = savedToken
    }
  }

  return {
    // 状态
    isLoggedIn,
    userInfo,
    token,
    // 计算属性
    username,
    userRole,
    isAuthenticated,
    // 方法
    login,
    logout,
    restoreLoginState
  }
})