-- 插入商品数据（带图片链接）
-- 数据库：db_mall

USE db_mall;

-- 首先确保有分类数据
INSERT IGNORE INTO category (id, name) VALUES 
(1, '服装'),
(2, '鞋类'),
(3, '酒类');

-- 插入商品数据，包含图片链接
INSERT INTO good (id, name, description, discount, sales, sale_money, category_id, imgs, create_time, recommend, is_delete) VALUES 
(1, '女上衣', '时尚女性上衣，舒适面料，多种颜色可选', 0.85, 120, 2400.00, 1, 'https://img.alicdn.com/bao/uploaded/i4/705106688/O1CN01oqb6iZ1zH9i4qLBjJ_!!705106688.jpg', NOW(), 1, 0),
(2, '休闲鞋', '舒适透气的休闲运动鞋，适合日常穿着', 0.90, 85, 1700.00, 2, 'https://ts1.tc.mm.bing.net/th/id/OIP-C.jpOvme9hraBerYgowHeyRwHaE7?w=251&h=211&c=8&rs=1&qlt=90&o=6&dpr=1.3&pid=3.1&rm=2', NOW(), 1, 0),
(3, '威士忌 大瓶', '优质威士忌，口感醇厚，适合收藏和品鉴', 0.95, 45, 2250.00, 3, 'https://ts1.tc.mm.bing.net/th/id/OIP-C.9G_O0NZgTOl3FXFZrOOf1AHaE7?w=280&h=211&c=8&rs=1&qlt=90&o=6&dpr=1.3&pid=3.1&rm=2', NOW(), 1, 0)
ON DUPLICATE KEY UPDATE 
    imgs = VALUES(imgs),
    description = VALUES(description),
    discount = VALUES(discount),
    sales = VALUES(sales),
    sale_money = VALUES(sale_money),
    recommend = VALUES(recommend);

-- 插入商品规格数据（价格信息）
INSERT INTO good_standard (id, good_id, standard, price, stock) VALUES 
(1, 1, 'S码', 120.00, 50),
(2, 1, 'M码', 120.00, 80),
(3, 1, 'L码', 120.00, 60),
(4, 1, 'XL码', 125.00, 40),
(5, 2, '36码', 180.00, 30),
(6, 2, '37码', 180.00, 35),
(7, 2, '38码', 180.00, 40),
(8, 2, '39码', 180.00, 45),
(9, 2, '40码', 180.00, 35),
(10, 2, '41码', 185.00, 25),
(11, 2, '42码', 185.00, 20),
(12, 3, '750ml', 450.00, 20),
(13, 3, '1000ml', 580.00, 15)
ON DUPLICATE KEY UPDATE 
    price = VALUES(price),
    stock = VALUES(stock);

-- 插入轮播图数据
INSERT INTO carousel (id, good_id, show_order) VALUES 
(1, 1, 1),
(2, 2, 2),
(3, 3, 3)
ON DUPLICATE KEY UPDATE 
    show_order = VALUES(show_order);

-- 验证数据插入
SELECT 
    g.id,
    g.name,
    g.description,
    g.imgs,
    g.discount,
    g.sales,
    g.recommend,
    c.name as category_name
FROM good g 
LEFT JOIN category c ON g.category_id = c.id 
WHERE g.is_delete = 0
ORDER BY g.id;

-- 验证商品规格数据
SELECT 
    gs.id,
    g.name as good_name,
    gs.standard,
    gs.price,
    gs.stock
FROM good_standard gs
LEFT JOIN good g ON gs.good_id = g.id
ORDER BY gs.good_id, gs.id;

-- 验证轮播图数据
SELECT 
    c.id,
    c.good_id,
    g.name as good_name,
    g.imgs,
    c.show_order
FROM carousel c
LEFT JOIN good g ON c.good_id = g.id
ORDER BY c.show_order;
