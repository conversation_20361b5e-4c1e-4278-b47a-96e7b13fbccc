<template>
  <div class="product-category">
    <el-card class="category-card">
      <div class="category-title">商品分类</div>
      <div class="category-list">
        <el-button
          :class="['category-btn', { 'active': selectedCategory === null }]"
          @click="handleCategoryClick(null)"
        >
          全部分类
        </el-button>
        <el-button
          v-for="category in categories"
          :key="category.id"
          :class="['category-btn', { 'active': selectedCategory === category.id }]"
          @click="handleCategoryClick(category)"
        >
          {{ category.name }}
        </el-button>
      </div>
    </el-card>
  </div>
</template>

<script>
import request from '@/utils/request.js'

export default {
  name: 'ProductCategory',
  data() {
    return {
      selectedCategory: null,
      categories: [],
      loading: false
    };
  },

  created() {
    this.fetchCategories();
  },

  methods: {
    // 获取分类数据
    async fetchCategories() {
      try {
        this.loading = true;
        const response = await request.get('/categoryAPI');

        if (Array.isArray(response)) {
          this.categories = response;
          console.log('获取分类数据成功:', this.categories);
        } else {
          // 如果后端没有数据，使用默认分类
          this.categories = this.getDefaultCategories();
          console.log('使用默认分类数据');
        }
      } catch (error) {
        console.error('获取分类数据失败:', error);
        // 使用默认分类作为后备
        this.categories = this.getDefaultCategories();
      } finally {
        this.loading = false;
      }
    },

    // 获取默认分类数据
    getDefaultCategories() {
      return [
        { id: 1, name: '服装' },
        { id: 2, name: '鞋类' },
        { id: 3, name: '酒类' },
        { id: 4, name: '日用百货' },
        { id: 5, name: '家用电器' },
        { id: 6, name: '数码产品' }
      ];
    },

    handleCategoryClick(category) {
      if (category === null) {
        this.selectedCategory = null;
        this.$emit('category-change', { categoryId: null, categoryName: '全部分类' });
      } else {
        this.selectedCategory = category.id;
        this.$emit('category-change', { categoryId: category.id, categoryName: category.name });
      }
    },

    clearCategory() {
      this.selectedCategory = null;
      this.$emit('category-change', { categoryId: null, categoryName: '全部分类' });
    }
  }
};
</script>

<style scoped>
.product-category {
  margin-bottom: 20px;
}

.category-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}

.category-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 15px;
  color: #303133;
  padding-bottom: 10px;
  border-bottom: 1px solid #f0f0f0;
}

.category-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.category-btn {
  padding: 8px 16px;
  border-radius: 20px;
  background: #f5f5f5;
  color: #606266;
  border: none;
  transition: all 0.3s;
}

.category-btn:hover {
  background: #e9e9e9;
  color: #409eff;
}

.category-btn.active {
  background: #409eff;
  color: white;
}
</style>