<template>
  <div class="product-category">
    <el-card class="category-card">
      <div class="category-title">商品分类</div>
      <div class="category-list">
        <el-button
          v-for="category in categories"
          :key="category"
          :class="['category-btn', { 'active': selectedCategory === category }]"
          @click="handleCategoryClick(category)"
        >
          {{ category }}
        </el-button>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'ProductCategory',
  data() {
    return {
      selectedCategory: null,
      categories: [
        '日用百货',
        '服装鞋帽',
        '家用电器',
        '家居家装',
        '数码产品',
        '母婴用品',
        '美妆个护',
        '运动户外',
        '图书音影'
      ]
    };
  },
  methods: {
    handleCategoryClick(category) {
      this.selectedCategory = category;
      this.$emit('category-change', category);
    },
    clearCategory() {
      this.selectedCategory = null;
      this.$emit('category-change', null);
    }
  }
};
</script>

<style scoped>
.product-category {
  margin-bottom: 20px;
}

.category-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}

.category-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 15px;
  color: #303133;
  padding-bottom: 10px;
  border-bottom: 1px solid #f0f0f0;
}

.category-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.category-btn {
  padding: 8px 16px;
  border-radius: 20px;
  background: #f5f5f5;
  color: #606266;
  border: none;
  transition: all 0.3s;
}

.category-btn:hover {
  background: #e9e9e9;
  color: #409eff;
}

.category-btn.active {
  background: #409eff;
  color: white;
}
</style>