{"ast": null, "code": "export default {\n  name: 'XxxView',\n  // ✅ 推荐使用多词组件名\n\n  // 引入的组件需要注册\n  components: {},\n  // 数据区\n  data() {\n    return {};\n  },\n  // 计算属性\n  computed: {},\n  // 监听数据变化\n  watch: {},\n  // 方法集合\n  methods: {},\n  // 生命周期 - 创建完成\n  created() {\n    // \n  },\n  // 生命周期 - 挂载完成\n  mounted() {\n    // \n  },\n  // 生命周期 - 更新之前\n  beforeUpdate() {},\n  // 生命周期 - 更新之后\n  updated() {},\n  // 生命周期 - 卸载前\n  beforeUnmount() {\n    // 替代 beforeDestroy\n    // \n  },\n  // 生命周期 - 卸载后\n  unmounted() {\n    // 替代 destroyed\n    // \n  },\n  // keep-alive 缓存组件激活时触发\n  activated() {},\n  // keep-alive 缓存组件失活时触发\n  deactivated() {}\n};", "map": {"version": 3, "names": ["name", "components", "data", "computed", "watch", "methods", "created", "mounted", "beforeUpdate", "updated", "beforeUnmount", "unmounted", "activated", "deactivated"], "sources": ["D:\\2025_down\\project\\shoppingOnline_2025_91\\shoppingOnline_front-2025-91\\src\\views\\Home.vue"], "sourcesContent": ["<!--  -->\r\n<template>\r\n  <div class=\"\"></div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'XxxView', // ✅ 推荐使用多词组件名\r\n\r\n  // 引入的组件需要注册\r\n  components: {},\r\n\r\n  // 数据区\r\n  data() {\r\n    return {\r\n      \r\n    };\r\n  },\r\n\r\n  // 计算属性\r\n  computed: {},\r\n\r\n  // 监听数据变化\r\n  watch: {},\r\n\r\n  // 方法集合\r\n  methods: {\r\n    \r\n  },\r\n\r\n  // 生命周期 - 创建完成\r\n  created() {\r\n    // \r\n  },\r\n\r\n  // 生命周期 - 挂载完成\r\n  mounted() {\r\n    // \r\n  },\r\n\r\n  // 生命周期 - 更新之前\r\n  beforeUpdate() {},\r\n\r\n  // 生命周期 - 更新之后\r\n  updated() {},\r\n\r\n  // 生命周期 - 卸载前\r\n  beforeUnmount() {\r\n    // 替代 beforeDestroy\r\n    // \r\n  },\r\n\r\n  // 生命周期 - 卸载后\r\n  unmounted() {\r\n    // 替代 destroyed\r\n    // \r\n  },\r\n\r\n  // keep-alive 缓存组件激活时触发\r\n  activated() {},\r\n\r\n  // keep-alive 缓存组件失活时触发\r\n  deactivated() {}\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n\r\n</style>"], "mappings": "AAMA,eAAe;EACbA,IAAI,EAAE,SAAS;EAAE;;EAEjB;EACAC,UAAU,EAAE,CAAC,CAAC;EAEd;EACAC,IAAIA,CAAA,EAAG;IACL,OAAO,CAEP,CAAC;EACH,CAAC;EAED;EACAC,QAAQ,EAAE,CAAC,CAAC;EAEZ;EACAC,KAAK,EAAE,CAAC,CAAC;EAET;EACAC,OAAO,EAAE,CAET,CAAC;EAED;EACAC,OAAOA,CAAA,EAAG;IACR;EAAA,CACD;EAED;EACAC,OAAOA,CAAA,EAAG;IACR;EAAA,CACD;EAED;EACAC,YAAYA,CAAA,EAAG,CAAC,CAAC;EAEjB;EACAC,OAAOA,CAAA,EAAG,CAAC,CAAC;EAEZ;EACAC,aAAaA,CAAA,EAAG;IACd;IACA;EAAA,CACD;EAED;EACAC,SAASA,CAAA,EAAG;IACV;IACA;EAAA,CACD;EAED;EACAC,SAASA,CAAA,EAAG,CAAC,CAAC;EAEd;EACAC,WAAWA,CAAA,EAAG,CAAC;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}