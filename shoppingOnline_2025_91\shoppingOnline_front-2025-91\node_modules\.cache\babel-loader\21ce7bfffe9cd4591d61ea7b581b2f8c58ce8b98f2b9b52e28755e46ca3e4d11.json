{"ast": null, "code": "import { createElementVNode as _createElementVNode, createCommentVNode as _createCommentVNode, vModelText as _vModelText, normalizeClass as _normalizeClass, withDirectives as _withDirectives, toDisplayString as _toDisplayString, openBlock as _openBlock, createElementBlock as _createElementBlock, vModelCheckbox as _vModelCheckbox, createTextVNode as _createTextVNode, withModifiers as _withModifiers } from \"vue\";\nconst _hoisted_1 = {\n  class: \"login-container\"\n};\nconst _hoisted_2 = {\n  class: \"login-card\"\n};\nconst _hoisted_3 = {\n  class: \"form-group\"\n};\nconst _hoisted_4 = {\n  key: 0,\n  class: \"error-text\"\n};\nconst _hoisted_5 = {\n  class: \"form-group\"\n};\nconst _hoisted_6 = {\n  key: 0,\n  class: \"error-text\"\n};\nconst _hoisted_7 = {\n  class: \"form-options\"\n};\nconst _hoisted_8 = {\n  class: \"checkbox-wrapper\"\n};\nconst _hoisted_9 = [\"disabled\"];\nconst _hoisted_10 = {\n  class: \"register-section\"\n};\nconst _hoisted_11 = {\n  class: \"social-login\"\n};\nconst _hoisted_12 = {\n  class: \"modal-header\"\n};\nconst _hoisted_13 = {\n  class: \"form-group\"\n};\nconst _hoisted_14 = {\n  class: \"form-group\"\n};\nconst _hoisted_15 = {\n  class: \"form-group\"\n};\nconst _hoisted_16 = [\"disabled\"];\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_cache[24] || (_cache[24] = _createElementVNode(\"div\", {\n    class: \"login-header\"\n  }, [_createElementVNode(\"h2\", null, \"欢迎登录\"), _createElementVNode(\"p\", {\n    class: \"subtitle\"\n  }, \"在线商城管理系统\")], -1 /* CACHED */)), _createCommentVNode(\" 登录页面 \"), _createElementVNode(\"form\", {\n    onSubmit: _cache[6] || (_cache[6] = _withModifiers((...args) => $options.handleLogin && $options.handleLogin(...args), [\"prevent\"])),\n    class: \"login-form\"\n  }, [_createElementVNode(\"div\", _hoisted_3, [_withDirectives(_createElementVNode(\"input\", {\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $data.loginForm.username = $event),\n    type: \"text\",\n    placeholder: \"用户名\",\n    class: _normalizeClass([\"form-input\", {\n      'error': $data.usernameError\n    }]),\n    onBlur: _cache[1] || (_cache[1] = (...args) => $options.validateUsername && $options.validateUsername(...args))\n  }, null, 34 /* CLASS, NEED_HYDRATION */), [[_vModelText, $data.loginForm.username]]), $data.usernameError ? (_openBlock(), _createElementBlock(\"span\", _hoisted_4, _toDisplayString($data.usernameError), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_5, [_withDirectives(_createElementVNode(\"input\", {\n    \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $data.loginForm.password = $event),\n    type: \"password\",\n    placeholder: \"密码\",\n    class: _normalizeClass([\"form-input\", {\n      'error': $data.passwordError\n    }]),\n    onBlur: _cache[3] || (_cache[3] = (...args) => $options.validatePassword && $options.validatePassword(...args))\n  }, null, 34 /* CLASS, NEED_HYDRATION */), [[_vModelText, $data.loginForm.password]]), $data.passwordError ? (_openBlock(), _createElementBlock(\"span\", _hoisted_6, _toDisplayString($data.passwordError), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"label\", _hoisted_8, [_withDirectives(_createElementVNode(\"input\", {\n    \"onUpdate:modelValue\": _cache[4] || (_cache[4] = $event => $data.rememberMe = $event),\n    type: \"checkbox\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelCheckbox, $data.rememberMe]]), _cache[18] || (_cache[18] = _createElementVNode(\"span\", {\n    class: \"checkmark\"\n  }, null, -1 /* CACHED */)), _cache[19] || (_cache[19] = _createTextVNode(\" 记住我 \", -1 /* CACHED */))]), _createElementVNode(\"a\", {\n    href: \"#\",\n    class: \"forgot-link\",\n    onClick: _cache[5] || (_cache[5] = _withModifiers((...args) => $options.showForgotPassword && $options.showForgotPassword(...args), [\"prevent\"]))\n  }, \"忘记密码?\")]), _createElementVNode(\"button\", {\n    type: \"submit\",\n    class: \"login-btn\",\n    disabled: $data.isLoading\n  }, _toDisplayString($data.isLoading ? '登录中...' : '登 录'), 9 /* TEXT, PROPS */, _hoisted_9)], 32 /* NEED_HYDRATION */), _createElementVNode(\"div\", _hoisted_10, [_cache[20] || (_cache[20] = _createElementVNode(\"span\", null, \"没有账号？\", -1 /* CACHED */)), _createElementVNode(\"button\", {\n    onClick: _cache[7] || (_cache[7] = (...args) => $options.showRegisterForm && $options.showRegisterForm(...args)),\n    class: \"register-btn\"\n  }, \"注册用户\")]), _cache[25] || (_cache[25] = _createElementVNode(\"div\", {\n    class: \"divider\"\n  }, [_createElementVNode(\"span\", null, \"第三方登录\")], -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_11, [_createElementVNode(\"button\", {\n    class: \"social-btn wechat\",\n    onClick: _cache[8] || (_cache[8] = $event => $options.thirdPartyLogin('wechat'))\n  }, [...(_cache[21] || (_cache[21] = [_createElementVNode(\"div\", {\n    class: \"social-icon wechat-icon\"\n  }, null, -1 /* CACHED */)]))]), _createElementVNode(\"button\", {\n    class: \"social-btn weibo\",\n    onClick: _cache[9] || (_cache[9] = $event => $options.thirdPartyLogin('weibo'))\n  }, [...(_cache[22] || (_cache[22] = [_createElementVNode(\"div\", {\n    class: \"social-icon weibo-icon\"\n  }, null, -1 /* CACHED */)]))]), _createElementVNode(\"button\", {\n    class: \"social-btn qq\",\n    onClick: _cache[10] || (_cache[10] = $event => $options.thirdPartyLogin('qq'))\n  }, [...(_cache[23] || (_cache[23] = [_createElementVNode(\"div\", {\n    class: \"social-icon qq-icon\"\n  }, null, -1 /* CACHED */)]))])])]), _createCommentVNode(\" 注册弹窗 \"), $data.showRegister ? (_openBlock(), _createElementBlock(\"div\", {\n    key: 0,\n    class: \"modal-overlay\",\n    onClick: _cache[17] || (_cache[17] = (...args) => $options.closeRegisterForm && $options.closeRegisterForm(...args))\n  }, [_createElementVNode(\"div\", {\n    class: \"modal-content\",\n    onClick: _cache[16] || (_cache[16] = _withModifiers(() => {}, [\"stop\"]))\n  }, [_createElementVNode(\"div\", _hoisted_12, [_cache[26] || (_cache[26] = _createElementVNode(\"h3\", null, \"用户注册\", -1 /* CACHED */)), _createElementVNode(\"button\", {\n    onClick: _cache[11] || (_cache[11] = (...args) => $options.closeRegisterForm && $options.closeRegisterForm(...args)),\n    class: \"close-btn\"\n  }, \"×\")]), _createElementVNode(\"form\", {\n    onSubmit: _cache[15] || (_cache[15] = _withModifiers((...args) => $options.handleRegister && $options.handleRegister(...args), [\"prevent\"])),\n    class: \"register-form\"\n  }, [_createElementVNode(\"div\", _hoisted_13, [_withDirectives(_createElementVNode(\"input\", {\n    \"onUpdate:modelValue\": _cache[12] || (_cache[12] = $event => $data.registerForm.username = $event),\n    type: \"text\",\n    placeholder: \"用户名\",\n    class: \"form-input\",\n    required: \"\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.registerForm.username]])]), _createElementVNode(\"div\", _hoisted_14, [_withDirectives(_createElementVNode(\"input\", {\n    \"onUpdate:modelValue\": _cache[13] || (_cache[13] = $event => $data.registerForm.password = $event),\n    type: \"password\",\n    placeholder: \"密码\",\n    class: \"form-input\",\n    required: \"\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.registerForm.password]])]), _createElementVNode(\"div\", _hoisted_15, [_withDirectives(_createElementVNode(\"input\", {\n    \"onUpdate:modelValue\": _cache[14] || (_cache[14] = $event => $data.registerForm.confirmPassword = $event),\n    type: \"password\",\n    placeholder: \"确认密码\",\n    class: \"form-input\",\n    required: \"\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.registerForm.confirmPassword]])]), _createElementVNode(\"button\", {\n    type: \"submit\",\n    class: \"register-submit-btn\",\n    disabled: $data.isRegistering\n  }, _toDisplayString($data.isRegistering ? '注册中...' : '注册'), 9 /* TEXT, PROPS */, _hoisted_16)], 32 /* NEED_HYDRATION */)])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 消息提示 \"), $data.message.show ? (_openBlock(), _createElementBlock(\"div\", {\n    key: 1,\n    class: _normalizeClass(['message', $data.message.type])\n  }, _toDisplayString($data.message.text), 3 /* TEXT, CLASS */)) : _createCommentVNode(\"v-if\", true)]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_createCommentVNode", "onSubmit", "_cache", "_withModifiers", "args", "$options", "handleLogin", "_hoisted_3", "$data", "loginForm", "username", "$event", "type", "placeholder", "_normalizeClass", "usernameError", "onBlur", "validateUsername", "_hoisted_4", "_toDisplayString", "_hoisted_5", "password", "passwordError", "validatePassword", "_hoisted_6", "_hoisted_7", "_hoisted_8", "rememberMe", "href", "onClick", "showForgotPassword", "disabled", "isLoading", "_hoisted_9", "_hoisted_10", "showRegisterForm", "_hoisted_11", "thirdParty<PERSON><PERSON>in", "showRegister", "closeRegisterForm", "_hoisted_12", "handleRegister", "_hoisted_13", "registerForm", "required", "_hoisted_14", "_hoisted_15", "confirmPassword", "isRegistering", "_hoisted_16", "message", "show", "text"], "sources": ["D:\\2025_down\\project\\shoppingOnline_2025_91\\shoppingOnline_front-2025-91\\src\\views\\Login.vue"], "sourcesContent": ["\r\n<template>\r\n  <div class=\"login-container\">\r\n    <div class=\"login-card\">\r\n      <div class=\"login-header\">\r\n        <h2>欢迎登录</h2>\r\n        <p class=\"subtitle\">在线商城管理系统</p>\r\n      </div>\r\n     <!-- 登录页面 -->\r\n      <form @submit.prevent=\"handleLogin\" class=\"login-form\">\r\n        <div class=\"form-group\">\r\n          <input\r\n            v-model=\"loginForm.username\"\r\n            type=\"text\"\r\n            placeholder=\"用户名\"\r\n            class=\"form-input\"\r\n            :class=\"{ 'error': usernameError }\"\r\n            @blur=\"validateUsername\"\r\n          />\r\n          <span v-if=\"usernameError\" class=\"error-text\">{{ usernameError }}</span>\r\n        </div>\r\n\r\n        <div class=\"form-group\">\r\n          <input\r\n            v-model=\"loginForm.password\"\r\n            type=\"password\"\r\n            placeholder=\"密码\"\r\n            class=\"form-input\"\r\n            :class=\"{ 'error': passwordError }\"\r\n            @blur=\"validatePassword\"\r\n          />\r\n          <span v-if=\"passwordError\" class=\"error-text\">{{ passwordError }}</span>\r\n        </div>\r\n\r\n        <div class=\"form-options\">\r\n          <label class=\"checkbox-wrapper\">\r\n            <input v-model=\"rememberMe\" type=\"checkbox\" />\r\n            <span class=\"checkmark\"></span>\r\n            记住我\r\n          </label>\r\n          <a href=\"#\" class=\"forgot-link\" @click.prevent=\"showForgotPassword\">忘记密码?</a>\r\n        </div>\r\n\r\n        <button type=\"submit\" class=\"login-btn\" :disabled=\"isLoading\">\r\n          {{ isLoading ? '登录中...' : '登 录' }}\r\n        </button>\r\n      </form>\r\n\r\n      <div class=\"register-section\">\r\n        <span>没有账号？</span>\r\n        <button @click=\"showRegisterForm\" class=\"register-btn\">注册用户</button>\r\n      </div>\r\n\r\n      <div class=\"divider\">\r\n        <span>第三方登录</span>\r\n      </div>\r\n\r\n      <div class=\"social-login\">\r\n        <button class=\"social-btn wechat\" @click=\"thirdPartyLogin('wechat')\">\r\n          <div class=\"social-icon wechat-icon\"></div>\r\n        </button>\r\n        <button class=\"social-btn weibo\" @click=\"thirdPartyLogin('weibo')\">\r\n          <div class=\"social-icon weibo-icon\"></div>\r\n        </button>\r\n        <button class=\"social-btn qq\" @click=\"thirdPartyLogin('qq')\">\r\n          <div class=\"social-icon qq-icon\"></div>\r\n        </button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 注册弹窗 -->\r\n    <div v-if=\"showRegister\" class=\"modal-overlay\" @click=\"closeRegisterForm\">\r\n      <div class=\"modal-content\" @click.stop>\r\n        <div class=\"modal-header\">\r\n          <h3>用户注册</h3>\r\n          <button @click=\"closeRegisterForm\" class=\"close-btn\">&times;</button>\r\n        </div>\r\n        <form @submit.prevent=\"handleRegister\" class=\"register-form\">\r\n          <div class=\"form-group\">\r\n            <input\r\n              v-model=\"registerForm.username\"\r\n              type=\"text\"\r\n              placeholder=\"用户名\"\r\n              class=\"form-input\"\r\n              required\r\n            />\r\n          </div>\r\n          <div class=\"form-group\">\r\n            <input\r\n              v-model=\"registerForm.password\"\r\n              type=\"password\"\r\n              placeholder=\"密码\"\r\n              class=\"form-input\"\r\n              required\r\n            />\r\n          </div>\r\n          <div class=\"form-group\">\r\n            <input\r\n              v-model=\"registerForm.confirmPassword\"\r\n              type=\"password\"\r\n              placeholder=\"确认密码\"\r\n              class=\"form-input\"\r\n              required\r\n            />\r\n          </div>\r\n          <button type=\"submit\" class=\"register-submit-btn\" :disabled=\"isRegistering\">\r\n            {{ isRegistering ? '注册中...' : '注册' }}\r\n          </button>\r\n        </form>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 消息提示 -->\r\n    <div v-if=\"message.show\" :class=\"['message', message.type]\">\r\n      {{ message.text }}\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport md5 from \"js-md5\";\r\nimport { useUserStore } from '@/store/index';\r\n\r\nexport default {\r\n  name: 'LoginView',\r\n\r\n  components: {},\r\n\r\n  data() {\r\n    return {\r\n      loginForm: {\r\n        username: '',\r\n        password: ''\r\n      },\r\n      registerForm: {\r\n        username: '',\r\n        email: '',\r\n        password: '',\r\n        confirmPassword: ''\r\n      },\r\n      rememberMe: false,\r\n      showRegister: false,\r\n      isLoading: false,\r\n      isRegistering: false,\r\n      usernameError: '',\r\n      passwordError: '',\r\n      message: {\r\n        show: false,\r\n        type: '',\r\n        text: ''\r\n      }\r\n      // 移除模拟用户数据，使用真实API\r\n    };\r\n  },\r\n\r\n  computed: {},\r\n\r\n  watch: {},\r\n\r\n  methods: {\r\n    // 验证用户名\r\n    validateUsername() {\r\n      if (!this.loginForm.username.trim()) {\r\n        this.usernameError = '请输入用户名';\r\n      } else {\r\n        this.usernameError = '';\r\n      }\r\n    },\r\n\r\n    // 验证密码\r\n    validatePassword() {\r\n      if (!this.loginForm.password.trim()) {\r\n        this.passwordError = '请输入密码';\r\n      } else {\r\n        this.passwordError = '';\r\n      }\r\n    },\r\n\r\n    // 登录API\r\n    async loginAPI(username, password) {\r\n      try {\r\n        const response = await fetch('http://127.0.0.1:9197/userAPI/login', {\r\n          method: 'POST',\r\n          headers: {\r\n            'Content-Type': 'application/json',\r\n          },\r\n          body: JSON.stringify({\r\n            username: username,\r\n            password: md5(password)\r\n          })\r\n        });\r\n\r\n        if (!response.ok) {\r\n          throw new Error(`HTTP error! status: ${response.status}`);\r\n        }\r\n\r\n        const data = await response.json();\r\n        return data;\r\n      } catch (error) {\r\n        throw new Error('登录请求失败: ' + error.message);\r\n      }\r\n    },\r\n\r\n    // 注册API（如果有注册接口的话）\r\n    async registerAPI(userData) {\r\n      try {\r\n        const response = await fetch('http://127.0.0.1:9197/userAPI/register', {\r\n          method: 'POST',\r\n          headers: {\r\n            'Content-Type': 'application/json',\r\n          },\r\n          body: JSON.stringify({\r\n            username: userData.username,\r\n            password: md5(userData.password)\r\n          })\r\n        });\r\n\r\n        if (!response.ok) {\r\n          throw new Error(`HTTP error! status: ${response.status}`);\r\n        }\r\n\r\n        const data = await response.json();\r\n        return data;\r\n      } catch (error) {\r\n        throw new Error('注册请求失败: ' + error.message);\r\n      }\r\n    },\r\n\r\n    // 处理登录\r\n    async handleLogin() {\r\n      this.validateUsername();\r\n      this.validatePassword();\r\n      \r\n      if (this.usernameError || this.passwordError) {\r\n        return;\r\n      }\r\n\r\n      this.isLoading = true;\r\n      \r\n      try {\r\n        const response = await this.loginAPI(\r\n          this.loginForm.username,\r\n          this.loginForm.password\r\n        );\r\n        \r\n      \r\n\r\n        // 根据后端返回的数据结构处理\r\n        if (Number(response.code) === 200) {\r\n          this.showMessage('success', '登录成功！');\r\n          console.log('完整响应:', response);\r\n\r\n          // 保存token和用户信息到localStorage\r\n          const token = response.data?.token || 'login-success-' + Date.now();\r\n          const user = response.data || {\r\n            username: this.loginForm.username,\r\n            role: 'user',\r\n            id: Date.now()\r\n          };\r\n\r\n          // 使用用户store保存登录状态\r\n          const { useUserStore } = await import('@/store/index');\r\n          const userStore = useUserStore();\r\n          userStore.login(user, token);\r\n\r\n          console.log('登录成功，准备跳转到主页');\r\n          this.$router.push('/goods');\r\n          \r\n        } else {\r\n          // 登录失败\r\n          const errorMsg = response.message || response.msg || '登录失败，请重试';\r\n          this.showMessage('error', errorMsg);\r\n        }\r\n        \r\n      } catch (error) {\r\n        console.error('登录错误:', error);\r\n        this.showMessage('error', error.message || '网络错误，请稍后重试');\r\n      } finally {\r\n        this.isLoading = false;\r\n      }\r\n    },\r\n\r\n    // 处理注册\r\n    async handleRegister() {\r\n      if (this.registerForm.password !== this.registerForm.confirmPassword) {\r\n        this.showMessage('error', '两次输入的密码不一致');\r\n        return;\r\n      }\r\n\r\n      this.isRegistering = true;\r\n\r\n      try {\r\n        const response = await this.registerAPI({\r\n          username: this.registerForm.username,\r\n          password: this.registerForm.password\r\n        });\r\n\r\n        // 根据后端返回的数据结构处理\r\n        if (Number(response.code) === 200) {\r\n          this.showMessage('success', '注册成功！请登录');\r\n          this.closeRegisterForm();\r\n          this.resetRegisterForm();\r\n        } else {\r\n          const errorMsg = response.message || response.msg || '注册失败，请重试';\r\n          this.showMessage('error', errorMsg);\r\n        }\r\n\r\n      } catch (error) {\r\n        console.error('注册错误:', error);\r\n        this.showMessage('error', error.message || '网络错误，请稍后重试');\r\n      } finally {\r\n        this.isRegistering = false;\r\n      }\r\n    },\r\n\r\n    // 显示注册表单\r\n    showRegisterForm() {\r\n      this.showRegister = true;\r\n    },\r\n\r\n    // 关闭注册表单\r\n    closeRegisterForm() {\r\n      this.showRegister = false;\r\n    },\r\n\r\n    // 重置注册表单\r\n    resetRegisterForm() {\r\n      this.registerForm = {\r\n        username: '',\r\n        password: '',\r\n        confirmPassword: ''\r\n      };\r\n    },\r\n\r\n    // 忘记密码\r\n    showForgotPassword() {\r\n      this.showMessage('info', '请联系管理员重置密码');\r\n    },\r\n\r\n    // 第三方登录\r\n    thirdPartyLogin(type) {\r\n      this.showMessage('info', `${type}登录功能开发中...`);\r\n    },\r\n\r\n    // 显示消息\r\n    showMessage(type, text) {\r\n      this.message = { show: true, type, text };\r\n      setTimeout(() => {\r\n        this.message.show = false;\r\n      }, 3000);\r\n    }\r\n  },\r\n\r\n  created() {\r\n    // 检查是否已经登录\r\n    const token = localStorage.getItem('token');\r\n    if (token) {\r\n      console.log('用户已登录');\r\n    }\r\n  },\r\n\r\n  mounted() {},\r\n\r\n  beforeUpdate() {},\r\n\r\n  updated() {},\r\n\r\n  beforeUnmount() {},\r\n\r\n  unmounted() {},\r\n\r\n  activated() {},\r\n\r\n  deactivated() {}\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n* {\r\n  margin: 0;\r\n  padding: 0;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.login-container {\r\n  min-height: 100vh;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  padding: 20px;\r\n  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;\r\n}\r\n\r\n.login-card {\r\n  background: white;\r\n  border-radius: 20px;\r\n  padding: 40px;\r\n  width: 100%;\r\n  max-width: 400px;\r\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\r\n  position: relative;\r\n}\r\n\r\n.login-header {\r\n  text-align: center;\r\n  margin-bottom: 30px;\r\n}\r\n\r\n.login-header h2 {\r\n  color: #333;\r\n  font-size: 24px;\r\n  font-weight: 600;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.subtitle {\r\n  color: #666;\r\n  font-size: 14px;\r\n}\r\n\r\n.login-form {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.form-group {\r\n  margin-bottom: 20px;\r\n  position: relative;\r\n}\r\n\r\n.form-input {\r\n  width: 100%;\r\n  padding: 15px;\r\n  border: 2px solid #e1e5e9;\r\n  border-radius: 10px;\r\n  font-size: 14px;\r\n  transition: all 0.3s ease;\r\n  outline: none;\r\n}\r\n\r\n.form-input:focus {\r\n  border-color: #667eea;\r\n  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\r\n}\r\n\r\n.form-input.error {\r\n  border-color: #ff4757;\r\n}\r\n\r\n.error-text {\r\n  color: #ff4757;\r\n  font-size: 12px;\r\n  position: absolute;\r\n  top: 100%;\r\n  left: 0;\r\n  margin-top: 5px;\r\n}\r\n\r\n.form-options {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 25px;\r\n}\r\n\r\n.checkbox-wrapper {\r\n  display: flex;\r\n  align-items: center;\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n  color: #666;\r\n}\r\n\r\n.checkbox-wrapper input[type=\"checkbox\"] {\r\n  margin-right: 8px;\r\n}\r\n\r\n.forgot-link {\r\n  color: #667eea;\r\n  text-decoration: none;\r\n  font-size: 14px;\r\n  transition: color 0.3s ease;\r\n}\r\n\r\n.forgot-link:hover {\r\n  color: #5a67d8;\r\n}\r\n\r\n.login-btn {\r\n  width: 100%;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n  border: none;\r\n  padding: 15px;\r\n  border-radius: 10px;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.login-btn:hover:not(:disabled) {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);\r\n}\r\n\r\n.login-btn:disabled {\r\n  opacity: 0.7;\r\n  cursor: not-allowed;\r\n}\r\n\r\n.register-section {\r\n  text-align: center;\r\n  margin: 20px 0;\r\n  font-size: 14px;\r\n  color: #666;\r\n}\r\n\r\n.register-btn {\r\n  background: none;\r\n  border: none;\r\n  color: #667eea;\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n  margin-left: 5px;\r\n  text-decoration: underline;\r\n}\r\n\r\n.divider {\r\n  text-align: center;\r\n  margin: 30px 0 20px;\r\n  position: relative;\r\n  color: #999;\r\n  font-size: 14px;\r\n}\r\n\r\n.divider::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 0;\r\n  right: 0;\r\n  height: 1px;\r\n  background: #e1e5e9;\r\n  z-index: 1;\r\n}\r\n\r\n.divider span {\r\n  background: white;\r\n  padding: 0 15px;\r\n  position: relative;\r\n  z-index: 2;\r\n}\r\n\r\n.social-login {\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: 15px;\r\n}\r\n\r\n.social-btn {\r\n  width: 50px;\r\n  height: 50px;\r\n  border: none;\r\n  border-radius: 50%;\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.social-btn:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.social-btn.wechat {\r\n  background: #07c160;\r\n}\r\n\r\n.social-btn.weibo {\r\n  background: #e6162d;\r\n}\r\n\r\n.social-btn.qq {\r\n  background: #1296db;\r\n}\r\n\r\n.social-icon {\r\n  width: 24px;\r\n  height: 24px;\r\n  border-radius: 50%;\r\n}\r\n\r\n.wechat-icon {\r\n  background: #fff;\r\n}\r\n\r\n.weibo-icon {\r\n  background: #fff;\r\n}\r\n\r\n.qq-icon {\r\n  background: #fff;\r\n}\r\n\r\n/* 弹窗样式 */\r\n.modal-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.5);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  z-index: 1000;\r\n}\r\n\r\n.modal-content {\r\n  background: white;\r\n  border-radius: 15px;\r\n  padding: 30px;\r\n  width: 90%;\r\n  max-width: 400px;\r\n  position: relative;\r\n}\r\n\r\n.modal-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.modal-header h3 {\r\n  color: #333;\r\n  font-size: 20px;\r\n}\r\n\r\n.close-btn {\r\n  background: none;\r\n  border: none;\r\n  font-size: 24px;\r\n  cursor: pointer;\r\n  color: #999;\r\n}\r\n\r\n.register-submit-btn {\r\n  width: 100%;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n  border: none;\r\n  padding: 15px;\r\n  border-radius: 10px;\r\n  font-size: 16px;\r\n  cursor: pointer;\r\n  margin-top: 10px;\r\n}\r\n\r\n.register-submit-btn:hover:not(:disabled) {\r\n  opacity: 0.9;\r\n}\r\n\r\n/* 消息提示 */\r\n.message {\r\n  position: fixed;\r\n  top: 20px;\r\n  right: 20px;\r\n  padding: 15px 20px;\r\n  border-radius: 8px;\r\n  color: white;\r\n  font-weight: 500;\r\n  z-index: 2000;\r\n  animation: slideIn 0.3s ease;\r\n}\r\n\r\n.message.success {\r\n  background: #27ae60;\r\n}\r\n\r\n.message.error {\r\n  background: #e74c3c;\r\n}\r\n\r\n.message.info {\r\n  background: #3498db;\r\n}\r\n\r\n@keyframes slideIn {\r\n  from {\r\n    transform: translateX(100%);\r\n    opacity: 0;\r\n  }\r\n  to {\r\n    transform: translateX(0);\r\n    opacity: 1;\r\n  }\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 480px) {\r\n  .login-card {\r\n    padding: 30px 20px;\r\n    margin: 10px;\r\n  }\r\n  \r\n  .login-header h2 {\r\n    font-size: 20px;\r\n  }\r\n}\r\n</style>"], "mappings": ";;EAEOA,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAY;;EAOdA,KAAK,EAAC;AAAY;;;EASMA,KAAK,EAAC;;;EAG9BA,KAAK,EAAC;AAAY;;;EASMA,KAAK,EAAC;;;EAG9BA,KAAK,EAAC;AAAc;;EAChBA,KAAK,EAAC;AAAkB;;;EAa9BA,KAAK,EAAC;AAAkB;;EASxBA,KAAK,EAAC;AAAc;;EAgBlBA,KAAK,EAAC;AAAc;;EAKlBA,KAAK,EAAC;AAAY;;EASlBA,KAAK,EAAC;AAAY;;EASlBA,KAAK,EAAC;AAAY;;;uBA9F/BC,mBAAA,CAkHM,OAlHNC,UAkHM,GAjHJC,mBAAA,CAiEM,OAjENC,UAiEM,G,4BAhEJD,mBAAA,CAGM;IAHDH,KAAK,EAAC;EAAc,IACvBG,mBAAA,CAAa,YAAT,MAAI,GACRA,mBAAA,CAAgC;IAA7BH,KAAK,EAAC;EAAU,GAAC,UAAQ,E,qBAE/BK,mBAAA,UAAa,EACZF,mBAAA,CAqCO;IArCAG,QAAM,EAAAC,MAAA,QAAAA,MAAA,MAAAC,cAAA,KAAAC,IAAA,KAAUC,QAAA,CAAAC,WAAA,IAAAD,QAAA,CAAAC,WAAA,IAAAF,IAAA,CAAW;IAAET,KAAK,EAAC;MACxCG,mBAAA,CAUM,OAVNS,UAUM,G,gBATJT,mBAAA,CAOE;+DANSU,KAAA,CAAAC,SAAS,CAACC,QAAQ,GAAAC,MAAA;IAC3BC,IAAI,EAAC,MAAM;IACXC,WAAW,EAAC,KAAK;IACjBlB,KAAK,EAAAmB,eAAA,EAAC,YAAY;MAAA,SACCN,KAAA,CAAAO;IAAa;IAC/BC,MAAI,EAAAd,MAAA,QAAAA,MAAA,UAAAE,IAAA,KAAEC,QAAA,CAAAY,gBAAA,IAAAZ,QAAA,CAAAY,gBAAA,IAAAb,IAAA,CAAgB;2DALdI,KAAA,CAAAC,SAAS,CAACC,QAAQ,E,GAOjBF,KAAA,CAAAO,aAAa,I,cAAzBnB,mBAAA,CAAwE,QAAxEsB,UAAwE,EAAAC,gBAAA,CAAvBX,KAAA,CAAAO,aAAa,oB,qCAGhEjB,mBAAA,CAUM,OAVNsB,UAUM,G,gBATJtB,mBAAA,CAOE;+DANSU,KAAA,CAAAC,SAAS,CAACY,QAAQ,GAAAV,MAAA;IAC3BC,IAAI,EAAC,UAAU;IACfC,WAAW,EAAC,IAAI;IAChBlB,KAAK,EAAAmB,eAAA,EAAC,YAAY;MAAA,SACCN,KAAA,CAAAc;IAAa;IAC/BN,MAAI,EAAAd,MAAA,QAAAA,MAAA,UAAAE,IAAA,KAAEC,QAAA,CAAAkB,gBAAA,IAAAlB,QAAA,CAAAkB,gBAAA,IAAAnB,IAAA,CAAgB;2DALdI,KAAA,CAAAC,SAAS,CAACY,QAAQ,E,GAOjBb,KAAA,CAAAc,aAAa,I,cAAzB1B,mBAAA,CAAwE,QAAxE4B,UAAwE,EAAAL,gBAAA,CAAvBX,KAAA,CAAAc,aAAa,oB,qCAGhExB,mBAAA,CAOM,OAPN2B,UAOM,GANJ3B,mBAAA,CAIQ,SAJR4B,UAIQ,G,gBAHN5B,mBAAA,CAA8C;+DAA9BU,KAAA,CAAAmB,UAAU,GAAAhB,MAAA;IAAEC,IAAI,EAAC;qDAAjBJ,KAAA,CAAAmB,UAAU,E,+BAC1B7B,mBAAA,CAA+B;IAAzBH,KAAK,EAAC;EAAW,4B,6CAAQ,OAEjC,oB,GACAG,mBAAA,CAA6E;IAA1E8B,IAAI,EAAC,GAAG;IAACjC,KAAK,EAAC,aAAa;IAAEkC,OAAK,EAAA3B,MAAA,QAAAA,MAAA,MAAAC,cAAA,KAAAC,IAAA,KAAUC,QAAA,CAAAyB,kBAAA,IAAAzB,QAAA,CAAAyB,kBAAA,IAAA1B,IAAA,CAAkB;KAAE,OAAK,E,GAG3EN,mBAAA,CAES;IAFDc,IAAI,EAAC,QAAQ;IAACjB,KAAK,EAAC,WAAW;IAAEoC,QAAQ,EAAEvB,KAAA,CAAAwB;sBAC9CxB,KAAA,CAAAwB,SAAS,2CAAAC,UAAA,E,4BAIhBnC,mBAAA,CAGM,OAHNoC,WAGM,G,4BAFJpC,mBAAA,CAAkB,cAAZ,OAAK,qBACXA,mBAAA,CAAoE;IAA3D+B,OAAK,EAAA3B,MAAA,QAAAA,MAAA,UAAAE,IAAA,KAAEC,QAAA,CAAA8B,gBAAA,IAAA9B,QAAA,CAAA8B,gBAAA,IAAA/B,IAAA,CAAgB;IAAET,KAAK,EAAC;KAAe,MAAI,E,+BAG7DG,mBAAA,CAEM;IAFDH,KAAK,EAAC;EAAS,IAClBG,mBAAA,CAAkB,cAAZ,OAAK,E,qBAGbA,mBAAA,CAUM,OAVNsC,WAUM,GATJtC,mBAAA,CAES;IAFDH,KAAK,EAAC,mBAAmB;IAAEkC,OAAK,EAAA3B,MAAA,QAAAA,MAAA,MAAAS,MAAA,IAAEN,QAAA,CAAAgC,eAAe;uCACvDvC,mBAAA,CAA2C;IAAtCH,KAAK,EAAC;EAAyB,0B,MAEtCG,mBAAA,CAES;IAFDH,KAAK,EAAC,kBAAkB;IAAEkC,OAAK,EAAA3B,MAAA,QAAAA,MAAA,MAAAS,MAAA,IAAEN,QAAA,CAAAgC,eAAe;uCACtDvC,mBAAA,CAA0C;IAArCH,KAAK,EAAC;EAAwB,0B,MAErCG,mBAAA,CAES;IAFDH,KAAK,EAAC,eAAe;IAAEkC,OAAK,EAAA3B,MAAA,SAAAA,MAAA,OAAAS,MAAA,IAAEN,QAAA,CAAAgC,eAAe;uCACnDvC,mBAAA,CAAuC;IAAlCH,KAAK,EAAC;EAAqB,0B,UAKtCK,mBAAA,UAAa,EACFQ,KAAA,CAAA8B,YAAY,I,cAAvB1C,mBAAA,CAuCM;;IAvCmBD,KAAK,EAAC,eAAe;IAAEkC,OAAK,EAAA3B,MAAA,SAAAA,MAAA,WAAAE,IAAA,KAAEC,QAAA,CAAAkC,iBAAA,IAAAlC,QAAA,CAAAkC,iBAAA,IAAAnC,IAAA,CAAiB;MACtEN,mBAAA,CAqCM;IArCDH,KAAK,EAAC,eAAe;IAAEkC,OAAK,EAAA3B,MAAA,SAAAA,MAAA,OAAAC,cAAA,CAAN,QAAW;MACpCL,mBAAA,CAGM,OAHN0C,WAGM,G,4BAFJ1C,mBAAA,CAAa,YAAT,MAAI,qBACRA,mBAAA,CAAqE;IAA5D+B,OAAK,EAAA3B,MAAA,SAAAA,MAAA,WAAAE,IAAA,KAAEC,QAAA,CAAAkC,iBAAA,IAAAlC,QAAA,CAAAkC,iBAAA,IAAAnC,IAAA,CAAiB;IAAET,KAAK,EAAC;KAAY,GAAO,E,GAE9DG,mBAAA,CA+BO;IA/BAG,QAAM,EAAAC,MAAA,SAAAA,MAAA,OAAAC,cAAA,KAAAC,IAAA,KAAUC,QAAA,CAAAoC,cAAA,IAAApC,QAAA,CAAAoC,cAAA,IAAArC,IAAA,CAAc;IAAET,KAAK,EAAC;MAC3CG,mBAAA,CAQM,OARN4C,WAQM,G,gBAPJ5C,mBAAA,CAME;iEALSU,KAAA,CAAAmC,YAAY,CAACjC,QAAQ,GAAAC,MAAA;IAC9BC,IAAI,EAAC,MAAM;IACXC,WAAW,EAAC,KAAK;IACjBlB,KAAK,EAAC,YAAY;IAClBiD,QAAQ,EAAR;iDAJSpC,KAAA,CAAAmC,YAAY,CAACjC,QAAQ,E,KAOlCZ,mBAAA,CAQM,OARN+C,WAQM,G,gBAPJ/C,mBAAA,CAME;iEALSU,KAAA,CAAAmC,YAAY,CAACtB,QAAQ,GAAAV,MAAA;IAC9BC,IAAI,EAAC,UAAU;IACfC,WAAW,EAAC,IAAI;IAChBlB,KAAK,EAAC,YAAY;IAClBiD,QAAQ,EAAR;iDAJSpC,KAAA,CAAAmC,YAAY,CAACtB,QAAQ,E,KAOlCvB,mBAAA,CAQM,OARNgD,WAQM,G,gBAPJhD,mBAAA,CAME;iEALSU,KAAA,CAAAmC,YAAY,CAACI,eAAe,GAAApC,MAAA;IACrCC,IAAI,EAAC,UAAU;IACfC,WAAW,EAAC,MAAM;IAClBlB,KAAK,EAAC,YAAY;IAClBiD,QAAQ,EAAR;iDAJSpC,KAAA,CAAAmC,YAAY,CAACI,eAAe,E,KAOzCjD,mBAAA,CAES;IAFDc,IAAI,EAAC,QAAQ;IAACjB,KAAK,EAAC,qBAAqB;IAAEoC,QAAQ,EAAEvB,KAAA,CAAAwC;sBACxDxC,KAAA,CAAAwC,aAAa,0CAAAC,WAAA,E,qEAMxBjD,mBAAA,UAAa,EACFQ,KAAA,CAAA0C,OAAO,CAACC,IAAI,I,cAAvBvD,mBAAA,CAEM;;IAFoBD,KAAK,EAAAmB,eAAA,aAAcN,KAAA,CAAA0C,OAAO,CAACtC,IAAI;sBACpDJ,KAAA,CAAA0C,OAAO,CAACE,IAAI,2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}