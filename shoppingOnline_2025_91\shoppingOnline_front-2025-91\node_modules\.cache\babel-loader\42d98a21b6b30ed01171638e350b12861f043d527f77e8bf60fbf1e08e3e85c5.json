{"ast": null, "code": "import { createRouter, createWebHistory } from 'vue-router';\nimport request from '../utils/request';\n\n//requireAuth: 是否需要检查登录\nconst routes = [{\n  path: '/',\n  name: 'MainLayout',\n  component: () => import('@/views/MainLayout.vue'),\n  redirect: '/goods',\n  children: [{\n    path: 'goods',\n    name: \"GoodsView\",\n    component: () => import('@/views/goods.vue'),\n    meta: {\n      title: '商品列表',\n      requireAuth: false\n    }\n  }, {\n    path: 'home',\n    name: \"HomeView\",\n    component: () => import('@/views/Home.vue'),\n    meta: {\n      title: '首页',\n      requireAuth: false\n    }\n  }, {\n    path: 'test',\n    name: \"TestPage\",\n    component: () => import('@/views/TestPage.vue'),\n    meta: {\n      title: '功能测试',\n      requireAuth: false\n    }\n  }]\n},\n//前台\n// {\n//   path: '/',\n//   name: 'front',\n//   redirect: \"/topview\",\n//   component: () => import('../views/front/Front.vue'),\n//   meta: {title:'在线商城', path: '在线商城', requireAuth: false},\n//   children: [\n\n//   ]\n// },\n//后台\n// {\n//   path: '/manage',\n//   name: 'manage',\n//   component: () => import('../views/manage/Manage.vue'),\n//   redirect: \"/manage/home\",\n//   meta: {title:'后台', path: '后台',requireAuth: true},\n//   children: [\n\n//   ]\n// },\n{\n  path: '/login',\n  name: 'LoginView',\n  meta: {\n    title: '登录',\n    requireAuth: false\n  },\n  component: () => import(/* webpackChunkName: \"about\" */'../views/Login.vue')\n}, {\n  path: '/register',\n  name: 'RegisterView',\n  meta: {\n    title: '注册',\n    requireAuth: false\n  },\n  component: () => import(/* webpackChunkName: \"about\" */'../views/Register.vue')\n}, {\n  path: '/*',\n  name: 'NotFound',\n  meta: {\n    title: '找不到页面'\n  },\n  component: () => import(/* webpackChunkName: \"about\" */'../views/404NotFound.vue')\n}];\n// 创建路由器\nconst router = createRouter({\n  history: createWebHistory(),\n  // 替代 mode: 'history'\n  routes\n});\n\n//beforeEach是router的钩子函数，在进入路由前执行\nrouter.beforeEach((to, from, next) => {\n  // if(to.path === '/manage'){\n  //   let user = localStorage.getItem(\"user\");\n  //   if(!user.token){\n  //     next('/login');\n  //   }\n  // }\n  let role;\n  let allow = false;\n  if (to.meta.requireAuth === true) {\n    //在后台获得该用户的身份\n    request.post(\"http://localhost:9191/role\").then(res => {\n      if (res.code === '200') {\n        role = res.data;\n        console.log('您的身份是：' + role);\n        if (role === 'admin') {\n          allow = true;\n        } else if (role === 'user') {\n          alert(\"您没有权限\");\n          allow = false;\n          next(\"/\");\n        }\n      } else {\n        //查询身份失败\n        alert(res.msg);\n        next('/login');\n      }\n      //放行\n      if (allow === true) {\n        //设置网页title\n        if (to.meta.title) {\n          document.title = to.meta.title;\n        } else {\n          document.title = '未知页面';\n        }\n        next();\n      }\n    });\n  } else {\n    //不需要判断权限\n    if (to.meta.requireLogin === true) {\n      if (!isLogin()) {\n        next('/login');\n      }\n    }\n    if (to.meta.title) {\n      document.title = to.meta.title;\n    } else {\n      document.title = '未知页面';\n    }\n    next();\n  }\n});\nfunction isLogin() {\n  let user = localStorage.getItem(\"user\");\n  if (user) {\n    return true;\n  } else {\n    return false;\n  }\n}\n/*import 'vue-vibe'*/\nexport default router;", "map": {"version": 3, "names": ["createRouter", "createWebHistory", "request", "routes", "path", "name", "component", "redirect", "children", "meta", "title", "requireAuth", "router", "history", "beforeEach", "to", "from", "next", "role", "allow", "post", "then", "res", "code", "data", "console", "log", "alert", "msg", "document", "requireLogin", "is<PERSON>ogin", "user", "localStorage", "getItem"], "sources": ["D:/2025_down/project/shoppingOnline_2025_91/shoppingOnline_front-2025-91/src/router/index.js"], "sourcesContent": ["import { createRouter, createWebHistory } from 'vue-router'\r\nimport request from '../utils/request';\r\n\r\n\r\n//requireAuth: 是否需要检查登录\r\nconst routes = [\r\n  {\r\n    path: '/',\r\n    name: 'MainLayout',\r\n    component: () => import('@/views/MainLayout.vue'),\r\n    redirect: '/goods',\r\n    children: [\r\n      {\r\n        path: 'goods',\r\n        name: \"GoodsView\",\r\n        component: () => import('@/views/goods.vue'),\r\n        meta: { title: '商品列表', requireAuth: false }\r\n      },\r\n      {\r\n        path: 'home',\r\n        name: \"HomeView\",\r\n        component: () => import('@/views/Home.vue'),\r\n        meta: { title: '首页', requireAuth: false }\r\n      },\r\n      {\r\n        path: 'test',\r\n        name: \"TestPage\",\r\n        component: () => import('@/views/TestPage.vue'),\r\n        meta: { title: '功能测试', requireAuth: false }\r\n      }\r\n    ]\r\n  },\r\n   \r\n    //前台\r\n  // {\r\n  //   path: '/',\r\n  //   name: 'front',\r\n  //   redirect: \"/topview\",\r\n  //   component: () => import('../views/front/Front.vue'),\r\n  //   meta: {title:'在线商城', path: '在线商城', requireAuth: false},\r\n  //   children: [\r\n      \r\n\r\n  //   ]\r\n  // },\r\n    //后台\r\n  // {\r\n  //   path: '/manage',\r\n  //   name: 'manage',\r\n  //   component: () => import('../views/manage/Manage.vue'),\r\n  //   redirect: \"/manage/home\",\r\n  //   meta: {title:'后台', path: '后台',requireAuth: true},\r\n  //   children: [\r\n     \r\n\r\n  //   ]\r\n  // },\r\n  {\r\n    path: '/login',\r\n    name: 'LoginView',\r\n    meta: {\r\n      title: '登录',\r\n      requireAuth: false,\r\n    },\r\n    component: () => import(/* webpackChunkName: \"about\" */ '../views/Login.vue')\r\n  },\r\n  {\r\n    path: '/register',\r\n    name: 'RegisterView',\r\n    meta: {\r\n      title: '注册',requireAuth: false,\r\n    },\r\n    component: () => import(/* webpackChunkName: \"about\" */ '../views/Register.vue')\r\n  },\r\n  {\r\n    path: '/*',\r\n    name: 'NotFound',\r\n    meta: {\r\n      title: '找不到页面'\r\n    },\r\n    component: () => import(/* webpackChunkName: \"about\" */ '../views/404NotFound.vue')\r\n  },\r\n]\r\n// 创建路由器\r\nconst router = createRouter({\r\n  history: createWebHistory(), // 替代 mode: 'history'\r\n  routes\r\n})\r\n\r\n\r\n//beforeEach是router的钩子函数，在进入路由前执行\r\nrouter.beforeEach((to, from, next) => {\r\n  // if(to.path === '/manage'){\r\n  //   let user = localStorage.getItem(\"user\");\r\n  //   if(!user.token){\r\n  //     next('/login');\r\n  //   }\r\n  // }\r\n  let role;\r\n  let allow = false;\r\n  if(to.meta.requireAuth===true){\r\n    //在后台获得该用户的身份\r\n    request.post(\"http://localhost:9191/role\").then(res=>{\r\n      if(res.code==='200'){\r\n        role = res.data;\r\n        console.log('您的身份是：'+role);\r\n        if(role === 'admin'){\r\n          allow = true;\r\n        }\r\n        else if(role==='user'){\r\n            alert(\"您没有权限\");\r\n            allow = false;\r\n            next(\"/\")\r\n        }\r\n      }\r\n      else{  //查询身份失败\r\n        alert(res.msg);\r\n        next('/login');\r\n      }\r\n      //放行\r\n      if(allow === true){\r\n        //设置网页title\r\n        if (to.meta.title) {\r\n          document.title = to.meta.title\r\n        } else {\r\n          document.title ='未知页面'\r\n        }\r\n        next()\r\n      }\r\n    }\r\n    )\r\n  }\r\n  else{    //不需要判断权限\r\n    if(to.meta.requireLogin===true){\r\n      if(!isLogin()){\r\n        next('/login');\r\n      }\r\n    }\r\n    if (to.meta.title) {\r\n      document.title = to.meta.title\r\n    } else {\r\n      document.title ='未知页面'\r\n    }\r\n    next()\r\n  }\r\n\r\n})\r\n\r\nfunction isLogin() {\r\n  let user = localStorage.getItem(\"user\");\r\n  if(user){\r\n    return true;\r\n  }else{\r\n    return false;\r\n  }\r\n}\r\n/*import 'vue-vibe'*/\r\nexport default router\r\n"], "mappings": "AAAA,SAASA,YAAY,EAAEC,gBAAgB,QAAQ,YAAY;AAC3D,OAAOC,OAAO,MAAM,kBAAkB;;AAGtC;AACA,MAAMC,MAAM,GAAG,CACb;EACEC,IAAI,EAAE,GAAG;EACTC,IAAI,EAAE,YAAY;EAClBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,wBAAwB,CAAC;EACjDC,QAAQ,EAAE,QAAQ;EAClBC,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,OAAO;IACbC,IAAI,EAAE,WAAW;IACjBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,mBAAmB,CAAC;IAC5CG,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,WAAW,EAAE;IAAM;EAC5C,CAAC,EACD;IACEP,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,UAAU;IAChBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,kBAAkB,CAAC;IAC3CG,IAAI,EAAE;MAAEC,KAAK,EAAE,IAAI;MAAEC,WAAW,EAAE;IAAM;EAC1C,CAAC,EACD;IACEP,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,UAAU;IAChBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,sBAAsB,CAAC;IAC/CG,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,WAAW,EAAE;IAAM;EAC5C,CAAC;AAEL,CAAC;AAEC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;EACEP,IAAI,EAAE,QAAQ;EACdC,IAAI,EAAE,WAAW;EACjBI,IAAI,EAAE;IACJC,KAAK,EAAE,IAAI;IACXC,WAAW,EAAE;EACf,CAAC;EACDL,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,+BAAgC,oBAAoB;AAC9E,CAAC,EACD;EACEF,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,cAAc;EACpBI,IAAI,EAAE;IACJC,KAAK,EAAE,IAAI;IAACC,WAAW,EAAE;EAC3B,CAAC;EACDL,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,+BAAgC,uBAAuB;AACjF,CAAC,EACD;EACEF,IAAI,EAAE,IAAI;EACVC,IAAI,EAAE,UAAU;EAChBI,IAAI,EAAE;IACJC,KAAK,EAAE;EACT,CAAC;EACDJ,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,+BAAgC,0BAA0B;AACpF,CAAC,CACF;AACD;AACA,MAAMM,MAAM,GAAGZ,YAAY,CAAC;EAC1Ba,OAAO,EAAEZ,gBAAgB,CAAC,CAAC;EAAE;EAC7BE;AACF,CAAC,CAAC;;AAGF;AACAS,MAAM,CAACE,UAAU,CAAC,CAACC,EAAE,EAAEC,IAAI,EAAEC,IAAI,KAAK;EACpC;EACA;EACA;EACA;EACA;EACA;EACA,IAAIC,IAAI;EACR,IAAIC,KAAK,GAAG,KAAK;EACjB,IAAGJ,EAAE,CAACN,IAAI,CAACE,WAAW,KAAG,IAAI,EAAC;IAC5B;IACAT,OAAO,CAACkB,IAAI,CAAC,4BAA4B,CAAC,CAACC,IAAI,CAACC,GAAG,IAAE;MACnD,IAAGA,GAAG,CAACC,IAAI,KAAG,KAAK,EAAC;QAClBL,IAAI,GAAGI,GAAG,CAACE,IAAI;QACfC,OAAO,CAACC,GAAG,CAAC,QAAQ,GAACR,IAAI,CAAC;QAC1B,IAAGA,IAAI,KAAK,OAAO,EAAC;UAClBC,KAAK,GAAG,IAAI;QACd,CAAC,MACI,IAAGD,IAAI,KAAG,MAAM,EAAC;UAClBS,KAAK,CAAC,OAAO,CAAC;UACdR,KAAK,GAAG,KAAK;UACbF,IAAI,CAAC,GAAG,CAAC;QACb;MACF,CAAC,MACG;QAAG;QACLU,KAAK,CAACL,GAAG,CAACM,GAAG,CAAC;QACdX,IAAI,CAAC,QAAQ,CAAC;MAChB;MACA;MACA,IAAGE,KAAK,KAAK,IAAI,EAAC;QAChB;QACA,IAAIJ,EAAE,CAACN,IAAI,CAACC,KAAK,EAAE;UACjBmB,QAAQ,CAACnB,KAAK,GAAGK,EAAE,CAACN,IAAI,CAACC,KAAK;QAChC,CAAC,MAAM;UACLmB,QAAQ,CAACnB,KAAK,GAAE,MAAM;QACxB;QACAO,IAAI,CAAC,CAAC;MACR;IACF,CACA,CAAC;EACH,CAAC,MACG;IAAK;IACP,IAAGF,EAAE,CAACN,IAAI,CAACqB,YAAY,KAAG,IAAI,EAAC;MAC7B,IAAG,CAACC,OAAO,CAAC,CAAC,EAAC;QACZd,IAAI,CAAC,QAAQ,CAAC;MAChB;IACF;IACA,IAAIF,EAAE,CAACN,IAAI,CAACC,KAAK,EAAE;MACjBmB,QAAQ,CAACnB,KAAK,GAAGK,EAAE,CAACN,IAAI,CAACC,KAAK;IAChC,CAAC,MAAM;MACLmB,QAAQ,CAACnB,KAAK,GAAE,MAAM;IACxB;IACAO,IAAI,CAAC,CAAC;EACR;AAEF,CAAC,CAAC;AAEF,SAASc,OAAOA,CAAA,EAAG;EACjB,IAAIC,IAAI,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;EACvC,IAAGF,IAAI,EAAC;IACN,OAAO,IAAI;EACb,CAAC,MAAI;IACH,OAAO,KAAK;EACd;AACF;AACA;AACA,eAAepB,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}