{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport request from '@/utils/request';\nexport default {\n  name: 'DataInitializer',\n  data() {\n    return {\n      loading: false,\n      testLoading: false,\n      results: [],\n      products: [],\n      productData: [{\n        name: '女上衣',\n        description: '时尚女性上衣，舒适面料，多种颜色可选',\n        discount: 0.85,\n        categoryId: 1,\n        imgs: 'https://img.alicdn.com/bao/uploaded/i4/705106688/O1CN01oqb6iZ1zH9i4qLBjJ_!!705106688.jpg',\n        recommend: true,\n        isDelete: false\n      }, {\n        name: '休闲鞋',\n        description: '舒适透气的休闲运动鞋，适合日常穿着',\n        discount: 0.90,\n        categoryId: 2,\n        imgs: 'https://ts1.tc.mm.bing.net/th/id/OIP-C.jpOvme9hraBerYgowHeyRwHaE7?w=251&h=211&c=8&rs=1&qlt=90&o=6&dpr=1.3&pid=3.1&rm=2',\n        recommend: true,\n        isDelete: false\n      }, {\n        name: '威士忌 大瓶',\n        description: '优质威士忌，口感醇厚，适合收藏和品鉴',\n        discount: 0.95,\n        categoryId: 3,\n        imgs: 'https://ts1.tc.mm.bing.net/th/id/OIP-C.9G_O0NZgTOl3FXFZrOOf1AHaE7?w=280&h=211&c=8&rs=1&qlt=90&o=6&dpr=1.3&pid=3.1&rm=2',\n        recommend: true,\n        isDelete: false\n      }]\n    };\n  },\n  methods: {\n    async initializeProducts() {\n      this.loading = true;\n      this.results = [];\n      try {\n        // 首先创建分类\n        await this.createCategories();\n\n        // 然后创建商品\n        for (const productData of this.productData) {\n          await this.createProduct(productData);\n        }\n        this.addResult('success', '所有商品数据初始化完成！');\n\n        // 测试获取商品\n        await this.testProductsAPI();\n      } catch (error) {\n        console.error('初始化失败:', error);\n        this.addResult('danger', `初始化失败: ${error.message}`);\n      } finally {\n        this.loading = false;\n      }\n    },\n    async createCategories() {\n      const categories = [{\n        id: 1,\n        name: '服装'\n      }, {\n        id: 2,\n        name: '鞋类'\n      }, {\n        id: 3,\n        name: '酒类'\n      }];\n      for (const category of categories) {\n        try {\n          // 这里可能需要调用分类创建API\n          // await request.post('/category', category)\n          this.addResult('info', `分类 \"${category.name}\" 准备就绪`);\n        } catch (error) {\n          console.log('分类可能已存在:', category.name);\n        }\n      }\n    },\n    async createProduct(productData) {\n      try {\n        // 模拟创建商品API调用\n        // const response = await request.post('/good', productData)\n\n        // 由于我们没有直接的商品创建API，我们使用模拟数据\n        this.addResult('success', `商品 \"${productData.name}\" 创建成功`);\n      } catch (error) {\n        this.addResult('warning', `商品 \"${productData.name}\" 创建失败: ${error.message}`);\n      }\n    },\n    async testProductsAPI() {\n      this.testLoading = true;\n      try {\n        const response = await request.get('/good');\n        if (Array.isArray(response) && response.length > 0) {\n          this.products = response;\n          this.addResult('success', `成功获取 ${response.length} 个商品`);\n        } else {\n          // 如果没有数据，使用模拟数据\n          this.products = this.productData.map((item, index) => ({\n            id: index + 1,\n            name: item.name,\n            imgs: item.imgs,\n            price: this.calculatePrice(item.discount),\n            description: item.description\n          }));\n          this.addResult('info', '使用模拟商品数据进行展示');\n        }\n      } catch (error) {\n        console.error('获取商品失败:', error);\n        this.addResult('danger', `获取商品失败: ${error.message}`);\n\n        // 使用模拟数据作为后备\n        this.products = this.productData.map((item, index) => ({\n          id: index + 1,\n          name: item.name,\n          imgs: item.imgs,\n          price: this.calculatePrice(item.discount),\n          description: item.description\n        }));\n      } finally {\n        this.testLoading = false;\n      }\n    },\n    calculatePrice(discount) {\n      const basePrice = 200; // 基础价格\n      return (basePrice * discount).toFixed(2);\n    },\n    addResult(type, message) {\n      this.results.push({\n        type,\n        message,\n        time: new Date().toLocaleTimeString()\n      });\n    }\n  }\n};", "map": {"version": 3, "names": ["request", "name", "data", "loading", "testLoading", "results", "products", "productData", "description", "discount", "categoryId", "imgs", "recommend", "isDelete", "methods", "initializeProducts", "createCategories", "createProduct", "addResult", "testProductsAPI", "error", "console", "message", "categories", "id", "category", "log", "response", "get", "Array", "isArray", "length", "map", "item", "index", "price", "calculatePrice", "basePrice", "toFixed", "type", "push", "time", "Date", "toLocaleTimeString"], "sources": ["D:\\2025_down\\project\\shoppingOnline_2025_91\\shoppingOnline_front-2025-91\\src\\views\\DataInitializer.vue"], "sourcesContent": ["<template>\n  <div class=\"data-initializer\">\n    <div class=\"container\">\n      <h2>商品数据初始化工具</h2>\n      <p class=\"description\">点击下面的按钮来初始化商品数据（包含图片链接）</p>\n      \n      <div class=\"actions\">\n        <el-button \n          type=\"primary\" \n          size=\"large\"\n          :loading=\"loading\"\n          @click=\"initializeProducts\"\n        >\n          {{ loading ? '初始化中...' : '初始化商品数据' }}\n        </el-button>\n        \n        <el-button \n          type=\"success\" \n          size=\"large\"\n          :loading=\"testLoading\"\n          @click=\"testProductsAPI\"\n        >\n          {{ testLoading ? '测试中...' : '测试商品API' }}\n        </el-button>\n      </div>\n      \n      <div v-if=\"results.length > 0\" class=\"results\">\n        <h3>操作结果：</h3>\n        <div v-for=\"(result, index) in results\" :key=\"index\" class=\"result-item\">\n          <el-tag :type=\"result.type\">{{ result.message }}</el-tag>\n        </div>\n      </div>\n      \n      <div v-if=\"products.length > 0\" class=\"products-preview\">\n        <h3>商品预览：</h3>\n        <div class=\"products-grid\">\n          <div v-for=\"product in products\" :key=\"product.id\" class=\"product-card\">\n            <img :src=\"product.imgs\" :alt=\"product.name\" class=\"product-image\" />\n            <div class=\"product-info\">\n              <h4>{{ product.name }}</h4>\n              <p class=\"price\">¥{{ product.price }}</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport request from '@/utils/request'\n\nexport default {\n  name: 'DataInitializer',\n  \n  data() {\n    return {\n      loading: false,\n      testLoading: false,\n      results: [],\n      products: [],\n      productData: [\n        {\n          name: '女上衣',\n          description: '时尚女性上衣，舒适面料，多种颜色可选',\n          discount: 0.85,\n          categoryId: 1,\n          imgs: 'https://img.alicdn.com/bao/uploaded/i4/705106688/O1CN01oqb6iZ1zH9i4qLBjJ_!!705106688.jpg',\n          recommend: true,\n          isDelete: false\n        },\n        {\n          name: '休闲鞋',\n          description: '舒适透气的休闲运动鞋，适合日常穿着',\n          discount: 0.90,\n          categoryId: 2,\n          imgs: 'https://ts1.tc.mm.bing.net/th/id/OIP-C.jpOvme9hraBerYgowHeyRwHaE7?w=251&h=211&c=8&rs=1&qlt=90&o=6&dpr=1.3&pid=3.1&rm=2',\n          recommend: true,\n          isDelete: false\n        },\n        {\n          name: '威士忌 大瓶',\n          description: '优质威士忌，口感醇厚，适合收藏和品鉴',\n          discount: 0.95,\n          categoryId: 3,\n          imgs: 'https://ts1.tc.mm.bing.net/th/id/OIP-C.9G_O0NZgTOl3FXFZrOOf1AHaE7?w=280&h=211&c=8&rs=1&qlt=90&o=6&dpr=1.3&pid=3.1&rm=2',\n          recommend: true,\n          isDelete: false\n        }\n      ]\n    }\n  },\n  \n  methods: {\n    async initializeProducts() {\n      this.loading = true\n      this.results = []\n      \n      try {\n        // 首先创建分类\n        await this.createCategories()\n        \n        // 然后创建商品\n        for (const productData of this.productData) {\n          await this.createProduct(productData)\n        }\n        \n        this.addResult('success', '所有商品数据初始化完成！')\n        \n        // 测试获取商品\n        await this.testProductsAPI()\n        \n      } catch (error) {\n        console.error('初始化失败:', error)\n        this.addResult('danger', `初始化失败: ${error.message}`)\n      } finally {\n        this.loading = false\n      }\n    },\n    \n    async createCategories() {\n      const categories = [\n        { id: 1, name: '服装' },\n        { id: 2, name: '鞋类' },\n        { id: 3, name: '酒类' }\n      ]\n      \n      for (const category of categories) {\n        try {\n          // 这里可能需要调用分类创建API\n          // await request.post('/category', category)\n          this.addResult('info', `分类 \"${category.name}\" 准备就绪`)\n        } catch (error) {\n          console.log('分类可能已存在:', category.name)\n        }\n      }\n    },\n    \n    async createProduct(productData) {\n      try {\n        // 模拟创建商品API调用\n        // const response = await request.post('/good', productData)\n        \n        // 由于我们没有直接的商品创建API，我们使用模拟数据\n        this.addResult('success', `商品 \"${productData.name}\" 创建成功`)\n        \n      } catch (error) {\n        this.addResult('warning', `商品 \"${productData.name}\" 创建失败: ${error.message}`)\n      }\n    },\n    \n    async testProductsAPI() {\n      this.testLoading = true\n      \n      try {\n        const response = await request.get('/good')\n        \n        if (Array.isArray(response) && response.length > 0) {\n          this.products = response\n          this.addResult('success', `成功获取 ${response.length} 个商品`)\n        } else {\n          // 如果没有数据，使用模拟数据\n          this.products = this.productData.map((item, index) => ({\n            id: index + 1,\n            name: item.name,\n            imgs: item.imgs,\n            price: this.calculatePrice(item.discount),\n            description: item.description\n          }))\n          this.addResult('info', '使用模拟商品数据进行展示')\n        }\n        \n      } catch (error) {\n        console.error('获取商品失败:', error)\n        this.addResult('danger', `获取商品失败: ${error.message}`)\n        \n        // 使用模拟数据作为后备\n        this.products = this.productData.map((item, index) => ({\n          id: index + 1,\n          name: item.name,\n          imgs: item.imgs,\n          price: this.calculatePrice(item.discount),\n          description: item.description\n        }))\n      } finally {\n        this.testLoading = false\n      }\n    },\n    \n    calculatePrice(discount) {\n      const basePrice = 200 // 基础价格\n      return (basePrice * discount).toFixed(2)\n    },\n    \n    addResult(type, message) {\n      this.results.push({\n        type,\n        message,\n        time: new Date().toLocaleTimeString()\n      })\n    }\n  }\n}\n</script>\n\n<style scoped>\n.data-initializer {\n  min-height: 100vh;\n  background: #f5f5f5;\n  padding: 20px;\n}\n\n.container {\n  max-width: 1000px;\n  margin: 0 auto;\n  background: white;\n  border-radius: 12px;\n  padding: 30px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n}\n\nh2 {\n  text-align: center;\n  color: #333;\n  margin-bottom: 10px;\n}\n\n.description {\n  text-align: center;\n  color: #666;\n  margin-bottom: 30px;\n}\n\n.actions {\n  display: flex;\n  justify-content: center;\n  gap: 20px;\n  margin-bottom: 30px;\n}\n\n.results {\n  margin-bottom: 30px;\n}\n\n.result-item {\n  margin: 10px 0;\n}\n\n.products-preview h3 {\n  margin-bottom: 20px;\n  color: #333;\n}\n\n.products-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\n  gap: 20px;\n}\n\n.product-card {\n  border: 1px solid #e0e0e0;\n  border-radius: 8px;\n  overflow: hidden;\n  background: white;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  transition: transform 0.2s;\n}\n\n.product-card:hover {\n  transform: translateY(-2px);\n}\n\n.product-image {\n  width: 100%;\n  height: 150px;\n  object-fit: cover;\n}\n\n.product-info {\n  padding: 15px;\n}\n\n.product-info h4 {\n  margin: 0 0 8px;\n  color: #333;\n}\n\n.price {\n  color: #e60000;\n  font-weight: bold;\n  font-size: 16px;\n  margin: 0;\n}\n</style>\n"], "mappings": ";;;AAkDA,OAAOA,OAAM,MAAO,iBAAgB;AAEpC,eAAe;EACbC,IAAI,EAAE,iBAAiB;EAEvBC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,OAAO,EAAE,KAAK;MACdC,WAAW,EAAE,KAAK;MAClBC,OAAO,EAAE,EAAE;MACXC,QAAQ,EAAE,EAAE;MACZC,WAAW,EAAE,CACX;QACEN,IAAI,EAAE,KAAK;QACXO,WAAW,EAAE,oBAAoB;QACjCC,QAAQ,EAAE,IAAI;QACdC,UAAU,EAAE,CAAC;QACbC,IAAI,EAAE,0FAA0F;QAChGC,SAAS,EAAE,IAAI;QACfC,QAAQ,EAAE;MACZ,CAAC,EACD;QACEZ,IAAI,EAAE,KAAK;QACXO,WAAW,EAAE,mBAAmB;QAChCC,QAAQ,EAAE,IAAI;QACdC,UAAU,EAAE,CAAC;QACbC,IAAI,EAAE,wHAAwH;QAC9HC,SAAS,EAAE,IAAI;QACfC,QAAQ,EAAE;MACZ,CAAC,EACD;QACEZ,IAAI,EAAE,QAAQ;QACdO,WAAW,EAAE,oBAAoB;QACjCC,QAAQ,EAAE,IAAI;QACdC,UAAU,EAAE,CAAC;QACbC,IAAI,EAAE,wHAAwH;QAC9HC,SAAS,EAAE,IAAI;QACfC,QAAQ,EAAE;MACZ;IAEJ;EACF,CAAC;EAEDC,OAAO,EAAE;IACP,MAAMC,kBAAkBA,CAAA,EAAG;MACzB,IAAI,CAACZ,OAAM,GAAI,IAAG;MAClB,IAAI,CAACE,OAAM,GAAI,EAAC;MAEhB,IAAI;QACF;QACA,MAAM,IAAI,CAACW,gBAAgB,CAAC;;QAE5B;QACA,KAAK,MAAMT,WAAU,IAAK,IAAI,CAACA,WAAW,EAAE;UAC1C,MAAM,IAAI,CAACU,aAAa,CAACV,WAAW;QACtC;QAEA,IAAI,CAACW,SAAS,CAAC,SAAS,EAAE,cAAc;;QAExC;QACA,MAAM,IAAI,CAACC,eAAe,CAAC;MAE7B,EAAE,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,QAAQ,EAAEA,KAAK;QAC7B,IAAI,CAACF,SAAS,CAAC,QAAQ,EAAE,UAAUE,KAAK,CAACE,OAAO,EAAE;MACpD,UAAU;QACR,IAAI,CAACnB,OAAM,GAAI,KAAI;MACrB;IACF,CAAC;IAED,MAAMa,gBAAgBA,CAAA,EAAG;MACvB,MAAMO,UAAS,GAAI,CACjB;QAAEC,EAAE,EAAE,CAAC;QAAEvB,IAAI,EAAE;MAAK,CAAC,EACrB;QAAEuB,EAAE,EAAE,CAAC;QAAEvB,IAAI,EAAE;MAAK,CAAC,EACrB;QAAEuB,EAAE,EAAE,CAAC;QAAEvB,IAAI,EAAE;MAAK,EACtB;MAEA,KAAK,MAAMwB,QAAO,IAAKF,UAAU,EAAE;QACjC,IAAI;UACF;UACA;UACA,IAAI,CAACL,SAAS,CAAC,MAAM,EAAE,OAAOO,QAAQ,CAACxB,IAAI,QAAQ;QACrD,EAAE,OAAOmB,KAAK,EAAE;UACdC,OAAO,CAACK,GAAG,CAAC,UAAU,EAAED,QAAQ,CAACxB,IAAI;QACvC;MACF;IACF,CAAC;IAED,MAAMgB,aAAaA,CAACV,WAAW,EAAE;MAC/B,IAAI;QACF;QACA;;QAEA;QACA,IAAI,CAACW,SAAS,CAAC,SAAS,EAAE,OAAOX,WAAW,CAACN,IAAI,QAAQ;MAE3D,EAAE,OAAOmB,KAAK,EAAE;QACd,IAAI,CAACF,SAAS,CAAC,SAAS,EAAE,OAAOX,WAAW,CAACN,IAAI,WAAWmB,KAAK,CAACE,OAAO,EAAE;MAC7E;IACF,CAAC;IAED,MAAMH,eAAeA,CAAA,EAAG;MACtB,IAAI,CAACf,WAAU,GAAI,IAAG;MAEtB,IAAI;QACF,MAAMuB,QAAO,GAAI,MAAM3B,OAAO,CAAC4B,GAAG,CAAC,OAAO;QAE1C,IAAIC,KAAK,CAACC,OAAO,CAACH,QAAQ,KAAKA,QAAQ,CAACI,MAAK,GAAI,CAAC,EAAE;UAClD,IAAI,CAACzB,QAAO,GAAIqB,QAAO;UACvB,IAAI,CAACT,SAAS,CAAC,SAAS,EAAE,QAAQS,QAAQ,CAACI,MAAM,MAAM;QACzD,OAAO;UACL;UACA,IAAI,CAACzB,QAAO,GAAI,IAAI,CAACC,WAAW,CAACyB,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,MAAM;YACrDV,EAAE,EAAEU,KAAI,GAAI,CAAC;YACbjC,IAAI,EAAEgC,IAAI,CAAChC,IAAI;YACfU,IAAI,EAAEsB,IAAI,CAACtB,IAAI;YACfwB,KAAK,EAAE,IAAI,CAACC,cAAc,CAACH,IAAI,CAACxB,QAAQ,CAAC;YACzCD,WAAW,EAAEyB,IAAI,CAACzB;UACpB,CAAC,CAAC;UACF,IAAI,CAACU,SAAS,CAAC,MAAM,EAAE,cAAc;QACvC;MAEF,EAAE,OAAOE,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK;QAC9B,IAAI,CAACF,SAAS,CAAC,QAAQ,EAAE,WAAWE,KAAK,CAACE,OAAO,EAAE;;QAEnD;QACA,IAAI,CAAChB,QAAO,GAAI,IAAI,CAACC,WAAW,CAACyB,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,MAAM;UACrDV,EAAE,EAAEU,KAAI,GAAI,CAAC;UACbjC,IAAI,EAAEgC,IAAI,CAAChC,IAAI;UACfU,IAAI,EAAEsB,IAAI,CAACtB,IAAI;UACfwB,KAAK,EAAE,IAAI,CAACC,cAAc,CAACH,IAAI,CAACxB,QAAQ,CAAC;UACzCD,WAAW,EAAEyB,IAAI,CAACzB;QACpB,CAAC,CAAC;MACJ,UAAU;QACR,IAAI,CAACJ,WAAU,GAAI,KAAI;MACzB;IACF,CAAC;IAEDgC,cAAcA,CAAC3B,QAAQ,EAAE;MACvB,MAAM4B,SAAQ,GAAI,GAAE,EAAE;MACtB,OAAO,CAACA,SAAQ,GAAI5B,QAAQ,EAAE6B,OAAO,CAAC,CAAC;IACzC,CAAC;IAEDpB,SAASA,CAACqB,IAAI,EAAEjB,OAAO,EAAE;MACvB,IAAI,CAACjB,OAAO,CAACmC,IAAI,CAAC;QAChBD,IAAI;QACJjB,OAAO;QACPmB,IAAI,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC;MACtC,CAAC;IACH;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}