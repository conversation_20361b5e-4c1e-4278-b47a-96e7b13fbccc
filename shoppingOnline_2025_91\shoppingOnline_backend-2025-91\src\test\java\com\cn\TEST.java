package com.cn;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cn.entity.Carousel;
import com.cn.entity.Category;
import com.cn.entity.User;
import com.cn.service.CarouselService;
import com.cn.service.CategoryService;
import com.cn.service.UserService;
import com.cn.utils.TokenUtils;
import com.cn.utils.Util;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest
public class TEST {

    @Autowired
    private UserService userService;
    @Autowired
    private CategoryService categoryService;
    @Resource
    private CarouselService carouselService;

    /**
     * 用户 测试
     */
    @Test
    public void  userTest(){
        User user=new User();
        user.setPhone("18722821243");
        user.setNickname("潘xxxxxx");
        user.setId(3);
        boolean b = userService.saveOrUpdate(user);
        System.out.println("b:"+b);

    }
    @Test
    public void  userTest2(){
        String nickname="新";
        IPage<User> userPage = new Page<>(1, 10);
        QueryWrapper<User> userQueryWrapper = new QueryWrapper<>();
       /* if (!Util.isEmptyString(id)) {
            userQueryWrapper.like("id", id);
        }
        if (!Util.isEmptyString(username)) {
            userQueryWrapper.like("username", username);
        }*/
        if (!Util.isEmptyString(nickname)) {
            userQueryWrapper.like("nickname", nickname);
        }
        userQueryWrapper.orderByDesc("id");
        System.out.println("============" + TokenUtils.getCurrentUser());
        IPage<User> page = userService.page(userPage, userQueryWrapper);
        System.out.println("======b:"+page.getRecords());
    }

    /**
     * cateService
     */

    @Test
    public void  userTest3(){
        Category category=new Category();
        category.setName("测试类");
       // categoryService.add(category);
        Long id= 24L;
        categoryService.delete(id);
    }

    /**
     * CarouselController
     */
    @Test
    public void  userTest4(){
        List<Carousel> allCarousel = carouselService.getAllCarousel();
        for (Carousel carousel : allCarousel) {
             System.out.println("XXX:"+carousel.getGoodName());
        }
    }
}
