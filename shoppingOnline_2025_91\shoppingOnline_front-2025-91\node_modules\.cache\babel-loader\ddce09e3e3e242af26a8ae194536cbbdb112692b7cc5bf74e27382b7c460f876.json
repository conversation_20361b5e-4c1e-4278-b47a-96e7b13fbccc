{"ast": null, "code": "/*\r\n * @Description: \r\n * @Author: JACK\r\n * @Date: 2025年8月29日11:12:13\r\n */\nimport { defineStore } from 'pinia';\n\n// 定义主要的 store\nexport const useMainStore = defineStore('main', {\n  state: () => ({\n    baseApi: 'http://localhost:9197'\n  }),\n  getters: {\n    // 同步计算属性\n  },\n  actions: {\n    // 可以包含异步逻辑\n  }\n});\n\n// 定义用户状态 store\nexport const useUserStore = defineStore('user', {\n  state: () => ({\n    isLoggedIn: false,\n    userInfo: null,\n    token: null\n  }),\n  getters: {\n    // 获取用户名\n    username: state => state.userInfo?.username || '',\n    // 获取用户角色\n    userRole: state => state.userInfo?.role || '',\n    // 检查是否已登录\n    isAuthenticated: state => state.isLoggedIn && !!state.token\n  },\n  actions: {\n    // 登录\n    login(userInfo, token) {\n      this.isLoggedIn = true;\n      this.userInfo = userInfo;\n      this.token = token;\n\n      // 保存到localStorage\n      localStorage.setItem('isLoggedIn', 'true');\n      localStorage.setItem('userInfo', JSON.stringify(userInfo));\n      localStorage.setItem('token', token);\n    },\n    // 登出\n    logout() {\n      this.isLoggedIn = false;\n      this.userInfo = null;\n      this.token = null;\n\n      // 清除localStorage\n      localStorage.removeItem('isLoggedIn');\n      localStorage.removeItem('userInfo');\n      localStorage.removeItem('token');\n      localStorage.removeItem('username'); // 兼容旧版本\n    },\n    // 从localStorage恢复登录状态\n    restoreLoginState() {\n      const isLoggedIn = localStorage.getItem('isLoggedIn') === 'true';\n      const userInfo = localStorage.getItem('userInfo');\n      const token = localStorage.getItem('token');\n      if (isLoggedIn && userInfo && token) {\n        this.isLoggedIn = true;\n        this.userInfo = JSON.parse(userInfo);\n        this.token = token;\n      }\n    }\n  }\n});", "map": {"version": 3, "names": ["defineStore", "useMainStore", "state", "baseApi", "getters", "actions", "useUserStore", "isLoggedIn", "userInfo", "token", "username", "userRole", "role", "isAuthenticated", "login", "localStorage", "setItem", "JSON", "stringify", "logout", "removeItem", "restoreLoginState", "getItem", "parse"], "sources": ["D:/2025_down/project/shoppingOnline_2025_91/shoppingOnline_front-2025-91/src/store/index.js"], "sourcesContent": ["/*\r\n * @Description: \r\n * @Author: JACK\r\n * @Date: 2025年8月29日11:12:13\r\n */\r\nimport { defineStore } from 'pinia'\r\n\r\n// 定义主要的 store\r\nexport const useMainStore = defineStore('main', {\r\n  state: () => ({\r\n    baseApi: 'http://localhost:9197'\r\n  }),\r\n  getters: {\r\n    // 同步计算属性\r\n  },\r\n  actions: {\r\n    // 可以包含异步逻辑\r\n  }\r\n})\r\n\r\n// 定义用户状态 store\r\nexport const useUserStore = defineStore('user', {\r\n  state: () => ({\r\n    isLoggedIn: false,\r\n    userInfo: null,\r\n    token: null\r\n  }),\r\n  getters: {\r\n    // 获取用户名\r\n    username: (state) => state.userInfo?.username || '',\r\n    // 获取用户角色\r\n    userRole: (state) => state.userInfo?.role || '',\r\n    // 检查是否已登录\r\n    isAuthenticated: (state) => state.isLoggedIn && !!state.token\r\n  },\r\n  actions: {\r\n    // 登录\r\n    login(userInfo, token) {\r\n      this.isLoggedIn = true\r\n      this.userInfo = userInfo\r\n      this.token = token\r\n\r\n      // 保存到localStorage\r\n      localStorage.setItem('isLoggedIn', 'true')\r\n      localStorage.setItem('userInfo', JSON.stringify(userInfo))\r\n      localStorage.setItem('token', token)\r\n    },\r\n\r\n    // 登出\r\n    logout() {\r\n      this.isLoggedIn = false\r\n      this.userInfo = null\r\n      this.token = null\r\n\r\n      // 清除localStorage\r\n      localStorage.removeItem('isLoggedIn')\r\n      localStorage.removeItem('userInfo')\r\n      localStorage.removeItem('token')\r\n      localStorage.removeItem('username') // 兼容旧版本\r\n    },\r\n\r\n    // 从localStorage恢复登录状态\r\n    restoreLoginState() {\r\n      const isLoggedIn = localStorage.getItem('isLoggedIn') === 'true'\r\n      const userInfo = localStorage.getItem('userInfo')\r\n      const token = localStorage.getItem('token')\r\n\r\n      if (isLoggedIn && userInfo && token) {\r\n        this.isLoggedIn = true\r\n        this.userInfo = JSON.parse(userInfo)\r\n        this.token = token\r\n      }\r\n    }\r\n  }\r\n})"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,SAASA,WAAW,QAAQ,OAAO;;AAEnC;AACA,OAAO,MAAMC,YAAY,GAAGD,WAAW,CAAC,MAAM,EAAE;EAC9CE,KAAK,EAAEA,CAAA,MAAO;IACZC,OAAO,EAAE;EACX,CAAC,CAAC;EACFC,OAAO,EAAE;IACP;EAAA,CACD;EACDC,OAAO,EAAE;IACP;EAAA;AAEJ,CAAC,CAAC;;AAEF;AACA,OAAO,MAAMC,YAAY,GAAGN,WAAW,CAAC,MAAM,EAAE;EAC9CE,KAAK,EAAEA,CAAA,MAAO;IACZK,UAAU,EAAE,KAAK;IACjBC,QAAQ,EAAE,IAAI;IACdC,KAAK,EAAE;EACT,CAAC,CAAC;EACFL,OAAO,EAAE;IACP;IACAM,QAAQ,EAAGR,KAAK,IAAKA,KAAK,CAACM,QAAQ,EAAEE,QAAQ,IAAI,EAAE;IACnD;IACAC,QAAQ,EAAGT,KAAK,IAAKA,KAAK,CAACM,QAAQ,EAAEI,IAAI,IAAI,EAAE;IAC/C;IACAC,eAAe,EAAGX,KAAK,IAAKA,KAAK,CAACK,UAAU,IAAI,CAAC,CAACL,KAAK,CAACO;EAC1D,CAAC;EACDJ,OAAO,EAAE;IACP;IACAS,KAAKA,CAACN,QAAQ,EAAEC,KAAK,EAAE;MACrB,IAAI,CAACF,UAAU,GAAG,IAAI;MACtB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;MACxB,IAAI,CAACC,KAAK,GAAGA,KAAK;;MAElB;MACAM,YAAY,CAACC,OAAO,CAAC,YAAY,EAAE,MAAM,CAAC;MAC1CD,YAAY,CAACC,OAAO,CAAC,UAAU,EAAEC,IAAI,CAACC,SAAS,CAACV,QAAQ,CAAC,CAAC;MAC1DO,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEP,KAAK,CAAC;IACtC,CAAC;IAED;IACAU,MAAMA,CAAA,EAAG;MACP,IAAI,CAACZ,UAAU,GAAG,KAAK;MACvB,IAAI,CAACC,QAAQ,GAAG,IAAI;MACpB,IAAI,CAACC,KAAK,GAAG,IAAI;;MAEjB;MACAM,YAAY,CAACK,UAAU,CAAC,YAAY,CAAC;MACrCL,YAAY,CAACK,UAAU,CAAC,UAAU,CAAC;MACnCL,YAAY,CAACK,UAAU,CAAC,OAAO,CAAC;MAChCL,YAAY,CAACK,UAAU,CAAC,UAAU,CAAC,EAAC;IACtC,CAAC;IAED;IACAC,iBAAiBA,CAAA,EAAG;MAClB,MAAMd,UAAU,GAAGQ,YAAY,CAACO,OAAO,CAAC,YAAY,CAAC,KAAK,MAAM;MAChE,MAAMd,QAAQ,GAAGO,YAAY,CAACO,OAAO,CAAC,UAAU,CAAC;MACjD,MAAMb,KAAK,GAAGM,YAAY,CAACO,OAAO,CAAC,OAAO,CAAC;MAE3C,IAAIf,UAAU,IAAIC,QAAQ,IAAIC,KAAK,EAAE;QACnC,IAAI,CAACF,UAAU,GAAG,IAAI;QACtB,IAAI,CAACC,QAAQ,GAAGS,IAAI,CAACM,KAAK,CAACf,QAAQ,CAAC;QACpC,IAAI,CAACC,KAAK,GAAGA,KAAK;MACpB;IACF;EACF;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}