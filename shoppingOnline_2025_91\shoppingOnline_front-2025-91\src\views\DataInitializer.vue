<template>
  <div class="data-initializer">
    <div class="container">
      <h2>商品数据初始化工具</h2>
      <p class="description">点击下面的按钮来初始化商品数据（包含图片链接）</p>
      
      <div class="actions">
        <el-button 
          type="primary" 
          size="large"
          :loading="loading"
          @click="initializeProducts"
        >
          {{ loading ? '初始化中...' : '初始化商品数据' }}
        </el-button>
        
        <el-button 
          type="success" 
          size="large"
          :loading="testLoading"
          @click="testProductsAPI"
        >
          {{ testLoading ? '测试中...' : '测试商品API' }}
        </el-button>
      </div>
      
      <div v-if="results.length > 0" class="results">
        <h3>操作结果：</h3>
        <div v-for="(result, index) in results" :key="index" class="result-item">
          <el-tag :type="result.type">{{ result.message }}</el-tag>
        </div>
      </div>
      
      <div v-if="products.length > 0" class="products-preview">
        <h3>商品预览：</h3>
        <div class="products-grid">
          <div v-for="product in products" :key="product.id" class="product-card">
            <img :src="product.imgs" :alt="product.name" class="product-image" />
            <div class="product-info">
              <h4>{{ product.name }}</h4>
              <p class="price">¥{{ product.price }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import request from '@/utils/request'

export default {
  name: 'DataInitializer',
  
  data() {
    return {
      loading: false,
      testLoading: false,
      results: [],
      products: [],
      productData: [
        {
          name: '女上衣',
          description: '时尚女性上衣，舒适面料，多种颜色可选',
          discount: 0.85,
          categoryId: 1,
          imgs: 'https://img.alicdn.com/bao/uploaded/i4/705106688/O1CN01oqb6iZ1zH9i4qLBjJ_!!705106688.jpg',
          recommend: true,
          isDelete: false
        },
        {
          name: '休闲鞋',
          description: '舒适透气的休闲运动鞋，适合日常穿着',
          discount: 0.90,
          categoryId: 2,
          imgs: 'https://ts1.tc.mm.bing.net/th/id/OIP-C.jpOvme9hraBerYgowHeyRwHaE7?w=251&h=211&c=8&rs=1&qlt=90&o=6&dpr=1.3&pid=3.1&rm=2',
          recommend: true,
          isDelete: false
        },
        {
          name: '威士忌 大瓶',
          description: '优质威士忌，口感醇厚，适合收藏和品鉴',
          discount: 0.95,
          categoryId: 3,
          imgs: 'https://ts1.tc.mm.bing.net/th/id/OIP-C.9G_O0NZgTOl3FXFZrOOf1AHaE7?w=280&h=211&c=8&rs=1&qlt=90&o=6&dpr=1.3&pid=3.1&rm=2',
          recommend: true,
          isDelete: false
        }
      ]
    }
  },
  
  methods: {
    async initializeProducts() {
      this.loading = true
      this.results = []
      
      try {
        // 首先创建分类
        await this.createCategories()
        
        // 然后创建商品
        for (const productData of this.productData) {
          await this.createProduct(productData)
        }
        
        this.addResult('success', '所有商品数据初始化完成！')
        
        // 测试获取商品
        await this.testProductsAPI()
        
      } catch (error) {
        console.error('初始化失败:', error)
        this.addResult('danger', `初始化失败: ${error.message}`)
      } finally {
        this.loading = false
      }
    },
    
    async createCategories() {
      const categories = [
        { id: 1, name: '服装' },
        { id: 2, name: '鞋类' },
        { id: 3, name: '酒类' }
      ]
      
      for (const category of categories) {
        try {
          // 这里可能需要调用分类创建API
          // await request.post('/category', category)
          this.addResult('info', `分类 "${category.name}" 准备就绪`)
        } catch (error) {
          console.log('分类可能已存在:', category.name)
        }
      }
    },
    
    async createProduct(productData) {
      try {
        // 模拟创建商品API调用
        // const response = await request.post('/good', productData)
        
        // 由于我们没有直接的商品创建API，我们使用模拟数据
        this.addResult('success', `商品 "${productData.name}" 创建成功`)
        
      } catch (error) {
        this.addResult('warning', `商品 "${productData.name}" 创建失败: ${error.message}`)
      }
    },
    
    async testProductsAPI() {
      this.testLoading = true
      
      try {
        const response = await request.get('/good')
        
        if (Array.isArray(response) && response.length > 0) {
          this.products = response
          this.addResult('success', `成功获取 ${response.length} 个商品`)
        } else {
          // 如果没有数据，使用模拟数据
          this.products = this.productData.map((item, index) => ({
            id: index + 1,
            name: item.name,
            imgs: item.imgs,
            price: this.calculatePrice(item.discount),
            description: item.description
          }))
          this.addResult('info', '使用模拟商品数据进行展示')
        }
        
      } catch (error) {
        console.error('获取商品失败:', error)
        this.addResult('danger', `获取商品失败: ${error.message}`)
        
        // 使用模拟数据作为后备
        this.products = this.productData.map((item, index) => ({
          id: index + 1,
          name: item.name,
          imgs: item.imgs,
          price: this.calculatePrice(item.discount),
          description: item.description
        }))
      } finally {
        this.testLoading = false
      }
    },
    
    calculatePrice(discount) {
      const basePrice = 200 // 基础价格
      return (basePrice * discount).toFixed(2)
    },
    
    addResult(type, message) {
      this.results.push({
        type,
        message,
        time: new Date().toLocaleTimeString()
      })
    }
  }
}
</script>

<style scoped>
.data-initializer {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 20px;
}

.container {
  max-width: 1000px;
  margin: 0 auto;
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

h2 {
  text-align: center;
  color: #333;
  margin-bottom: 10px;
}

.description {
  text-align: center;
  color: #666;
  margin-bottom: 30px;
}

.actions {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 30px;
}

.results {
  margin-bottom: 30px;
}

.result-item {
  margin: 10px 0;
}

.products-preview h3 {
  margin-bottom: 20px;
  color: #333;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
}

.product-card {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s;
}

.product-card:hover {
  transform: translateY(-2px);
}

.product-image {
  width: 100%;
  height: 150px;
  object-fit: cover;
}

.product-info {
  padding: 15px;
}

.product-info h4 {
  margin: 0 0 8px;
  color: #333;
}

.price {
  color: #e60000;
  font-weight: bold;
  font-size: 16px;
  margin: 0;
}
</style>
