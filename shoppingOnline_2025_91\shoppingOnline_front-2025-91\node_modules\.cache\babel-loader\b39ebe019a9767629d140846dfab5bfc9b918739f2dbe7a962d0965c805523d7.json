{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, withCtx as _withCtx, openBlock as _openBlock, createBlock as _createBlock, createElementBlock as _createElementBlock, renderList as _renderList, Fragment as _Fragment, withModifiers as _withModifiers } from \"vue\";\nconst _hoisted_1 = {\n  class: \"goods-container\"\n};\nconst _hoisted_2 = {\n  key: 0,\n  class: \"search-info\"\n};\nconst _hoisted_3 = {\n  class: \"search-count\"\n};\nconst _hoisted_4 = {\n  key: 1,\n  class: \"loading\"\n};\nconst _hoisted_5 = {\n  class: \"goods-list\"\n};\nconst _hoisted_6 = [\"onClick\"];\nconst _hoisted_7 = [\"src\", \"alt\"];\nconst _hoisted_8 = {\n  class: \"goods-info\"\n};\nconst _hoisted_9 = {\n  class: \"goods-name\"\n};\nconst _hoisted_10 = {\n  class: \"goods-desc\"\n};\nconst _hoisted_11 = {\n  class: \"goods-price\"\n};\nconst _hoisted_12 = [\"onClick\"];\nconst _hoisted_13 = {\n  key: 3,\n  class: \"no-data\"\n};\nconst _hoisted_14 = {\n  key: 4,\n  class: \"pagination-wrapper\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_CarouselComponent = _resolveComponent(\"CarouselComponent\");\n  const _component_SearchBox = _resolveComponent(\"SearchBox\");\n  const _component_ProductCategory = _resolveComponent(\"ProductCategory\");\n  const _component_el_tag = _resolveComponent(\"el-tag\");\n  const _component_el_skeleton = _resolveComponent(\"el-skeleton\");\n  const _component_ProductDetail = _resolveComponent(\"ProductDetail\");\n  const _component_el_empty = _resolveComponent(\"el-empty\");\n  const _component_el_pagination = _resolveComponent(\"el-pagination\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" 页面标题 \"), _cache[6] || (_cache[6] = _createElementVNode(\"h1\", {\n    class: \"page-title\"\n  }, \"在线购物商城\", -1 /* CACHED */)), _createCommentVNode(\" 轮播图组件 \"), _createVNode(_component_CarouselComponent, {\n    onCarouselClick: $options.handleCarouselClick\n  }, null, 8 /* PROPS */, [\"onCarouselClick\"]), _createCommentVNode(\" 搜索框组件 \"), _createVNode(_component_SearchBox, {\n    onSearch: $options.handleSearch,\n    onSearchResults: $options.handleSearchResults,\n    onClearSearch: $options.handleClearSearch\n  }, null, 8 /* PROPS */, [\"onSearch\", \"onSearchResults\", \"onClearSearch\"]), _createCommentVNode(\" 商品分类 \"), _createVNode(_component_ProductCategory, {\n    ref: \"productCategory\",\n    onCategoryChange: $options.handleCategoryChange\n  }, null, 8 /* PROPS */, [\"onCategoryChange\"]), _createCommentVNode(\" 搜索结果和分类筛选提示 \"), $data.searchKeyword || $data.hasCategoryFilter ? (_openBlock(), _createElementBlock(\"div\", _hoisted_2, [_createCommentVNode(\" 搜索结果标签 \"), $data.searchKeyword ? (_openBlock(), _createBlock(_component_el_tag, {\n    key: 0,\n    type: \"info\",\n    closable: \"\",\n    onClose: $options.handleClearSearch\n  }, {\n    default: _withCtx(() => [_createTextVNode(\" 搜索：\" + _toDisplayString($data.searchKeyword), 1 /* TEXT */)]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClose\"])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 分类筛选标签 \"), $data.selectedMainCategory ? (_openBlock(), _createBlock(_component_el_tag, {\n    key: 1,\n    type: \"success\",\n    closable: \"\",\n    onClose: $options.clearCategoryFilter\n  }, {\n    default: _withCtx(() => [...(_cache[5] || (_cache[5] = [_createTextVNode(\" 分类筛选 \", -1 /* CACHED */)]))]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClose\"])) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"span\", _hoisted_3, \"共找到 \" + _toDisplayString($data.totalCount) + \" 件商品\", 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 加载状态 \"), $data.loading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_4, [_createVNode(_component_el_skeleton, {\n    rows: 3,\n    animated: \"\"\n  })])) : (_openBlock(), _createElementBlock(_Fragment, {\n    key: 2\n  }, [_createCommentVNode(\" 商品列表 \"), _createElementVNode(\"div\", _hoisted_5, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.goodsList, good => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      class: \"goods-card\",\n      key: good.id,\n      onClick: $event => $options.showProductDetail(good)\n    }, [_createElementVNode(\"img\", {\n      src: good.imgs,\n      alt: good.name,\n      class: \"goods-image\",\n      onError: _cache[0] || (_cache[0] = (...args) => $options.handleImageError && $options.handleImageError(...args)),\n      onLoad: _cache[1] || (_cache[1] = (...args) => $options.handleImageLoad && $options.handleImageLoad(...args))\n    }, null, 40 /* PROPS, NEED_HYDRATION */, _hoisted_7), _createElementVNode(\"div\", _hoisted_8, [_createElementVNode(\"h3\", _hoisted_9, _toDisplayString(good.name), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_10, _toDisplayString(good.description || '暂无商品描述'), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_11, [_createElementVNode(\"strong\", null, \"¥\" + _toDisplayString(good.price), 1 /* TEXT */)]), _createElementVNode(\"button\", {\n      class: \"btn-add-cart\",\n      onClick: _withModifiers($event => $options.addToCart(good), [\"stop\"])\n    }, \" 加入购物车 \", 8 /* PROPS */, _hoisted_12)])], 8 /* PROPS */, _hoisted_6);\n  }), 128 /* KEYED_FRAGMENT */))])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)), _createCommentVNode(\" 商品详情弹窗 \"), _createVNode(_component_ProductDetail, {\n    modelValue: $data.showDetailDialog,\n    \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $data.showDetailDialog = $event),\n    product: $data.selectedProduct,\n    onAddToCart: $options.addToCart,\n    onBuyNow: $options.buyNow\n  }, null, 8 /* PROPS */, [\"modelValue\", \"product\", \"onAddToCart\", \"onBuyNow\"]), _createCommentVNode(\" 无数据\"), !$data.loading && $data.goodsList.length === 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_13, [_createVNode(_component_el_empty, {\n    description: \"暂无商品\"\n  })])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 分页组件 \"), $data.totalCount > $data.pageSize ? (_openBlock(), _createElementBlock(\"div\", _hoisted_14, [_createVNode(_component_el_pagination, {\n    \"current-page\": $data.currentPage,\n    \"onUpdate:currentPage\": _cache[3] || (_cache[3] = $event => $data.currentPage = $event),\n    \"page-size\": $data.pageSize,\n    \"onUpdate:pageSize\": _cache[4] || (_cache[4] = $event => $data.pageSize = $event),\n    \"page-sizes\": [10, 20, 50, 100],\n    total: $data.totalCount,\n    layout: \"total, sizes, prev, pager, next, jumper\",\n    onSizeChange: $options.handleSizeChange,\n    onCurrentChange: $options.handleCurrentChange\n  }, null, 8 /* PROPS */, [\"current-page\", \"page-size\", \"total\", \"onSizeChange\", \"onCurrentChange\"])])) : _createCommentVNode(\"v-if\", true)]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "_createVNode", "_component_CarouselComponent", "onCarouselClick", "$options", "handleCarouselClick", "_component_SearchBox", "onSearch", "handleSearch", "onSearchResults", "handleSearchResults", "onClearSearch", "handleClearSearch", "_component_ProductCategory", "ref", "onCategoryChange", "handleCategoryChange", "$data", "searchKeyword", "hasCate<PERSON><PERSON><PERSON><PERSON><PERSON>", "_hoisted_2", "_createBlock", "_component_el_tag", "type", "closable", "onClose", "_toDisplayString", "selected<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clearCategory<PERSON><PERSON>er", "_cache", "_hoisted_3", "totalCount", "loading", "_hoisted_4", "_component_el_skeleton", "rows", "animated", "_Fragment", "key", "_hoisted_5", "_renderList", "goodsList", "good", "id", "onClick", "$event", "showProductDetail", "src", "imgs", "alt", "name", "onError", "args", "handleImageError", "onLoad", "handleImageLoad", "_hoisted_8", "_hoisted_9", "_hoisted_10", "description", "_hoisted_11", "price", "_withModifiers", "addToCart", "_hoisted_12", "_component_ProductDetail", "showDetailDialog", "product", "selectedProduct", "onAddToCart", "onBuyNow", "buyNow", "length", "_hoisted_13", "_component_el_empty", "pageSize", "_hoisted_14", "_component_el_pagination", "currentPage", "total", "layout", "onSizeChange", "handleSizeChange", "onCurrentChange", "handleCurrentChange"], "sources": ["D:\\2025_down\\project\\shoppingOnline_2025_91\\shoppingOnline_front-2025-91\\src\\views\\goods.vue"], "sourcesContent": ["<template>\r\n  <div class=\"goods-container\">\r\n    <!-- 页面标题 -->\r\n    <h1 class=\"page-title\">在线购物商城</h1>\r\n\r\n    <!-- 轮播图组件 -->\r\n    <CarouselComponent @carousel-click=\"handleCarouselClick\" />\r\n\r\n    <!-- 搜索框组件 -->\r\n    <SearchBox\r\n      @search=\"handleSearch\"\r\n      @search-results=\"handleSearchResults\"\r\n      @clear-search=\"handleClearSearch\"\r\n    />\r\n\r\n    <!-- 商品分类 -->\r\n    <ProductCategory ref=\"productCategory\" @category-change=\"handleCategoryChange\" />\r\n\r\n    <!-- 搜索结果和分类筛选提示 -->\r\n    <div v-if=\"searchKeyword || hasCategoryFilter\" class=\"search-info\">\r\n      <!-- 搜索结果标签 -->\r\n      <el-tag v-if=\"searchKeyword\" type=\"info\" closable @close=\"handleClearSearch\">\r\n        搜索：{{ searchKeyword }}\r\n      </el-tag>\r\n      \r\n      <!-- 分类筛选标签 -->\r\n      <el-tag v-if=\"selectedMainCategory\" type=\"success\" closable @close=\"clearCategoryFilter\">\r\n        分类筛选\r\n      </el-tag>\r\n      \r\n      <span class=\"search-count\">共找到 {{ totalCount }} 件商品</span>\r\n    </div>\r\n\r\n    <!-- 加载状态 -->\r\n    <div v-if=\"loading\" class=\"loading\">\r\n      <el-skeleton :rows=\"3\" animated />\r\n    </div>\r\n\r\n    <!-- 商品列表 -->\r\n    <div v-else class=\"goods-list\">\r\n      <div\r\n        class=\"goods-card\"\r\n        v-for=\"good in goodsList\"\r\n        :key=\"good.id\"\r\n        @click=\"showProductDetail(good)\"\r\n      >\r\n        <img\r\n          :src=\"good.imgs\"\r\n          :alt=\"good.name\"\r\n          class=\"goods-image\"\r\n          @error=\"handleImageError\"\r\n          @load=\"handleImageLoad\"\r\n        />\r\n        <div class=\"goods-info\">\r\n          <h3 class=\"goods-name\">{{ good.name }}</h3>\r\n          <p class=\"goods-desc\">{{ good.description || '暂无商品描述' }}</p>\r\n          <p class=\"goods-price\"><strong>¥{{ good.price }}</strong></p>\r\n          <button class=\"btn-add-cart\" @click.stop=\"addToCart(good)\">\r\n            加入购物车\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 商品详情弹窗 -->\r\n    <ProductDetail\r\n      v-model=\"showDetailDialog\"\r\n      :product=\"selectedProduct\"\r\n      @add-to-cart=\"addToCart\"\r\n      @buy-now=\"buyNow\"\r\n    />\r\n\r\n    <!-- 无数据-->\r\n    <div v-if=\"!loading && goodsList.length === 0\" class=\"no-data\">\r\n      <el-empty description=\"暂无商品\" />\r\n    </div>\r\n\r\n    <!-- 分页组件 -->\r\n    <div v-if=\"totalCount > pageSize\" class=\"pagination-wrapper\">\r\n      <el-pagination\r\n        v-model:current-page=\"currentPage\"\r\n        v-model:page-size=\"pageSize\"\r\n        :page-sizes=\"[10, 20, 50, 100]\"\r\n        :total=\"totalCount\"\r\n        layout=\"total, sizes, prev, pager, next, jumper\"\r\n        @size-change=\"handleSizeChange\"\r\n        @current-change=\"handleCurrentChange\"\r\n      />\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport request from '@/utils/request.js'\r\nimport CarouselComponent from '@/components/Carousel.vue'\r\nimport SearchBox from '@/components/SearchBox.vue'\r\nimport ProductCategory from '@/components/ProductCategory.vue'\r\nimport ProductDetail from '@/components/ProductDetail.vue'\r\n\r\n\r\nexport default {\r\n  name: 'GoodsView',\r\n\r\n  components: {\r\n      CarouselComponent,\r\n      SearchBox,\r\n      ProductCategory,\r\n      ProductDetail\r\n    },\r\n\r\n  data() {\r\n    return {\r\n      name: '',\r\n      awesome: false,\r\n      loading: true,\r\n      goodsList: [],\r\n      searchKeyword: '', // 当前搜索关键词\r\n      currentPage: 1,\r\n      pageSize: 20,\r\n      totalCount: 0,\r\n      isSearchMode: false, // 是否处于搜索模式\r\n      selectedMainCategory: null, // 选中的主分类\r\n      selectedSubCategory: null, // 选中的子分类\r\n      hasCategoryFilter: false, // 是否有分类筛选\r\n      showDetailDialog: false, // 是否显示商品详情弹窗\r\n      selectedProduct: {} // 选中的商品\r\n    };\r\n  },\r\n\r\n  computed: {\r\n    // 计算商品总数\r\n    totalGoods() {\r\n      return this.goodsList.length;\r\n    }\r\n  },\r\n\r\n  watch: {\r\n    // 监听路由参数变化\r\n    '$route.query.search': {\r\n      handler(newSearch) {\r\n        if (newSearch) {\r\n          this.handleSearch(newSearch);\r\n        } else {\r\n          this.handleClearSearch();\r\n        }\r\n      },\r\n      immediate: false\r\n    }\r\n  },\r\n\r\n  created() {\r\n    // 检查是否有搜索参数\r\n    const searchQuery = this.$route.query.search;\r\n    const categoryId = this.$route.query.categoryId;\r\n\r\n    if (searchQuery) {\r\n      this.handleSearch(searchQuery);\r\n    } else if (categoryId) {\r\n      // 如果有分类参数，设置分类筛选\r\n      this.selectedMainCategory = parseInt(categoryId);\r\n      this.hasCategoryFilter = true;\r\n      this.findFrontGoods();\r\n    } else {\r\n      this.findFrontGoods();\r\n    }\r\n  },\r\n\r\n  mounted() {\r\n    // 检查路由参数中是否有分类信息\r\n    this.checkRouteParams();\r\n    // 调试：输出图片路径信息\r\n    console.log('当前页面URL:', window.location.href);\r\n    console.log('图片路径测试:', './images/women-top.jpg');\r\n    console.log('绝对路径测试:', '/images/women-top.jpg');\r\n\r\n    // 查询推荐商品\r\n    this.findFrontGoods();\r\n  },\r\n\r\n  methods: {\r\n    // 处理分类变化\r\n    handleCategoryChange(category) {\r\n      console.log('分类变化:', category);\r\n      this.selectedMainCategory = category.categoryId;\r\n      this.hasCategoryFilter = category.categoryId !== null;\r\n      this.currentPage = 1;\r\n\r\n      if (this.isSearchMode) {\r\n        this.searchGoods();\r\n      } else {\r\n        this.findFrontGoods();\r\n      }\r\n    },\r\n\r\n    // 图片加载错误处理\r\n    handleImageError(event) {\r\n      console.error('图片加载失败:', event.target.src);\r\n      console.error('图片元素:', event.target);\r\n      // 设置默认图片或隐藏图片\r\n      event.target.style.display = 'none';\r\n      event.target.style.backgroundColor = '#f0f0f0';\r\n      event.target.style.border = '1px solid #ccc';\r\n    },\r\n\r\n    // 图片加载成功处理\r\n    handleImageLoad(event) {\r\n      console.log('图片加载成功:', event.target.src);\r\n      console.log('图片尺寸:', event.target.naturalWidth, 'x', event.target.naturalHeight);\r\n    },\r\n\r\n    // 显示商品详情\r\n    showProductDetail(good) {\r\n      this.selectedProduct = good;\r\n      this.showDetailDialog = true;\r\n      console.log('查看商品详情:', good);\r\n    },\r\n\r\n    // 添加到购物车\r\n    addToCart(good) {\r\n      console.log('加入购物车:', good.name);\r\n      this.$emit('add-to-cart', good);\r\n      this.$message.success(`已加入购物车：${good.name}`);\r\n    },\r\n\r\n    // 立即购买\r\n    buyNow(good) {\r\n      console.log('立即购买:', good.name);\r\n      this.$message.info(`立即购买：${good.name}`);\r\n      // 这里可以跳转到订单页面\r\n    },\r\n\r\n    // 查询推荐商品\r\n    async findFrontGoods() {\r\n      try {\r\n        this.loading = true;\r\n        let params = {};\r\n        \r\n        // 如果有分类筛选，添加分类参数\r\n        if (this.hasCategoryFilter && this.selectedMainCategory) {\r\n          params.categoryId = this.selectedMainCategory;\r\n        }\r\n        \r\n        const response = await request.get('/good', params);\r\n\r\n        if (Array.isArray(response) && response.length > 0) {\r\n          // 使用后端数据，并处理图片URL和确保描述存在\r\n          this.goodsList = response.map((item, index) => {\r\n            // 根据商品名称匹配对应的图片\r\n            let imgUrl;\r\n            switch(item.name) {\r\n              case '女上衣':\r\n                imgUrl = require('@/assets/女上衣.png');\r\n                break;\r\n              case '休闲鞋':\r\n                imgUrl = require('@/assets/休闲鞋.png');\r\n                break;\r\n              case '威士忌 大瓶':\r\n                imgUrl = require('@/assets/威士忌 大瓶.png');\r\n                break;\r\n              case '墨镜':\r\n                imgUrl = require('@/assets/墨镜.png');\r\n                break;\r\n              case '桌椅套装':\r\n                imgUrl = require('@/assets/桌椅套装.png');\r\n                break;\r\n              case '儿童简笔画册':\r\n                imgUrl = require('@/assets/儿童简笔画册.png');\r\n                break;\r\n              case '英文版图书':\r\n                imgUrl = require('@/assets/英文版图书.png');\r\n                break;\r\n              case '衬衫':\r\n                imgUrl = require('@/assets/衬衫.png');\r\n                break;\r\n              default: {\r\n                // 默认图片，根据索引循环使用\r\n                const defaultImages = [\r\n                  require('@/assets/女上衣.png'),\r\n                  require('@/assets/休闲鞋.png'),\r\n                  require('@/assets/威士忌 大瓶.png'),\r\n                  require('@/assets/墨镜.png'),\r\n                  require('@/assets/桌椅套装.png')\r\n                ];\r\n                imgUrl = defaultImages[index % defaultImages.length];\r\n                break;\r\n              }\r\n            }\r\n            return {\r\n              ...item,\r\n              imgs: imgUrl,\r\n              description: item.description || this.getDefaultDescription(item.name), // 使用默认描述\r\n              price: item.price || (Math.random() * 200 + 50).toFixed(2) // 确保有价格\r\n            };\r\n          });\r\n          console.log('使用后端商品数据，添加图片:', this.goodsList);\r\n        } else {\r\n          // 如果后端没有数据，使用本地图片的模拟数据\r\n          this.goodsList = this.getMockProductsWithLocalImages();\r\n          console.log('使用本地图片的模拟商品数据:', this.goodsList);\r\n        }\r\n\r\n        this.totalCount = this.goodsList.length;\r\n        this.isSearchMode = false;\r\n      } catch (error) {\r\n        console.error('请求失败:', error.message || error);\r\n        // 使用本地图片的模拟数据作为后备\r\n        this.goodsList = this.getMockProductsWithLocalImages();\r\n        this.totalCount = this.goodsList.length;\r\n        console.log('API请求失败，使用本地图片的模拟数据:', this.goodsList);\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n\r\n    // 获取默认商品描述\r\n    getDefaultDescription(productName) {\r\n      const descriptions = {\r\n        '女上衣': '时尚女性上衣，舒适面料，多种颜色可选，适合日常穿着和商务场合，版型修身显瘦',\r\n        '休闲鞋': '舒适透气的休闲运动鞋，适合日常穿着，防滑耐磨，多种尺码可选，时尚百搭',\r\n        '威士忌 大瓶': '优质威士忌，口感醇厚，适合收藏和品鉴，酒精度40%，容量750ml，包装精美',\r\n        '墨镜': '时尚太阳镜，UV400防护，轻量化设计，多种款式可选',\r\n        '桌椅套装': '实木桌椅套装，环保材质，结实耐用，适合家庭使用',\r\n        '儿童简笔画册': '儿童启蒙绘画册，内容丰富，培养孩子创造力和想象力',\r\n        '英文版图书': '原版英文图书，提高英语阅读能力，内容精彩有趣',\r\n        '衬衫': '商务休闲衬衫，免烫面料，版型合身，适合各种场合'\r\n      }\r\n      return descriptions[productName] || '优质商品，品质保证，欢迎选购'\r\n    },\r\n\r\n    // 获取使用本地图片的模拟商品数据\r\n    getMockProductsWithLocalImages() {\r\n      return [\r\n        {\r\n          id: 1,\r\n          name: '女上衣',\r\n          description: '时尚女性上衣，舒适面料，多种颜色可选，适合日常穿着和商务场合，版型修身显瘦',\r\n          imgs: require('@/assets/女上衣.png'),\r\n          price: 102.00,\r\n          discount: 0.85,\r\n          sales: 120,\r\n          recommend: true\r\n        },\r\n        {\r\n          id: 2,\r\n          name: '休闲鞋',\r\n          description: '舒适透气的休闲运动鞋，适合日常穿着，防滑耐磨，多种尺码可选，时尚百搭',\r\n          imgs: require('@/assets/休闲鞋.png'),\r\n          price: 162.00,\r\n          discount: 0.90,\r\n          sales: 85,\r\n          recommend: true\r\n        },\r\n        {\r\n          id: 3,\r\n          name: '威士忌 大瓶',\r\n          description: '优质威士忌，口感醇厚，适合收藏和品鉴，酒精度40%，容量750ml，包装精美',\r\n          imgs: require('@/assets/威士忌 大瓶.png'),\r\n          price: 427.50,\r\n          discount: 0.95,\r\n          sales: 45,\r\n          recommend: true\r\n        },\r\n        {\r\n          id: 4,\r\n          name: '墨镜',\r\n          description: '时尚墨镜，防紫外线，多种款式可选',\r\n          imgs: require('@/assets/墨镜.png'),\r\n          price: 199.00,\r\n          discount: 0.80,\r\n          sales: 65,\r\n          recommend: true\r\n        },\r\n        {\r\n          id: 5,\r\n          name: '桌椅套装',\r\n          description: '舒适桌椅套装，适合家庭使用',\r\n          imgs: require('@/assets/桌椅套装.png'),\r\n          price: 1299.00,\r\n          discount: 0.85,\r\n          sales: 30,\r\n          recommend: true\r\n        },\r\n        {\r\n          id: 6,\r\n          name: '儿童简笔画册',\r\n          description: '儿童简笔画册，培养孩子的绘画兴趣',\r\n          imgs: require('@/assets/儿童简笔画册.png'),\r\n          price: 39.90,\r\n          discount: 0.90,\r\n          sales: 120,\r\n          recommend: true\r\n        },\r\n        {\r\n          id: 7,\r\n          name: '英文版图书',\r\n          description: '英文版图书，提高英语阅读能力',\r\n          imgs: require('@/assets/英文版图书.png'),\r\n          price: 68.00,\r\n          discount: 0.95,\r\n          sales: 45,\r\n          recommend: true\r\n        },\r\n        {\r\n          id: 8,\r\n          name: '衬衫',\r\n          description: '时尚衬衫，舒适面料，多种颜色可选',\r\n          imgs: require('@/assets/衬衫.png'),\r\n          price: 129.00,\r\n          discount: 0.85,\r\n          sales: 80,\r\n          recommend: true\r\n        }\r\n      ];\r\n    },\r\n\r\n    // 处理搜索\r\n    async handleSearch(keyword) {\r\n      this.searchKeyword = keyword;\r\n      this.isSearchMode = true;\r\n      this.currentPage = 1;\r\n      await this.searchGoods();\r\n    },\r\n\r\n    // 搜索商品\r\n    async searchGoods() {\r\n      try {\r\n        this.loading = true;\r\n        let params = {\r\n          searchText: this.searchKeyword,\r\n          pageNum: this.currentPage,\r\n          pageSize: this.pageSize\r\n        };\r\n        \r\n        // 如果有分类筛选，添加分类参数\r\n        if (this.hasCategoryFilter) {\r\n          params = {\r\n            ...params,\r\n            mainCategory: this.selectedMainCategory,\r\n            subCategory: this.selectedSubCategory\r\n          };\r\n        }\r\n        \r\n        const response = await request.get('/good/page', params);\r\n\r\n        if (response && response.records) {\r\n          this.goodsList = response.records;\r\n          this.totalCount = response.total || 0;\r\n        } else {\r\n          this.goodsList = [];\r\n          this.totalCount = 0;\r\n        }\r\n      } catch (error) {\r\n        console.error('搜索失败:', error);\r\n        this.goodsList = [];\r\n        this.totalCount = 0;\r\n        this.$message.error('搜索失败，请重试');\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n\r\n    // 处理搜索结果\r\n    handleSearchResults(results) {\r\n      if (results && results.records) {\r\n        this.goodsList = results.records;\r\n        this.totalCount = results.total || 0;\r\n      }\r\n    },\r\n\r\n    // 清除搜索\r\n    handleClearSearch() {\r\n      this.searchKeyword = '';\r\n      this.isSearchMode = false;\r\n      this.currentPage = 1;\r\n      \r\n      // 只有在没有分类筛选时才清除所有筛选\r\n      if (!this.hasCategoryFilter) {\r\n        this.findFrontGoods();\r\n      } else {\r\n        // 有分类筛选时，重新搜索\r\n        this.searchGoods();\r\n      }\r\n    },\r\n    \r\n    // 清除分类筛选\r\n    clearCategoryFilter() {\r\n      // 触发ProductCategory组件的clearCategoryFilter方法\r\n      const productCategory = this.$refs.productCategory;\r\n      if (productCategory && typeof productCategory.clearCategoryFilter === 'function') {\r\n        productCategory.clearCategoryFilter();\r\n      }\r\n    },\r\n    \r\n    // 检查路由参数\r\n    checkRouteParams() {\r\n      const routeQuery = this.$route.query;\r\n      if (routeQuery.mainCategory || routeQuery.subCategory) {\r\n        this.selectedMainCategory = routeQuery.mainCategory || null;\r\n        this.selectedSubCategory = routeQuery.subCategory || null;\r\n        this.hasCategoryFilter = true;\r\n        \r\n        // 在组件挂载后设置分类\r\n        this.$nextTick(() => {\r\n          const productCategory = this.$refs.productCategory;\r\n          if (productCategory && typeof productCategory.setSelectedCategory === 'function') {\r\n            productCategory.setSelectedCategory({\r\n              mainCategory: this.selectedMainCategory,\r\n              subCategory: this.selectedSubCategory\r\n            });\r\n          }\r\n        });\r\n      }\r\n    },\r\n\r\n    // 处理轮播图点击\r\n    handleCarouselClick(item) {\r\n      console.log('点击轮播图商品:', item);\r\n      // 可以跳转到商品详情页\r\n      // this.$router.push(`/goods/${item.goodId}`);\r\n    },\r\n\r\n    // 处理页面大小变化\r\n    handleSizeChange(newSize) {\r\n      this.pageSize = newSize;\r\n      this.currentPage = 1;\r\n      if (this.isSearchMode) {\r\n        this.searchGoods();\r\n      } else {\r\n        this.findFrontGoods();\r\n      }\r\n    },\r\n\r\n    // 处理当前页变化\r\n    handleCurrentChange(newPage) {\r\n      this.currentPage = newPage;\r\n      if (this.isSearchMode) {\r\n        this.searchGoods();\r\n      } else {\r\n        this.findFrontGoods();\r\n      }\r\n    }\r\n  },\r\n\r\n\r\n\r\n  beforeUnmount() {\r\n    console.log('商品页面即将卸载');\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.goods-container {\r\n  max-width: 1200px;\r\n  margin: 20px auto;\r\n  padding: 0 16px;\r\n}\r\n\r\n.page-title {\r\n  text-align: center;\r\n  color: #333;\r\n  margin-bottom: 30px;\r\n  font-size: 32px;\r\n  font-weight: bold;\r\n  background: linear-gradient(45deg, #409eff, #67c23a);\r\n  -webkit-background-clip: text;\r\n  -webkit-text-fill-color: transparent;\r\n  background-clip: text;\r\n}\r\n\r\n.search-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 15px;\r\n  margin-bottom: 20px;\r\n  padding: 15px;\r\n  background: #f8f9fa;\r\n  border-radius: 8px;\r\n  border-left: 4px solid #409eff;\r\n}\r\n\r\n.search-count {\r\n  color: #666;\r\n  font-size: 14px;\r\n}\r\n\r\n.loading {\r\n  padding: 40px 0;\r\n  /* 添加最小高度，避免布局跳动 */\r\n  min-height: 400px;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n.no-data {\r\n  text-align: center;\r\n  padding: 60px 0;\r\n  /* 添加最小高度，避免布局跳动 */\r\n  min-height: 400px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n.goods-list {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\r\n  gap: 24px;\r\n  margin-bottom: 30px;\r\n  /* 添加最小高度，避免布局跳动 */\r\n  min-height: 400px;\r\n  /* 添加过渡效果 */\r\n  transition: all 0.3s ease-in-out;\r\n}\r\n\r\n@keyframes fadeIn {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(20px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n.goods-card {\r\n  border: 1px solid #e0e0e0;\r\n  border-radius: 16px;\r\n  overflow: hidden;\r\n  background: white;\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);\r\n  transition: all 0.3s ease;\r\n  position: relative;\r\n  cursor: pointer;\r\n  /* 添加动画效果 */\r\n  animation: fadeIn 0.6s ease-out;\r\n  /* 添加will-change优化性能 */\r\n  will-change: transform, opacity;\r\n}\r\n\r\n.goods-card:hover {\r\n  transform: translateY(-8px);\r\n  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);\r\n  border-color: #409eff;\r\n}\r\n\r\n.goods-image {\r\n  width: 100%;\r\n  height: 200px;\r\n  object-fit: cover;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.goods-card:hover .goods-image {\r\n  transform: scale(1.05);\r\n}\r\n\r\n.goods-info {\r\n  padding: 20px;\r\n}\r\n\r\n.goods-name {\r\n  font-size: 18px;\r\n  color: #333;\r\n  margin: 0 0 8px;\r\n  font-weight: bold;\r\n  line-height: 1.4;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n  overflow: hidden;\r\n}\r\n\r\n.goods-desc {\r\n  color: #666;\r\n  font-size: 14px;\r\n  margin: 0 0 12px;\r\n  line-height: 1.5;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n  overflow: hidden;\r\n}\r\n\r\n.goods-price {\r\n  color: #e60000;\r\n  margin: 15px 0;\r\n  font-size: 20px;\r\n  font-weight: bold;\r\n}\r\n\r\n.btn-add-cart {\r\n  width: 100%;\r\n  padding: 12px;\r\n  background: linear-gradient(45deg, #409eff, #67c23a);\r\n  color: white;\r\n  border: none;\r\n  border-radius: 8px;\r\n  cursor: pointer;\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  transition: all 0.3s ease;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.btn-add-cart:hover {\r\n  background: linear-gradient(45deg, #337ecc, #5daf34);\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);\r\n}\r\n\r\n.btn-add-cart:active {\r\n  transform: translateY(0);\r\n}\r\n\r\n.pagination-wrapper {\r\n  display: flex;\r\n  justify-content: center;\r\n  margin-top: 40px;\r\n  padding: 20px 0;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .goods-container {\r\n    padding: 0 12px;\r\n  }\r\n\r\n  .page-title {\r\n    font-size: 24px;\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .goods-list {\r\n    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\r\n    gap: 16px;\r\n  }\r\n\r\n  .goods-card {\r\n    border-radius: 12px;\r\n  }\r\n\r\n  .goods-image {\r\n    height: 160px;\r\n  }\r\n\r\n  .goods-info {\r\n    padding: 16px;\r\n  }\r\n\r\n  .goods-name {\r\n    font-size: 16px;\r\n  }\r\n\r\n  .goods-price {\r\n    font-size: 18px;\r\n  }\r\n\r\n  .btn-add-cart {\r\n    padding: 10px;\r\n    font-size: 14px;\r\n  }\r\n\r\n  .search-info {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 8px;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .goods-list {\r\n    grid-template-columns: 1fr;\r\n  }\r\n\r\n  .page-title {\r\n    font-size: 20px;\r\n  }\r\n}\r\n</style>"], "mappings": ";;EACOA,KAAK,EAAC;AAAiB;;;EAkBqBA,KAAK,EAAC;;;EAW7CA,KAAK,EAAC;AAAc;;;EAIRA,KAAK,EAAC;;;EAKdA,KAAK,EAAC;AAAY;;;;EAcrBA,KAAK,EAAC;AAAY;;EACjBA,KAAK,EAAC;AAAY;;EACnBA,KAAK,EAAC;AAAY;;EAClBA,KAAK,EAAC;AAAa;;;;EAiBmBA,KAAK,EAAC;;;;EAKnBA,KAAK,EAAC;;;;;;;;;;;uBA7E1CC,mBAAA,CAwFM,OAxFNC,UAwFM,GAvFJC,mBAAA,UAAa,E,0BACbC,mBAAA,CAAkC;IAA9BJ,KAAK,EAAC;EAAY,GAAC,QAAM,qBAE7BG,mBAAA,WAAc,EACdE,YAAA,CAA2DC,4BAAA;IAAvCC,eAAc,EAAEC,QAAA,CAAAC;EAAmB,8CAEvDN,mBAAA,WAAc,EACdE,YAAA,CAIEK,oBAAA;IAHCC,QAAM,EAAEH,QAAA,CAAAI,YAAY;IACpBC,eAAc,EAAEL,QAAA,CAAAM,mBAAmB;IACnCC,aAAY,EAAEP,QAAA,CAAAQ;6EAGjBb,mBAAA,UAAa,EACbE,YAAA,CAAiFY,0BAAA;IAAhEC,GAAG,EAAC,iBAAiB;IAAEC,gBAAe,EAAEX,QAAA,CAAAY;iDAEzDjB,mBAAA,iBAAoB,EACTkB,KAAA,CAAAC,aAAa,IAAID,KAAA,CAAAE,iBAAiB,I,cAA7CtB,mBAAA,CAYM,OAZNuB,UAYM,GAXJrB,mBAAA,YAAe,EACDkB,KAAA,CAAAC,aAAa,I,cAA3BG,YAAA,CAESC,iBAAA;;IAFoBC,IAAI,EAAC,MAAM;IAACC,QAAQ,EAAR,EAAQ;IAAEC,OAAK,EAAErB,QAAA,CAAAQ;;sBAAmB,MACxE,C,iBADwE,MACxE,GAAAc,gBAAA,CAAGT,KAAA,CAAAC,aAAa,iB;;uEAGrBnB,mBAAA,YAAe,EACDkB,KAAA,CAAAU,oBAAoB,I,cAAlCN,YAAA,CAESC,iBAAA;;IAF2BC,IAAI,EAAC,SAAS;IAACC,QAAQ,EAAR,EAAQ;IAAEC,OAAK,EAAErB,QAAA,CAAAwB;;sBAAqB,MAEzF,KAAAC,MAAA,QAAAA,MAAA,O,iBAFyF,QAEzF,mB;;uEAEA7B,mBAAA,CAA0D,QAA1D8B,UAA0D,EAA/B,MAAI,GAAAJ,gBAAA,CAAGT,KAAA,CAAAc,UAAU,IAAG,MAAI,gB,wCAGrDhC,mBAAA,UAAa,EACFkB,KAAA,CAAAe,OAAO,I,cAAlBnC,mBAAA,CAEM,OAFNoC,UAEM,GADJhC,YAAA,CAAkCiC,sBAAA;IAApBC,IAAI,EAAE,CAAC;IAAEC,QAAQ,EAAR;yBAIzBvC,mBAAA,CAuBMwC,SAAA;IAAAC,GAAA;EAAA,IAxBNvC,mBAAA,UAAa,EACbC,mBAAA,CAuBM,OAvBNuC,UAuBM,I,kBAtBJ1C,mBAAA,CAqBMwC,SAAA,QAAAG,WAAA,CAnBWvB,KAAA,CAAAwB,SAAS,EAAjBC,IAAI;yBAFb7C,mBAAA,CAqBM;MApBJD,KAAK,EAAC,YAAY;MAEjB0C,GAAG,EAAEI,IAAI,CAACC,EAAE;MACZC,OAAK,EAAAC,MAAA,IAAEzC,QAAA,CAAA0C,iBAAiB,CAACJ,IAAI;QAE9B1C,mBAAA,CAME;MALC+C,GAAG,EAAEL,IAAI,CAACM,IAAI;MACdC,GAAG,EAAEP,IAAI,CAACQ,IAAI;MACftD,KAAK,EAAC,aAAa;MAClBuD,OAAK,EAAAtB,MAAA,QAAAA,MAAA,UAAAuB,IAAA,KAAEhD,QAAA,CAAAiD,gBAAA,IAAAjD,QAAA,CAAAiD,gBAAA,IAAAD,IAAA,CAAgB;MACvBE,MAAI,EAAAzB,MAAA,QAAAA,MAAA,UAAAuB,IAAA,KAAEhD,QAAA,CAAAmD,eAAA,IAAAnD,QAAA,CAAAmD,eAAA,IAAAH,IAAA,CAAe;0DAExBpD,mBAAA,CAOM,OAPNwD,UAOM,GANJxD,mBAAA,CAA2C,MAA3CyD,UAA2C,EAAA/B,gBAAA,CAAjBgB,IAAI,CAACQ,IAAI,kBACnClD,mBAAA,CAA4D,KAA5D0D,WAA4D,EAAAhC,gBAAA,CAAnCgB,IAAI,CAACiB,WAAW,8BACzC3D,mBAAA,CAA6D,KAA7D4D,WAA6D,GAAtC5D,mBAAA,CAAkC,gBAA1B,GAAC,GAAA0B,gBAAA,CAAGgB,IAAI,CAACmB,KAAK,iB,GAC7C7D,mBAAA,CAES;MAFDJ,KAAK,EAAC,cAAc;MAAEgD,OAAK,EAAAkB,cAAA,CAAAjB,MAAA,IAAOzC,QAAA,CAAA2D,SAAS,CAACrB,IAAI;OAAG,SAE3D,iBAAAsB,WAAA,E;sFAKNjE,mBAAA,YAAe,EACfE,YAAA,CAKEgE,wBAAA;gBAJShD,KAAA,CAAAiD,gBAAgB;+DAAhBjD,KAAA,CAAAiD,gBAAgB,GAAArB,MAAA;IACxBsB,OAAO,EAAElD,KAAA,CAAAmD,eAAe;IACxBC,WAAW,EAAEjE,QAAA,CAAA2D,SAAS;IACtBO,QAAO,EAAElE,QAAA,CAAAmE;iFAGZxE,mBAAA,QAAW,E,CACCkB,KAAA,CAAAe,OAAO,IAAIf,KAAA,CAAAwB,SAAS,CAAC+B,MAAM,U,cAAvC3E,mBAAA,CAEM,OAFN4E,WAEM,GADJxE,YAAA,CAA+ByE,mBAAA;IAArBf,WAAW,EAAC;EAAM,G,wCAG9B5D,mBAAA,UAAa,EACFkB,KAAA,CAAAc,UAAU,GAAGd,KAAA,CAAA0D,QAAQ,I,cAAhC9E,mBAAA,CAUM,OAVN+E,WAUM,GATJ3E,YAAA,CAQE4E,wBAAA;IAPQ,cAAY,EAAE5D,KAAA,CAAA6D,WAAW;gEAAX7D,KAAA,CAAA6D,WAAW,GAAAjC,MAAA;IACzB,WAAS,EAAE5B,KAAA,CAAA0D,QAAQ;6DAAR1D,KAAA,CAAA0D,QAAQ,GAAA9B,MAAA;IAC1B,YAAU,EAAE,iBAAiB;IAC7BkC,KAAK,EAAE9D,KAAA,CAAAc,UAAU;IAClBiD,MAAM,EAAC,yCAAyC;IAC/CC,YAAW,EAAE7E,QAAA,CAAA8E,gBAAgB;IAC7BC,eAAc,EAAE/E,QAAA,CAAAgF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}