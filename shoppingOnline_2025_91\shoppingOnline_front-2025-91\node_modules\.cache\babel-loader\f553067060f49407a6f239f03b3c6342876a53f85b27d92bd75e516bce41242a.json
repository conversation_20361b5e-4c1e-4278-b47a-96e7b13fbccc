{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, with<PERSON><PERSON><PERSON> as _with<PERSON>ey<PERSON>, createCommentVNode as _createCommentVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, createElementVNode as _createElementVNode, createBlock as _createBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"search-container\"\n};\nconst _hoisted_2 = {\n  class: \"search-wrapper\"\n};\nconst _hoisted_3 = {\n  key: 0,\n  class: \"suggestions-dropdown\"\n};\nconst _hoisted_4 = [\"onClick\"];\nconst _hoisted_5 = {\n  class: \"suggestion-price\"\n};\nconst _hoisted_6 = {\n  key: 0,\n  class: \"hot-search\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_Search = _resolveComponent(\"Search\");\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_el_tag = _resolveComponent(\"el-tag\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_input, {\n    modelValue: $data.searchText,\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $data.searchText = $event),\n    placeholder: \"搜索商品...\",\n    size: \"large\",\n    class: \"search-input\",\n    onKeyup: _withKeys($options.handleSearch, [\"enter\"]),\n    onInput: $options.handleInput,\n    clearable: \"\"\n  }, {\n    prefix: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_Search)]),\n      _: 1 /* STABLE */\n    })]),\n    suffix: _withCtx(() => [_createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: $options.handleSearch,\n      loading: $data.searching,\n      class: \"search-btn\"\n    }, {\n      default: _withCtx(() => [_createTextVNode(_toDisplayString($data.searching ? '搜索中...' : '搜索'), 1 /* TEXT */)]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"onClick\", \"loading\"])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onKeyup\", \"onInput\"]), _createCommentVNode(\" 搜索建议下拉框 \"), $data.showSuggestions && $data.suggestions.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_3, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.suggestions, (suggestion, index) => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: index,\n      class: \"suggestion-item\",\n      onClick: $event => $options.selectSuggestion(suggestion)\n    }, [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_Search)]),\n      _: 1 /* STABLE */\n    }), _createElementVNode(\"span\", null, _toDisplayString(suggestion.name), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_5, \"¥\" + _toDisplayString(suggestion.price), 1 /* TEXT */)], 8 /* PROPS */, _hoisted_4);\n  }), 128 /* KEYED_FRAGMENT */))])) : _createCommentVNode(\"v-if\", true)]), _createCommentVNode(\" 热门搜索标签 \"), $data.hotKeywords.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_6, [_cache[1] || (_cache[1] = _createElementVNode(\"span\", {\n    class: \"hot-label\"\n  }, \"热门搜索：\", -1 /* CACHED */)), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.hotKeywords, keyword => {\n    return _openBlock(), _createBlock(_component_el_tag, {\n      key: keyword,\n      class: \"hot-tag\",\n      onClick: $event => $options.searchByKeyword(keyword),\n      effect: \"plain\"\n    }, {\n      default: _withCtx(() => [_createTextVNode(_toDisplayString(keyword), 1 /* TEXT */)]),\n      _: 2 /* DYNAMIC */\n    }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"]);\n  }), 128 /* KEYED_FRAGMENT */))])) : _createCommentVNode(\"v-if\", true)]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_createVNode", "_component_el_input", "$data", "searchText", "$event", "placeholder", "size", "onKeyup", "_with<PERSON><PERSON><PERSON>", "$options", "handleSearch", "onInput", "handleInput", "clearable", "prefix", "_withCtx", "_component_el_icon", "_component_Search", "suffix", "_component_el_button", "type", "onClick", "loading", "searching", "_createCommentVNode", "showSuggestions", "suggestions", "length", "_hoisted_3", "_Fragment", "_renderList", "suggestion", "index", "key", "selectSuggestion", "_toDisplayString", "name", "_hoisted_5", "price", "hotKeywords", "_hoisted_6", "keyword", "_createBlock", "_component_el_tag", "searchByKeyword", "effect"], "sources": ["D:\\2025_down\\project\\shoppingOnline_2025_91\\shoppingOnline_front-2025-91\\src\\components\\SearchBox.vue"], "sourcesContent": ["<template>\n  <div class=\"search-container\">\n    <div class=\"search-wrapper\">\n      <el-input\n        v-model=\"searchText\"\n        placeholder=\"搜索商品...\"\n        size=\"large\"\n        class=\"search-input\"\n        @keyup.enter=\"handleSearch\"\n        @input=\"handleInput\"\n        clearable\n      >\n        <template #prefix>\n          <el-icon><Search /></el-icon>\n        </template>\n        <template #suffix>\n          <el-button \n            type=\"primary\" \n            @click=\"handleSearch\"\n            :loading=\"searching\"\n            class=\"search-btn\"\n          >\n            {{ searching ? '搜索中...' : '搜索' }}\n          </el-button>\n        </template>\n      </el-input>\n      \n      <!-- 搜索建议下拉框 -->\n      <div v-if=\"showSuggestions && suggestions.length > 0\" class=\"suggestions-dropdown\">\n        <div \n          v-for=\"(suggestion, index) in suggestions\" \n          :key=\"index\"\n          class=\"suggestion-item\"\n          @click=\"selectSuggestion(suggestion)\"\n        >\n          <el-icon><Search /></el-icon>\n          <span>{{ suggestion.name }}</span>\n          <span class=\"suggestion-price\">¥{{ suggestion.price }}</span>\n        </div>\n      </div>\n    </div>\n    \n    <!-- 热门搜索标签 -->\n    <div class=\"hot-search\" v-if=\"hotKeywords.length > 0\">\n      <span class=\"hot-label\">热门搜索：</span>\n      <el-tag \n        v-for=\"keyword in hotKeywords\" \n        :key=\"keyword\"\n        class=\"hot-tag\"\n        @click=\"searchByKeyword(keyword)\"\n        effect=\"plain\"\n      >\n        {{ keyword }}\n      </el-tag>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { Search } from '@element-plus/icons-vue'\nimport request from '@/utils/request.js'\n\nexport default {\n  name: 'SearchBox',\n  \n  components: {\n    Search\n  },\n  \n  data() {\n    return {\n      searchText: '',\n      searching: false,\n      showSuggestions: false,\n      suggestions: [],\n      hotKeywords: ['手机', '电脑', '耳机', '键盘', '鼠标'],\n      debounceTimer: null\n    }\n  },\n  \n  methods: {\n    // 处理搜索\n    async handleSearch() {\n      if (!this.searchText.trim()) {\n        this.$message.warning('请输入搜索关键词')\n        return\n      }\n      \n      this.searching = true\n      this.showSuggestions = false\n      \n      try {\n        // 触发搜索事件，传递搜索文本给父组件\n        this.$emit('search', this.searchText.trim())\n        \n        // 可以在这里调用搜索API\n        const response = await request.get('/good/page', {\n          searchText: this.searchText.trim(),\n          pageNum: 1,\n          pageSize: 20\n        })\n        \n        // 将搜索结果传递给父组件\n        this.$emit('search-results', response)\n        \n      } catch (error) {\n        console.error('搜索失败:', error)\n        this.$message.error('搜索失败，请重试')\n      } finally {\n        this.searching = false\n      }\n    },\n    \n    // 处理输入变化\n    handleInput() {\n      // 清除之前的定时器\n      if (this.debounceTimer) {\n        clearTimeout(this.debounceTimer)\n      }\n      \n      // 设置防抖，500ms后执行搜索建议\n      this.debounceTimer = setTimeout(() => {\n        if (this.searchText.trim().length > 0) {\n          this.fetchSuggestions()\n        } else {\n          this.showSuggestions = false\n        }\n      }, 500)\n    },\n    \n    // 获取搜索建议\n    async fetchSuggestions() {\n      try {\n        const response = await request.get('/api/good/page', {\n          params: {\n            searchText: this.searchText.trim(),\n            pageNum: 1,\n            pageSize: 5\n          }\n        })\n        \n        if (response && response.records) {\n          this.suggestions = response.records.slice(0, 5) // 最多显示5个建议\n          this.showSuggestions = true\n        }\n      } catch (error) {\n        console.error('获取搜索建议失败:', error)\n        this.showSuggestions = false\n      }\n    },\n    \n    // 选择搜索建议\n    selectSuggestion(suggestion) {\n      this.searchText = suggestion.name\n      this.showSuggestions = false\n      this.handleSearch()\n    },\n    \n    // 通过热门关键词搜索\n    searchByKeyword(keyword) {\n      this.searchText = keyword\n      this.handleSearch()\n    },\n    \n    // 清空搜索\n    clearSearch() {\n      this.searchText = ''\n      this.showSuggestions = false\n      this.$emit('clear-search')\n    }\n  },\n  \n  mounted() {\n    // 点击外部关闭建议框\n    document.addEventListener('click', (e) => {\n      if (!this.$el.contains(e.target)) {\n        this.showSuggestions = false\n      }\n    })\n  },\n  \n  beforeUnmount() {\n    if (this.debounceTimer) {\n      clearTimeout(this.debounceTimer)\n    }\n  }\n}\n</script>\n\n<style scoped>\n.search-container {\n  width: 100%;\n  max-width: 600px;\n  margin: 0 auto 20px;\n}\n\n.search-wrapper {\n  position: relative;\n}\n\n.search-input {\n  width: 100%;\n}\n\n.search-input :deep(.el-input__inner) {\n  border-radius: 25px;\n  padding-left: 45px;\n  padding-right: 120px;\n  height: 50px;\n  font-size: 16px;\n}\n\n.search-btn {\n  border-radius: 20px;\n  margin-right: 5px;\n}\n\n.suggestions-dropdown {\n  position: absolute;\n  top: 100%;\n  left: 0;\n  right: 0;\n  background: white;\n  border: 1px solid #e4e7ed;\n  border-radius: 8px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\n  z-index: 1000;\n  max-height: 300px;\n  overflow-y: auto;\n}\n\n.suggestion-item {\n  display: flex;\n  align-items: center;\n  padding: 12px 16px;\n  cursor: pointer;\n  transition: background 0.2s;\n  gap: 8px;\n}\n\n.suggestion-item:hover {\n  background: #f5f7fa;\n}\n\n.suggestion-item:not(:last-child) {\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.suggestion-price {\n  margin-left: auto;\n  color: #e60000;\n  font-weight: bold;\n}\n\n.hot-search {\n  display: flex;\n  align-items: center;\n  flex-wrap: wrap;\n  gap: 8px;\n  margin-top: 15px;\n  padding: 0 5px;\n}\n\n.hot-label {\n  color: #666;\n  font-size: 14px;\n  white-space: nowrap;\n}\n\n.hot-tag {\n  cursor: pointer;\n  transition: all 0.2s;\n}\n\n.hot-tag:hover {\n  background: #409eff;\n  color: white;\n  border-color: #409eff;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .search-input :deep(.el-input__inner) {\n    height: 45px;\n    font-size: 14px;\n    padding-right: 100px;\n  }\n  \n  .search-btn {\n    font-size: 14px;\n  }\n  \n  .hot-search {\n    margin-top: 10px;\n  }\n  \n  .hot-label {\n    font-size: 12px;\n  }\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAkB;;EACtBA,KAAK,EAAC;AAAgB;;;EA0B6BA,KAAK,EAAC;;;;EASlDA,KAAK,EAAC;AAAkB;;;EAM/BA,KAAK,EAAC;;;;;;;;uBA1CbC,mBAAA,CAsDM,OAtDNC,UAsDM,GArDJC,mBAAA,CAsCM,OAtCNC,UAsCM,GArCJC,YAAA,CAsBWC,mBAAA;gBArBAC,KAAA,CAAAC,UAAU;+DAAVD,KAAA,CAAAC,UAAU,GAAAC,MAAA;IACnBC,WAAW,EAAC,SAAS;IACrBC,IAAI,EAAC,OAAO;IACZX,KAAK,EAAC,cAAc;IACnBY,OAAK,EAAAC,SAAA,CAAQC,QAAA,CAAAC,YAAY;IACzBC,OAAK,EAAEF,QAAA,CAAAG,WAAW;IACnBC,SAAS,EAAT;;IAEWC,MAAM,EAAAC,QAAA,CACf,MAA6B,CAA7Bf,YAAA,CAA6BgB,kBAAA;wBAApB,MAAU,CAAVhB,YAAA,CAAUiB,iBAAA,E;;;IAEVC,MAAM,EAAAH,QAAA,CACf,MAOY,CAPZf,YAAA,CAOYmB,oBAAA;MANVC,IAAI,EAAC,SAAS;MACbC,OAAK,EAAEZ,QAAA,CAAAC,YAAY;MACnBY,OAAO,EAAEpB,KAAA,CAAAqB,SAAS;MACnB5B,KAAK,EAAC;;wBAEN,MAAiC,C,kCAA9BO,KAAA,CAAAqB,SAAS,mC;;;;2DAKlBC,mBAAA,aAAgB,EACLtB,KAAA,CAAAuB,eAAe,IAAIvB,KAAA,CAAAwB,WAAW,CAACC,MAAM,Q,cAAhD/B,mBAAA,CAWM,OAXNgC,UAWM,I,kBAVJhC,mBAAA,CASMiC,SAAA,QAAAC,WAAA,CAR0B5B,KAAA,CAAAwB,WAAW,GAAjCK,UAAU,EAAEC,KAAK;yBAD3BpC,mBAAA,CASM;MAPHqC,GAAG,EAAED,KAAK;MACXrC,KAAK,EAAC,iBAAiB;MACtB0B,OAAK,EAAAjB,MAAA,IAAEK,QAAA,CAAAyB,gBAAgB,CAACH,UAAU;QAEnC/B,YAAA,CAA6BgB,kBAAA;wBAApB,MAAU,CAAVhB,YAAA,CAAUiB,iBAAA,E;;QACnBnB,mBAAA,CAAkC,cAAAqC,gBAAA,CAAzBJ,UAAU,CAACK,IAAI,kBACxBtC,mBAAA,CAA6D,QAA7DuC,UAA6D,EAA9B,GAAC,GAAAF,gBAAA,CAAGJ,UAAU,CAACO,KAAK,iB;2EAKzDd,mBAAA,YAAe,EACetB,KAAA,CAAAqC,WAAW,CAACZ,MAAM,Q,cAAhD/B,mBAAA,CAWM,OAXN4C,UAWM,G,0BAVJ1C,mBAAA,CAAoC;IAA9BH,KAAK,EAAC;EAAW,GAAC,OAAK,sB,kBAC7BC,mBAAA,CAQSiC,SAAA,QAAAC,WAAA,CAPW5B,KAAA,CAAAqC,WAAW,EAAtBE,OAAO;yBADhBC,YAAA,CAQSC,iBAAA;MANNV,GAAG,EAAEQ,OAAO;MACb9C,KAAK,EAAC,SAAS;MACd0B,OAAK,EAAAjB,MAAA,IAAEK,QAAA,CAAAmC,eAAe,CAACH,OAAO;MAC/BI,MAAM,EAAC;;wBAEP,MAAa,C,kCAAVJ,OAAO,iB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}