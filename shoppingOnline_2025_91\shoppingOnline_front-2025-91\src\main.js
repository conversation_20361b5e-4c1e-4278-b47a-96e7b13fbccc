// src/main.js
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import router from './router'

// 创建应用
const app = createApp(App)

// 全局错误处理
app.config.errorHandler = (err, vm, info) => {
  // 忽略 ResizeObserver 相关错误
  if (err.message && err.message.includes('ResizeObserver')) {
    return
  }
  console.error('Vue Error:', err, info)
}

// ✅ 使用 Pinia（注意：有括号！）
app.use(createPinia())

// 使用 Element Plus
app.use(ElementPlus)

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 使用 Vue Router
app.use(router)

// 抑制 ResizeObserver 错误
const resizeObserverErrorHandler = (e) => {
  if (e.message === 'ResizeObserver loop completed with undelivered notifications.') {
    e.stopImmediatePropagation()
  }
}
window.addEventListener('error', resizeObserverErrorHandler)

// 挂载
app.mount('#app')