{"ast": null, "code": "import { ShoppingCart } from '@element-plus/icons-vue';\nexport default {\n  name: 'ProductDetail',\n  components: {\n    ShoppingCart\n  },\n  props: {\n    modelValue: {\n      type: Boolean,\n      default: false\n    },\n    product: {\n      type: Object,\n      default: () => ({})\n    }\n  },\n  emits: ['update:modelValue', 'add-to-cart', 'buy-now'],\n  data() {\n    return {\n      activeTab: 'details',\n      addingToCart: false\n    };\n  },\n  computed: {\n    visible: {\n      get() {\n        return this.modelValue;\n      },\n      set(value) {\n        this.$emit('update:modelValue', value);\n      }\n    }\n  },\n  methods: {\n    handleClose() {\n      this.visible = false;\n    },\n    handleImageError(event) {\n      console.error('商品详情图片加载失败:', event.target.src);\n      event.target.src = '/images/women-top.jpg'; // 使用默认图片\n    },\n    async addToCart() {\n      try {\n        this.addingToCart = true;\n        this.$emit('add-to-cart', this.product);\n        this.$message.success(`已加入购物车：${this.product.name}`);\n      } catch (error) {\n        this.$message.error('加入购物车失败');\n      } finally {\n        this.addingToCart = false;\n      }\n    },\n    buyNow() {\n      this.$emit('buy-now', this.product);\n      this.$message.info(`立即购买：${this.product.name}`);\n    }\n  }\n};", "map": {"version": 3, "names": ["ShoppingCart", "name", "components", "props", "modelValue", "type", "Boolean", "default", "product", "Object", "emits", "data", "activeTab", "addingToCart", "computed", "visible", "get", "set", "value", "$emit", "methods", "handleClose", "handleImageError", "event", "console", "error", "target", "src", "addToCart", "$message", "success", "buyNow", "info"], "sources": ["D:\\2025_down\\project\\shoppingOnline_2025_91\\shoppingOnline_front-2025-91\\src\\components\\ProductDetail.vue"], "sourcesContent": ["<template>\n  <el-dialog\n    v-model=\"visible\"\n    :title=\"product.name\"\n    width=\"800px\"\n    :before-close=\"handleClose\"\n    class=\"product-detail-dialog\"\n  >\n    <div class=\"product-detail\" v-if=\"product\">\n      <div class=\"product-main\">\n        <!-- 商品图片 -->\n        <div class=\"product-image-section\">\n          <img \n            :src=\"product.imgs\" \n            :alt=\"product.name\" \n            class=\"product-image\"\n            @error=\"handleImageError\"\n          />\n        </div>\n        \n        <!-- 商品信息 -->\n        <div class=\"product-info-section\">\n          <h2 class=\"product-title\">{{ product.name }}</h2>\n          \n          <div class=\"product-price\">\n            <span class=\"current-price\">¥{{ product.price }}</span>\n            <span v-if=\"product.discount && product.discount < 1\" class=\"discount-info\">\n              {{ Math.round(product.discount * 100) }}折\n            </span>\n          </div>\n          \n          <div class=\"product-stats\">\n            <span class=\"sales-info\">销量: {{ product.sales || 0 }}</span>\n            <span class=\"category-info\" v-if=\"product.categoryName\">\n              分类: {{ product.categoryName }}\n            </span>\n          </div>\n          \n          <!-- 商品描述 -->\n          <div class=\"product-description\">\n            <h3>商品描述</h3>\n            <p>{{ product.description || '暂无详细描述' }}</p>\n          </div>\n          \n          <!-- 操作按钮 -->\n          <div class=\"product-actions\">\n            <el-button \n              type=\"primary\" \n              size=\"large\"\n              @click=\"addToCart\"\n              :loading=\"addingToCart\"\n            >\n              <el-icon><ShoppingCart /></el-icon>\n              加入购物车\n            </el-button>\n            <el-button \n              type=\"success\" \n              size=\"large\"\n              @click=\"buyNow\"\n            >\n              立即购买\n            </el-button>\n          </div>\n        </div>\n      </div>\n      \n      <!-- 详细信息 -->\n      <div class=\"product-details\">\n        <el-tabs v-model=\"activeTab\">\n          <el-tab-pane label=\"商品详情\" name=\"details\">\n            <div class=\"detail-content\">\n              <h4>商品详细信息</h4>\n              <p>{{ product.description || '暂无详细描述' }}</p>\n              \n              <div class=\"product-attributes\" v-if=\"product.attributes\">\n                <h4>商品属性</h4>\n                <el-descriptions :column=\"2\" border>\n                  <el-descriptions-item label=\"商品ID\">{{ product.id }}</el-descriptions-item>\n                  <el-descriptions-item label=\"商品名称\">{{ product.name }}</el-descriptions-item>\n                  <el-descriptions-item label=\"价格\">¥{{ product.price }}</el-descriptions-item>\n                  <el-descriptions-item label=\"折扣\" v-if=\"product.discount\">\n                    {{ Math.round(product.discount * 100) }}%\n                  </el-descriptions-item>\n                  <el-descriptions-item label=\"销量\">{{ product.sales || 0 }}</el-descriptions-item>\n                  <el-descriptions-item label=\"分类\" v-if=\"product.categoryName\">\n                    {{ product.categoryName }}\n                  </el-descriptions-item>\n                </el-descriptions>\n              </div>\n            </div>\n          </el-tab-pane>\n          \n          <el-tab-pane label=\"用户评价\" name=\"reviews\">\n            <div class=\"reviews-content\">\n              <el-empty description=\"暂无用户评价\" />\n            </div>\n          </el-tab-pane>\n        </el-tabs>\n      </div>\n    </div>\n    \n    <template #footer>\n      <span class=\"dialog-footer\">\n        <el-button @click=\"handleClose\">关闭</el-button>\n      </span>\n    </template>\n  </el-dialog>\n</template>\n\n<script>\nimport { ShoppingCart } from '@element-plus/icons-vue'\n\nexport default {\n  name: 'ProductDetail',\n  \n  components: {\n    ShoppingCart\n  },\n  \n  props: {\n    modelValue: {\n      type: Boolean,\n      default: false\n    },\n    product: {\n      type: Object,\n      default: () => ({})\n    }\n  },\n  \n  emits: ['update:modelValue', 'add-to-cart', 'buy-now'],\n  \n  data() {\n    return {\n      activeTab: 'details',\n      addingToCart: false\n    }\n  },\n  \n  computed: {\n    visible: {\n      get() {\n        return this.modelValue\n      },\n      set(value) {\n        this.$emit('update:modelValue', value)\n      }\n    }\n  },\n  \n  methods: {\n    handleClose() {\n      this.visible = false\n    },\n    \n    handleImageError(event) {\n      console.error('商品详情图片加载失败:', event.target.src)\n      event.target.src = '/images/women-top.jpg' // 使用默认图片\n    },\n    \n    async addToCart() {\n      try {\n        this.addingToCart = true\n        this.$emit('add-to-cart', this.product)\n        this.$message.success(`已加入购物车：${this.product.name}`)\n      } catch (error) {\n        this.$message.error('加入购物车失败')\n      } finally {\n        this.addingToCart = false\n      }\n    },\n    \n    buyNow() {\n      this.$emit('buy-now', this.product)\n      this.$message.info(`立即购买：${this.product.name}`)\n    }\n  }\n}\n</script>\n\n<style scoped>\n.product-detail-dialog {\n  border-radius: 12px;\n}\n\n.product-detail {\n  padding: 20px 0;\n}\n\n.product-main {\n  display: flex;\n  gap: 30px;\n  margin-bottom: 30px;\n}\n\n.product-image-section {\n  flex: 1;\n  max-width: 350px;\n}\n\n.product-image {\n  width: 100%;\n  height: 350px;\n  object-fit: cover;\n  border-radius: 8px;\n  border: 1px solid #e0e0e0;\n}\n\n.product-info-section {\n  flex: 1;\n  padding-left: 20px;\n}\n\n.product-title {\n  font-size: 24px;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 15px;\n  line-height: 1.4;\n}\n\n.product-price {\n  margin-bottom: 15px;\n}\n\n.current-price {\n  font-size: 28px;\n  font-weight: bold;\n  color: #e60000;\n  margin-right: 10px;\n}\n\n.discount-info {\n  background: #ff4757;\n  color: white;\n  padding: 2px 8px;\n  border-radius: 4px;\n  font-size: 12px;\n}\n\n.product-stats {\n  display: flex;\n  gap: 20px;\n  margin-bottom: 20px;\n  font-size: 14px;\n  color: #666;\n}\n\n.product-description {\n  margin-bottom: 30px;\n}\n\n.product-description h3 {\n  font-size: 16px;\n  font-weight: 500;\n  margin-bottom: 10px;\n  color: #333;\n}\n\n.product-description p {\n  color: #666;\n  line-height: 1.6;\n}\n\n.product-actions {\n  display: flex;\n  gap: 15px;\n}\n\n.product-actions .el-button {\n  flex: 1;\n  height: 50px;\n  font-size: 16px;\n}\n\n.product-details {\n  border-top: 1px solid #f0f0f0;\n  padding-top: 20px;\n}\n\n.detail-content h4 {\n  font-size: 16px;\n  font-weight: 500;\n  margin-bottom: 15px;\n  color: #333;\n}\n\n.detail-content p {\n  color: #666;\n  line-height: 1.6;\n  margin-bottom: 20px;\n}\n\n.product-attributes {\n  margin-top: 20px;\n}\n\n.reviews-content {\n  padding: 40px 0;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .product-main {\n    flex-direction: column;\n    gap: 20px;\n  }\n  \n  .product-info-section {\n    padding-left: 0;\n  }\n  \n  .product-actions {\n    flex-direction: column;\n  }\n}\n</style>\n"], "mappings": "AA8GA,SAASA,YAAW,QAAS,yBAAwB;AAErD,eAAe;EACbC,IAAI,EAAE,eAAe;EAErBC,UAAU,EAAE;IACVF;EACF,CAAC;EAEDG,KAAK,EAAE;IACLC,UAAU,EAAE;MACVC,IAAI,EAAEC,OAAO;MACbC,OAAO,EAAE;IACX,CAAC;IACDC,OAAO,EAAE;MACPH,IAAI,EAAEI,MAAM;MACZF,OAAO,EAAEA,CAAA,MAAO,CAAC,CAAC;IACpB;EACF,CAAC;EAEDG,KAAK,EAAE,CAAC,mBAAmB,EAAE,aAAa,EAAE,SAAS,CAAC;EAEtDC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,SAAS,EAAE,SAAS;MACpBC,YAAY,EAAE;IAChB;EACF,CAAC;EAEDC,QAAQ,EAAE;IACRC,OAAO,EAAE;MACPC,GAAGA,CAAA,EAAG;QACJ,OAAO,IAAI,CAACZ,UAAS;MACvB,CAAC;MACDa,GAAGA,CAACC,KAAK,EAAE;QACT,IAAI,CAACC,KAAK,CAAC,mBAAmB,EAAED,KAAK;MACvC;IACF;EACF,CAAC;EAEDE,OAAO,EAAE;IACPC,WAAWA,CAAA,EAAG;MACZ,IAAI,CAACN,OAAM,GAAI,KAAI;IACrB,CAAC;IAEDO,gBAAgBA,CAACC,KAAK,EAAE;MACtBC,OAAO,CAACC,KAAK,CAAC,aAAa,EAAEF,KAAK,CAACG,MAAM,CAACC,GAAG;MAC7CJ,KAAK,CAACG,MAAM,CAACC,GAAE,GAAI,uBAAsB,EAAE;IAC7C,CAAC;IAED,MAAMC,SAASA,CAAA,EAAG;MAChB,IAAI;QACF,IAAI,CAACf,YAAW,GAAI,IAAG;QACvB,IAAI,CAACM,KAAK,CAAC,aAAa,EAAE,IAAI,CAACX,OAAO;QACtC,IAAI,CAACqB,QAAQ,CAACC,OAAO,CAAC,UAAU,IAAI,CAACtB,OAAO,CAACP,IAAI,EAAE;MACrD,EAAE,OAAOwB,KAAK,EAAE;QACd,IAAI,CAACI,QAAQ,CAACJ,KAAK,CAAC,SAAS;MAC/B,UAAU;QACR,IAAI,CAACZ,YAAW,GAAI,KAAI;MAC1B;IACF,CAAC;IAEDkB,MAAMA,CAAA,EAAG;MACP,IAAI,CAACZ,KAAK,CAAC,SAAS,EAAE,IAAI,CAACX,OAAO;MAClC,IAAI,CAACqB,QAAQ,CAACG,IAAI,CAAC,QAAQ,IAAI,CAACxB,OAAO,CAACP,IAAI,EAAE;IAChD;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}