{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createTextVNode as _createTextVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, renderList as _renderList, Fragment as _Fragment, toDisplayString as _toDisplayString, withModifiers as _withModifiers } from \"vue\";\nconst _hoisted_1 = {\n  class: \"home-container\"\n};\nconst _hoisted_2 = {\n  class: \"search-section\"\n};\nconst _hoisted_3 = {\n  class: \"recommended-section\"\n};\nconst _hoisted_4 = {\n  class: \"section-title\"\n};\nconst _hoisted_5 = {\n  key: 0,\n  class: \"loading\"\n};\nconst _hoisted_6 = {\n  class: \"goods-grid\"\n};\nconst _hoisted_7 = [\"onClick\"];\nconst _hoisted_8 = [\"src\", \"alt\"];\nconst _hoisted_9 = {\n  class: \"goods-info\"\n};\nconst _hoisted_10 = {\n  class: \"goods-name\"\n};\nconst _hoisted_11 = {\n  class: \"goods-price\"\n};\nconst _hoisted_12 = {\n  class: \"more-goods\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_CarouselComponent = _resolveComponent(\"CarouselComponent\");\n  const _component_SearchBox = _resolveComponent(\"SearchBox\");\n  const _component_Star = _resolveComponent(\"Star\");\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_el_skeleton = _resolveComponent(\"el-skeleton\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" 欢迎横幅 \"), _cache[3] || (_cache[3] = _createElementVNode(\"div\", {\n    class: \"welcome-banner\"\n  }, [_createElementVNode(\"h1\", {\n    class: \"welcome-title\"\n  }, \"欢迎来到在线购物商城\"), _createElementVNode(\"p\", {\n    class: \"welcome-subtitle\"\n  }, \"发现优质商品，享受购物乐趣\")], -1 /* CACHED */)), _createCommentVNode(\" 轮播图组件 \"), _createVNode(_component_CarouselComponent, {\n    onCarouselClick: $options.handleCarouselClick\n  }, null, 8 /* PROPS */, [\"onCarouselClick\"]), _createCommentVNode(\" 搜索框组件 \"), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_SearchBox, {\n    onSearch: $options.handleSearch,\n    onSearchResults: $options.handleSearchResults,\n    onClearSearch: $options.handleClearSearch\n  }, null, 8 /* PROPS */, [\"onSearch\", \"onSearchResults\", \"onClearSearch\"])]), _createCommentVNode(\" 推荐商品区域 \"), _createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"h2\", _hoisted_4, [_createVNode(_component_el_icon, null, {\n    default: _withCtx(() => [_createVNode(_component_Star)]),\n    _: 1 /* STABLE */\n  }), _cache[0] || (_cache[0] = _createTextVNode(\" 推荐商品 \", -1 /* CACHED */))]), _createCommentVNode(\" 加载状态 \"), $data.loading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_5, [_createVNode(_component_el_skeleton, {\n    rows: 2,\n    animated: \"\"\n  })])) : (_openBlock(), _createElementBlock(_Fragment, {\n    key: 1\n  }, [_createCommentVNode(\" 商品列表 \"), _createElementVNode(\"div\", _hoisted_6, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.recommendedGoods, good => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      class: \"goods-card\",\n      key: good.id,\n      onClick: $event => $options.goToGoodDetail(good)\n    }, [_createElementVNode(\"img\", {\n      src: good.imgs,\n      alt: good.name,\n      class: \"goods-image\"\n    }, null, 8 /* PROPS */, _hoisted_8), _createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"h3\", _hoisted_10, _toDisplayString(good.name), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_11, \"¥\" + _toDisplayString(good.price), 1 /* TEXT */), _createVNode(_component_el_button, {\n      type: \"primary\",\n      size: \"small\",\n      onClick: _withModifiers($event => $options.addToCart(good), [\"stop\"])\n    }, {\n      default: _withCtx(() => [...(_cache[1] || (_cache[1] = [_createTextVNode(\" 加入购物车 \", -1 /* CACHED */)]))]),\n      _: 2 /* DYNAMIC */\n    }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])])], 8 /* PROPS */, _hoisted_7);\n  }), 128 /* KEYED_FRAGMENT */))])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)), _createCommentVNode(\" 查看更多按钮 \"), _createElementVNode(\"div\", _hoisted_12, [_createVNode(_component_el_button, {\n    type: \"primary\",\n    size: \"large\",\n    onClick: $options.goToGoodsPage\n  }, {\n    default: _withCtx(() => [...(_cache[2] || (_cache[2] = [_createTextVNode(\" 查看更多商品 \", -1 /* CACHED */)]))]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClick\"])])])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "_createVNode", "_component_CarouselComponent", "onCarouselClick", "$options", "handleCarouselClick", "_hoisted_2", "_component_SearchBox", "onSearch", "handleSearch", "onSearchResults", "handleSearchResults", "onClearSearch", "handleClearSearch", "_hoisted_3", "_hoisted_4", "_component_el_icon", "_component_Star", "$data", "loading", "_hoisted_5", "_component_el_skeleton", "rows", "animated", "_Fragment", "key", "_hoisted_6", "_renderList", "recommendedGoods", "good", "id", "onClick", "$event", "goToGoodDetail", "src", "imgs", "alt", "name", "_hoisted_9", "_hoisted_10", "_toDisplayString", "_hoisted_11", "price", "_component_el_button", "type", "size", "_withModifiers", "addToCart", "_cache", "_hoisted_12", "goToGoodsPage"], "sources": ["D:\\2025_down\\project\\shoppingOnline_2025_91\\shoppingOnline_front-2025-91\\src\\views\\Home.vue"], "sourcesContent": ["<template>\r\n  <div class=\"home-container\">\r\n    <!-- 欢迎横幅 -->\r\n    <div class=\"welcome-banner\">\r\n      <h1 class=\"welcome-title\">欢迎来到在线购物商城</h1>\r\n      <p class=\"welcome-subtitle\">发现优质商品，享受购物乐趣</p>\r\n    </div>\r\n\r\n    <!-- 轮播图组件 -->\r\n    <CarouselComponent @carousel-click=\"handleCarouselClick\" />\r\n\r\n    <!-- 搜索框组件 -->\r\n    <div class=\"search-section\">\r\n      <SearchBox\r\n        @search=\"handleSearch\"\r\n        @search-results=\"handleSearchResults\"\r\n        @clear-search=\"handleClearSearch\"\r\n      />\r\n    </div>\r\n\r\n    <!-- 推荐商品区域 -->\r\n    <div class=\"recommended-section\">\r\n      <h2 class=\"section-title\">\r\n        <el-icon><Star /></el-icon>\r\n        推荐商品\r\n      </h2>\r\n\r\n      <!-- 加载状态 -->\r\n      <div v-if=\"loading\" class=\"loading\">\r\n        <el-skeleton :rows=\"2\" animated />\r\n      </div>\r\n\r\n      <!-- 商品列表 -->\r\n      <div v-else class=\"goods-grid\">\r\n        <div\r\n          class=\"goods-card\"\r\n          v-for=\"good in recommendedGoods\"\r\n          :key=\"good.id\"\r\n          @click=\"goToGoodDetail(good)\"\r\n        >\r\n          <img :src=\"good.imgs\" :alt=\"good.name\" class=\"goods-image\" />\r\n          <div class=\"goods-info\">\r\n            <h3 class=\"goods-name\">{{ good.name }}</h3>\r\n            <p class=\"goods-price\">¥{{ good.price }}</p>\r\n            <el-button type=\"primary\" size=\"small\" @click.stop=\"addToCart(good)\">\r\n              加入购物车\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 查看更多按钮 -->\r\n      <div class=\"more-goods\">\r\n        <el-button type=\"primary\" size=\"large\" @click=\"goToGoodsPage\">\r\n          查看更多商品\r\n        </el-button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CarouselComponent from '@/components/Carousel.vue'\r\nimport SearchBox from '@/components/SearchBox.vue'\r\nimport { Star } from '@element-plus/icons-vue'\r\nimport request from '@/utils/request.js'\r\n\r\nexport default {\r\n  name: 'HomeView',\r\n\r\n  components: {\r\n    CarouselComponent,\r\n    SearchBox,\r\n    Star\r\n  },\r\n\r\n  data() {\r\n    return {\r\n      loading: true,\r\n      recommendedGoods: []\r\n    };\r\n  },\r\n\r\n  methods: {\r\n    // 获取推荐商品\r\n    async fetchRecommendedGoods() {\r\n      try {\r\n        this.loading = true;\r\n        const response = await request.get('/good');\r\n\r\n        if (Array.isArray(response) && response.length > 0) {\r\n          this.recommendedGoods = response.slice(0, 8); // 只显示前8个\r\n        } else {\r\n          // 如果后端没有数据，使用本地图片的模拟数据\r\n          this.recommendedGoods = this.getMockProductsWithLocalImages();\r\n          console.log('首页使用本地图片的模拟商品数据');\r\n        }\r\n      } catch (error) {\r\n        console.error('获取推荐商品失败:', error);\r\n        // 使用本地图片的模拟数据作为后备\r\n        this.recommendedGoods = this.getMockProductsWithLocalImages();\r\n        console.log('首页API请求失败，使用本地图片的模拟数据');\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n\r\n    // 获取使用本地图片的模拟商品数据\r\n    getMockProductsWithLocalImages() {\r\n      return [\r\n        {\r\n          id: 1,\r\n          name: '女上衣',\r\n          description: '时尚女性上衣，舒适面料，多种颜色可选',\r\n          imgs: '/images/women-top.jpg',\r\n          price: 102.00,\r\n          discount: 0.85,\r\n          sales: 120,\r\n          recommend: true\r\n        },\r\n        {\r\n          id: 2,\r\n          name: '休闲鞋',\r\n          description: '舒适透气的休闲运动鞋，适合日常穿着',\r\n          imgs: '/images/casual-shoes.jpg',\r\n          price: 162.00,\r\n          discount: 0.90,\r\n          sales: 85,\r\n          recommend: true\r\n        },\r\n        {\r\n          id: 3,\r\n          name: '威士忌 大瓶',\r\n          description: '优质威士忌，口感醇厚，适合收藏和品鉴',\r\n          imgs: '/images/威士忌 大瓶.jpg',\r\n          price: 427.50,\r\n          discount: 0.95,\r\n          sales: 45,\r\n          recommend: true\r\n        }\r\n      ];\r\n    },\r\n\r\n    // 处理轮播图点击\r\n    handleCarouselClick(item) {\r\n      console.log('点击轮播图商品:', item);\r\n      this.$message.success(`查看商品: ${item.goodName}`);\r\n      // 可以跳转到商品详情页\r\n    },\r\n\r\n    // 处理搜索\r\n    handleSearch(keyword) {\r\n      console.log('搜索:', keyword);\r\n      // 跳转到商品页面并传递搜索参数\r\n      this.$router.push({\r\n        path: '/goods',\r\n        query: { search: keyword }\r\n      });\r\n    },\r\n\r\n    // 处理搜索结果\r\n    handleSearchResults(results) {\r\n      console.log('搜索结果:', results);\r\n    },\r\n\r\n    // 清除搜索\r\n    handleClearSearch() {\r\n      console.log('清除搜索');\r\n    },\r\n\r\n    // 添加到购物车\r\n    addToCart(good) {\r\n      console.log('加入购物车:', good.name);\r\n      this.$message.success(`已加入购物车：${good.name}`);\r\n    },\r\n\r\n    // 跳转到商品详情\r\n    goToGoodDetail(good) {\r\n      console.log('查看商品详情:', good);\r\n      this.$message.info(`查看商品详情: ${good.name}`);\r\n    },\r\n\r\n    // 跳转到商品页面\r\n    goToGoodsPage() {\r\n      this.$router.push('/goods');\r\n    }\r\n  },\r\n\r\n  created() {\r\n    this.fetchRecommendedGoods();\r\n  },\r\n\r\n  mounted() {\r\n    this.fetchRecommendedGoods();\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.home-container {\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  padding: 20px 16px;\r\n}\r\n\r\n.welcome-banner {\r\n  text-align: center;\r\n  margin-bottom: 40px;\r\n  padding: 40px 20px;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border-radius: 16px;\r\n  color: white;\r\n}\r\n\r\n.welcome-title {\r\n  font-size: 36px;\r\n  margin: 0 0 10px;\r\n  font-weight: bold;\r\n}\r\n\r\n.welcome-subtitle {\r\n  font-size: 18px;\r\n  margin: 0;\r\n  opacity: 0.9;\r\n}\r\n\r\n.search-section {\r\n  margin-bottom: 50px;\r\n}\r\n\r\n.recommended-section {\r\n  margin-top: 50px;\r\n}\r\n\r\n.section-title {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  font-size: 24px;\r\n  color: #333;\r\n  margin-bottom: 30px;\r\n  padding-bottom: 10px;\r\n  border-bottom: 2px solid #409eff;\r\n}\r\n\r\n.loading {\r\n  padding: 40px 0;\r\n}\r\n\r\n.goods-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\r\n  gap: 20px;\r\n  margin-bottom: 40px;\r\n}\r\n\r\n.goods-card {\r\n  border: 1px solid #e0e0e0;\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  background: white;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  transition: all 0.3s ease;\r\n  cursor: pointer;\r\n}\r\n\r\n.goods-card:hover {\r\n  transform: translateY(-5px);\r\n  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);\r\n  border-color: #409eff;\r\n}\r\n\r\n.goods-image {\r\n  width: 100%;\r\n  height: 160px;\r\n  object-fit: cover;\r\n}\r\n\r\n.goods-info {\r\n  padding: 16px;\r\n}\r\n\r\n.goods-name {\r\n  font-size: 16px;\r\n  color: #333;\r\n  margin: 0 0 8px;\r\n  font-weight: bold;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n  overflow: hidden;\r\n}\r\n\r\n.goods-price {\r\n  color: #e60000;\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n  margin: 10px 0 15px;\r\n}\r\n\r\n.more-goods {\r\n  text-align: center;\r\n  margin-top: 40px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .home-container {\r\n    padding: 15px 12px;\r\n  }\r\n\r\n  .welcome-banner {\r\n    padding: 30px 15px;\r\n    margin-bottom: 30px;\r\n  }\r\n\r\n  .welcome-title {\r\n    font-size: 28px;\r\n  }\r\n\r\n  .welcome-subtitle {\r\n    font-size: 16px;\r\n  }\r\n\r\n  .goods-grid {\r\n    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\r\n    gap: 15px;\r\n  }\r\n\r\n  .section-title {\r\n    font-size: 20px;\r\n  }\r\n}\r\n</style>"], "mappings": ";;EACOA,KAAK,EAAC;AAAgB;;EAWpBA,KAAK,EAAC;AAAgB;;EAStBA,KAAK,EAAC;AAAqB;;EAC1BA,KAAK,EAAC;AAAe;;;EAMLA,KAAK,EAAC;;;EAKdA,KAAK,EAAC;AAAY;;;;EAQrBA,KAAK,EAAC;AAAY;;EACjBA,KAAK,EAAC;AAAY;;EACnBA,KAAK,EAAC;AAAa;;EASvBA,KAAK,EAAC;AAAY;;;;;;;;uBAnD3BC,mBAAA,CAyDM,OAzDNC,UAyDM,GAxDJC,mBAAA,UAAa,E,0BACbC,mBAAA,CAGM;IAHDJ,KAAK,EAAC;EAAgB,IACzBI,mBAAA,CAAyC;IAArCJ,KAAK,EAAC;EAAe,GAAC,YAAU,GACpCI,mBAAA,CAA6C;IAA1CJ,KAAK,EAAC;EAAkB,GAAC,eAAa,E,qBAG3CG,mBAAA,WAAc,EACdE,YAAA,CAA2DC,4BAAA;IAAvCC,eAAc,EAAEC,QAAA,CAAAC;EAAmB,8CAEvDN,mBAAA,WAAc,EACdC,mBAAA,CAMM,OANNM,UAMM,GALJL,YAAA,CAIEM,oBAAA;IAHCC,QAAM,EAAEJ,QAAA,CAAAK,YAAY;IACpBC,eAAc,EAAEN,QAAA,CAAAO,mBAAmB;IACnCC,aAAY,EAAER,QAAA,CAAAS;+EAInBd,mBAAA,YAAe,EACfC,mBAAA,CAoCM,OApCNc,UAoCM,GAnCJd,mBAAA,CAGK,MAHLe,UAGK,GAFHd,YAAA,CAA2Be,kBAAA;sBAAlB,MAAQ,CAARf,YAAA,CAAQgB,eAAA,E;;iDAAU,QAE7B,oB,GAEAlB,mBAAA,UAAa,EACFmB,KAAA,CAAAC,OAAO,I,cAAlBtB,mBAAA,CAEM,OAFNuB,UAEM,GADJnB,YAAA,CAAkCoB,sBAAA;IAApBC,IAAI,EAAE,CAAC;IAAEC,QAAQ,EAAR;yBAIzB1B,mBAAA,CAgBM2B,SAAA;IAAAC,GAAA;EAAA,IAjBN1B,mBAAA,UAAa,EACbC,mBAAA,CAgBM,OAhBN0B,UAgBM,I,kBAfJ7B,mBAAA,CAcM2B,SAAA,QAAAG,WAAA,CAZWT,KAAA,CAAAU,gBAAgB,EAAxBC,IAAI;yBAFbhC,mBAAA,CAcM;MAbJD,KAAK,EAAC,YAAY;MAEjB6B,GAAG,EAAEI,IAAI,CAACC,EAAE;MACZC,OAAK,EAAAC,MAAA,IAAE5B,QAAA,CAAA6B,cAAc,CAACJ,IAAI;QAE3B7B,mBAAA,CAA6D;MAAvDkC,GAAG,EAAEL,IAAI,CAACM,IAAI;MAAGC,GAAG,EAAEP,IAAI,CAACQ,IAAI;MAAEzC,KAAK,EAAC;yCAC7CI,mBAAA,CAMM,OANNsC,UAMM,GALJtC,mBAAA,CAA2C,MAA3CuC,WAA2C,EAAAC,gBAAA,CAAjBX,IAAI,CAACQ,IAAI,kBACnCrC,mBAAA,CAA4C,KAA5CyC,WAA4C,EAArB,GAAC,GAAAD,gBAAA,CAAGX,IAAI,CAACa,KAAK,kBACrCzC,YAAA,CAEY0C,oBAAA;MAFDC,IAAI,EAAC,SAAS;MAACC,IAAI,EAAC,OAAO;MAAEd,OAAK,EAAAe,cAAA,CAAAd,MAAA,IAAO5B,QAAA,CAAA2C,SAAS,CAAClB,IAAI;;wBAAG,MAErE,KAAAmB,MAAA,QAAAA,MAAA,O,iBAFqE,SAErE,mB;;;sFAKNjD,mBAAA,YAAe,EACfC,mBAAA,CAIM,OAJNiD,WAIM,GAHJhD,YAAA,CAEY0C,oBAAA;IAFDC,IAAI,EAAC,SAAS;IAACC,IAAI,EAAC,OAAO;IAAEd,OAAK,EAAE3B,QAAA,CAAA8C;;sBAAe,MAE9D,KAAAF,MAAA,QAAAA,MAAA,O,iBAF8D,UAE9D,mB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}