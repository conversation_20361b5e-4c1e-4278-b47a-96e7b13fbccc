{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport CarouselComponent from '@/components/Carousel.vue';\nimport SearchBox from '@/components/SearchBox.vue';\nimport { Star } from '@element-plus/icons-vue';\nimport request from '@/utils/request.js';\nexport default {\n  name: 'HomeView',\n  components: {\n    CarouselComponent,\n    SearchBox,\n    Star\n  },\n  data() {\n    return {\n      loading: true,\n      recommendedGoods: []\n    };\n  },\n  methods: {\n    // 获取推荐商品\n    async fetchRecommendedGoods() {\n      try {\n        this.loading = true;\n        const response = await request.get('/good');\n        if (Array.isArray(response) && response.length > 0) {\n          this.recommendedGoods = response.slice(0, 8); // 只显示前8个\n        } else {\n          // 如果后端没有数据，使用模拟数据\n          this.recommendedGoods = this.getMockProducts();\n          console.log('首页使用模拟商品数据');\n        }\n      } catch (error) {\n        console.error('获取推荐商品失败:', error);\n        // 使用模拟数据作为后备\n        this.recommendedGoods = this.getMockProducts();\n        console.log('首页API请求失败，使用模拟数据');\n      } finally {\n        this.loading = false;\n      }\n    },\n    // 获取模拟商品数据\n    getMockProducts() {\n      return [{\n        id: 1,\n        name: '女上衣',\n        description: '时尚女性上衣，舒适面料，多种颜色可选',\n        imgs: 'https://img.alicdn.com/bao/uploaded/i4/705106688/O1CN01oqb6iZ1zH9i4qLBjJ_!!705106688.jpg',\n        price: 102.00,\n        discount: 0.85,\n        sales: 120,\n        recommend: true\n      }, {\n        id: 2,\n        name: '休闲鞋',\n        description: '舒适透气的休闲运动鞋，适合日常穿着',\n        imgs: 'https://ts1.tc.mm.bing.net/th/id/OIP-C.jpOvme9hraBerYgowHeyRwHaE7?w=251&h=211&c=8&rs=1&qlt=90&o=6&dpr=1.3&pid=3.1&rm=2',\n        price: 162.00,\n        discount: 0.90,\n        sales: 85,\n        recommend: true\n      }, {\n        id: 3,\n        name: '威士忌 大瓶',\n        description: '优质威士忌，口感醇厚，适合收藏和品鉴',\n        imgs: 'https://ts1.tc.mm.bing.net/th/id/OIP-C.9G_O0NZgTOl3FXFZrOOf1AHaE7?w=280&h=211&c=8&rs=1&qlt=90&o=6&dpr=1.3&pid=3.1&rm=2',\n        price: 427.50,\n        discount: 0.95,\n        sales: 45,\n        recommend: true\n      }];\n    },\n    // 处理轮播图点击\n    handleCarouselClick(item) {\n      console.log('点击轮播图商品:', item);\n      this.$message.success(`查看商品: ${item.goodName}`);\n      // 可以跳转到商品详情页\n    },\n    // 处理搜索\n    handleSearch(keyword) {\n      console.log('搜索:', keyword);\n      // 跳转到商品页面并传递搜索参数\n      this.$router.push({\n        path: '/goods',\n        query: {\n          search: keyword\n        }\n      });\n    },\n    // 处理搜索结果\n    handleSearchResults(results) {\n      console.log('搜索结果:', results);\n    },\n    // 清除搜索\n    handleClearSearch() {\n      console.log('清除搜索');\n    },\n    // 添加到购物车\n    addToCart(good) {\n      console.log('加入购物车:', good.name);\n      this.$message.success(`已加入购物车：${good.name}`);\n    },\n    // 跳转到商品详情\n    goToGoodDetail(good) {\n      console.log('查看商品详情:', good);\n      this.$message.info(`查看商品详情: ${good.name}`);\n    },\n    // 跳转到商品页面\n    goToGoodsPage() {\n      this.$router.push('/goods');\n    }\n  },\n  created() {\n    this.fetchRecommendedGoods();\n  },\n  mounted() {\n    this.fetchRecommendedGoods();\n  }\n};", "map": {"version": 3, "names": ["CarouselComponent", "SearchBox", "Star", "request", "name", "components", "data", "loading", "recommendedGoods", "methods", "fetchRecommendedGoods", "response", "get", "Array", "isArray", "length", "slice", "getMockProducts", "console", "log", "error", "id", "description", "imgs", "price", "discount", "sales", "recommend", "handleCarouselClick", "item", "$message", "success", "<PERSON><PERSON><PERSON>", "handleSearch", "keyword", "$router", "push", "path", "query", "search", "handleSearchResults", "results", "handleClearSearch", "addToCart", "good", "goToGoodDetail", "info", "goToGoodsPage", "created", "mounted"], "sources": ["D:\\2025_down\\project\\shoppingOnline_2025_91\\shoppingOnline_front-2025-91\\src\\views\\Home.vue"], "sourcesContent": ["<template>\r\n  <div class=\"home-container\">\r\n    <!-- 欢迎横幅 -->\r\n    <div class=\"welcome-banner\">\r\n      <h1 class=\"welcome-title\">欢迎来到在线购物商城</h1>\r\n      <p class=\"welcome-subtitle\">发现优质商品，享受购物乐趣</p>\r\n    </div>\r\n\r\n    <!-- 轮播图组件 -->\r\n    <CarouselComponent @carousel-click=\"handleCarouselClick\" />\r\n\r\n    <!-- 搜索框组件 -->\r\n    <div class=\"search-section\">\r\n      <SearchBox\r\n        @search=\"handleSearch\"\r\n        @search-results=\"handleSearchResults\"\r\n        @clear-search=\"handleClearSearch\"\r\n      />\r\n    </div>\r\n\r\n    <!-- 推荐商品区域 -->\r\n    <div class=\"recommended-section\">\r\n      <h2 class=\"section-title\">\r\n        <el-icon><Star /></el-icon>\r\n        推荐商品\r\n      </h2>\r\n\r\n      <!-- 加载状态 -->\r\n      <div v-if=\"loading\" class=\"loading\">\r\n        <el-skeleton :rows=\"2\" animated />\r\n      </div>\r\n\r\n      <!-- 商品列表 -->\r\n      <div v-else class=\"goods-grid\">\r\n        <div\r\n          class=\"goods-card\"\r\n          v-for=\"good in recommendedGoods\"\r\n          :key=\"good.id\"\r\n          @click=\"goToGoodDetail(good)\"\r\n        >\r\n          <img :src=\"good.imgs\" :alt=\"good.name\" class=\"goods-image\" />\r\n          <div class=\"goods-info\">\r\n            <h3 class=\"goods-name\">{{ good.name }}</h3>\r\n            <p class=\"goods-price\">¥{{ good.price }}</p>\r\n            <el-button type=\"primary\" size=\"small\" @click.stop=\"addToCart(good)\">\r\n              加入购物车\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 查看更多按钮 -->\r\n      <div class=\"more-goods\">\r\n        <el-button type=\"primary\" size=\"large\" @click=\"goToGoodsPage\">\r\n          查看更多商品\r\n        </el-button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CarouselComponent from '@/components/Carousel.vue'\r\nimport SearchBox from '@/components/SearchBox.vue'\r\nimport { Star } from '@element-plus/icons-vue'\r\nimport request from '@/utils/request.js'\r\n\r\nexport default {\r\n  name: 'HomeView',\r\n\r\n  components: {\r\n    CarouselComponent,\r\n    SearchBox,\r\n    Star\r\n  },\r\n\r\n  data() {\r\n    return {\r\n      loading: true,\r\n      recommendedGoods: []\r\n    };\r\n  },\r\n\r\n  methods: {\r\n    // 获取推荐商品\r\n    async fetchRecommendedGoods() {\r\n      try {\r\n        this.loading = true;\r\n        const response = await request.get('/good');\r\n\r\n        if (Array.isArray(response) && response.length > 0) {\r\n          this.recommendedGoods = response.slice(0, 8); // 只显示前8个\r\n        } else {\r\n          // 如果后端没有数据，使用模拟数据\r\n          this.recommendedGoods = this.getMockProducts();\r\n          console.log('首页使用模拟商品数据');\r\n        }\r\n      } catch (error) {\r\n        console.error('获取推荐商品失败:', error);\r\n        // 使用模拟数据作为后备\r\n        this.recommendedGoods = this.getMockProducts();\r\n        console.log('首页API请求失败，使用模拟数据');\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n\r\n    // 获取模拟商品数据\r\n    getMockProducts() {\r\n      return [\r\n        {\r\n          id: 1,\r\n          name: '女上衣',\r\n          description: '时尚女性上衣，舒适面料，多种颜色可选',\r\n          imgs: 'https://img.alicdn.com/bao/uploaded/i4/705106688/O1CN01oqb6iZ1zH9i4qLBjJ_!!705106688.jpg',\r\n          price: 102.00,\r\n          discount: 0.85,\r\n          sales: 120,\r\n          recommend: true\r\n        },\r\n        {\r\n          id: 2,\r\n          name: '休闲鞋',\r\n          description: '舒适透气的休闲运动鞋，适合日常穿着',\r\n          imgs: 'https://ts1.tc.mm.bing.net/th/id/OIP-C.jpOvme9hraBerYgowHeyRwHaE7?w=251&h=211&c=8&rs=1&qlt=90&o=6&dpr=1.3&pid=3.1&rm=2',\r\n          price: 162.00,\r\n          discount: 0.90,\r\n          sales: 85,\r\n          recommend: true\r\n        },\r\n        {\r\n          id: 3,\r\n          name: '威士忌 大瓶',\r\n          description: '优质威士忌，口感醇厚，适合收藏和品鉴',\r\n          imgs: 'https://ts1.tc.mm.bing.net/th/id/OIP-C.9G_O0NZgTOl3FXFZrOOf1AHaE7?w=280&h=211&c=8&rs=1&qlt=90&o=6&dpr=1.3&pid=3.1&rm=2',\r\n          price: 427.50,\r\n          discount: 0.95,\r\n          sales: 45,\r\n          recommend: true\r\n        }\r\n      ];\r\n    },\r\n\r\n    // 处理轮播图点击\r\n    handleCarouselClick(item) {\r\n      console.log('点击轮播图商品:', item);\r\n      this.$message.success(`查看商品: ${item.goodName}`);\r\n      // 可以跳转到商品详情页\r\n    },\r\n\r\n    // 处理搜索\r\n    handleSearch(keyword) {\r\n      console.log('搜索:', keyword);\r\n      // 跳转到商品页面并传递搜索参数\r\n      this.$router.push({\r\n        path: '/goods',\r\n        query: { search: keyword }\r\n      });\r\n    },\r\n\r\n    // 处理搜索结果\r\n    handleSearchResults(results) {\r\n      console.log('搜索结果:', results);\r\n    },\r\n\r\n    // 清除搜索\r\n    handleClearSearch() {\r\n      console.log('清除搜索');\r\n    },\r\n\r\n    // 添加到购物车\r\n    addToCart(good) {\r\n      console.log('加入购物车:', good.name);\r\n      this.$message.success(`已加入购物车：${good.name}`);\r\n    },\r\n\r\n    // 跳转到商品详情\r\n    goToGoodDetail(good) {\r\n      console.log('查看商品详情:', good);\r\n      this.$message.info(`查看商品详情: ${good.name}`);\r\n    },\r\n\r\n    // 跳转到商品页面\r\n    goToGoodsPage() {\r\n      this.$router.push('/goods');\r\n    }\r\n  },\r\n\r\n  created() {\r\n    this.fetchRecommendedGoods();\r\n  },\r\n\r\n  mounted() {\r\n    this.fetchRecommendedGoods();\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.home-container {\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  padding: 20px 16px;\r\n}\r\n\r\n.welcome-banner {\r\n  text-align: center;\r\n  margin-bottom: 40px;\r\n  padding: 40px 20px;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border-radius: 16px;\r\n  color: white;\r\n}\r\n\r\n.welcome-title {\r\n  font-size: 36px;\r\n  margin: 0 0 10px;\r\n  font-weight: bold;\r\n}\r\n\r\n.welcome-subtitle {\r\n  font-size: 18px;\r\n  margin: 0;\r\n  opacity: 0.9;\r\n}\r\n\r\n.search-section {\r\n  margin-bottom: 50px;\r\n}\r\n\r\n.recommended-section {\r\n  margin-top: 50px;\r\n}\r\n\r\n.section-title {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  font-size: 24px;\r\n  color: #333;\r\n  margin-bottom: 30px;\r\n  padding-bottom: 10px;\r\n  border-bottom: 2px solid #409eff;\r\n}\r\n\r\n.loading {\r\n  padding: 40px 0;\r\n}\r\n\r\n.goods-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\r\n  gap: 20px;\r\n  margin-bottom: 40px;\r\n}\r\n\r\n.goods-card {\r\n  border: 1px solid #e0e0e0;\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  background: white;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  transition: all 0.3s ease;\r\n  cursor: pointer;\r\n}\r\n\r\n.goods-card:hover {\r\n  transform: translateY(-5px);\r\n  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);\r\n  border-color: #409eff;\r\n}\r\n\r\n.goods-image {\r\n  width: 100%;\r\n  height: 160px;\r\n  object-fit: cover;\r\n}\r\n\r\n.goods-info {\r\n  padding: 16px;\r\n}\r\n\r\n.goods-name {\r\n  font-size: 16px;\r\n  color: #333;\r\n  margin: 0 0 8px;\r\n  font-weight: bold;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n  overflow: hidden;\r\n}\r\n\r\n.goods-price {\r\n  color: #e60000;\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n  margin: 10px 0 15px;\r\n}\r\n\r\n.more-goods {\r\n  text-align: center;\r\n  margin-top: 40px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .home-container {\r\n    padding: 15px 12px;\r\n  }\r\n\r\n  .welcome-banner {\r\n    padding: 30px 15px;\r\n    margin-bottom: 30px;\r\n  }\r\n\r\n  .welcome-title {\r\n    font-size: 28px;\r\n  }\r\n\r\n  .welcome-subtitle {\r\n    font-size: 16px;\r\n  }\r\n\r\n  .goods-grid {\r\n    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\r\n    gap: 15px;\r\n  }\r\n\r\n  .section-title {\r\n    font-size: 20px;\r\n  }\r\n}\r\n</style>"], "mappings": ";AA8DA,OAAOA,iBAAgB,MAAO,2BAA0B;AACxD,OAAOC,SAAQ,MAAO,4BAA2B;AACjD,SAASC,IAAG,QAAS,yBAAwB;AAC7C,OAAOC,OAAM,MAAO,oBAAmB;AAEvC,eAAe;EACbC,IAAI,EAAE,UAAU;EAEhBC,UAAU,EAAE;IACVL,iBAAiB;IACjBC,SAAS;IACTC;EACF,CAAC;EAEDI,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,OAAO,EAAE,IAAI;MACbC,gBAAgB,EAAE;IACpB,CAAC;EACH,CAAC;EAEDC,OAAO,EAAE;IACP;IACA,MAAMC,qBAAqBA,CAAA,EAAG;MAC5B,IAAI;QACF,IAAI,CAACH,OAAM,GAAI,IAAI;QACnB,MAAMI,QAAO,GAAI,MAAMR,OAAO,CAACS,GAAG,CAAC,OAAO,CAAC;QAE3C,IAAIC,KAAK,CAACC,OAAO,CAACH,QAAQ,KAAKA,QAAQ,CAACI,MAAK,GAAI,CAAC,EAAE;UAClD,IAAI,CAACP,gBAAe,GAAIG,QAAQ,CAACK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;QAChD,OAAO;UACL;UACA,IAAI,CAACR,gBAAe,GAAI,IAAI,CAACS,eAAe,CAAC,CAAC;UAC9CC,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC;QAC3B;MACF,EAAE,OAAOC,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjC;QACA,IAAI,CAACZ,gBAAe,GAAI,IAAI,CAACS,eAAe,CAAC,CAAC;QAC9CC,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;MACjC,UAAU;QACR,IAAI,CAACZ,OAAM,GAAI,KAAK;MACtB;IACF,CAAC;IAED;IACAU,eAAeA,CAAA,EAAG;MAChB,OAAO,CACL;QACEI,EAAE,EAAE,CAAC;QACLjB,IAAI,EAAE,KAAK;QACXkB,WAAW,EAAE,oBAAoB;QACjCC,IAAI,EAAE,0FAA0F;QAChGC,KAAK,EAAE,MAAM;QACbC,QAAQ,EAAE,IAAI;QACdC,KAAK,EAAE,GAAG;QACVC,SAAS,EAAE;MACb,CAAC,EACD;QACEN,EAAE,EAAE,CAAC;QACLjB,IAAI,EAAE,KAAK;QACXkB,WAAW,EAAE,mBAAmB;QAChCC,IAAI,EAAE,wHAAwH;QAC9HC,KAAK,EAAE,MAAM;QACbC,QAAQ,EAAE,IAAI;QACdC,KAAK,EAAE,EAAE;QACTC,SAAS,EAAE;MACb,CAAC,EACD;QACEN,EAAE,EAAE,CAAC;QACLjB,IAAI,EAAE,QAAQ;QACdkB,WAAW,EAAE,oBAAoB;QACjCC,IAAI,EAAE,wHAAwH;QAC9HC,KAAK,EAAE,MAAM;QACbC,QAAQ,EAAE,IAAI;QACdC,KAAK,EAAE,EAAE;QACTC,SAAS,EAAE;MACb,EACD;IACH,CAAC;IAED;IACAC,mBAAmBA,CAACC,IAAI,EAAE;MACxBX,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEU,IAAI,CAAC;MAC7B,IAAI,CAACC,QAAQ,CAACC,OAAO,CAAC,SAASF,IAAI,CAACG,QAAQ,EAAE,CAAC;MAC/C;IACF,CAAC;IAED;IACAC,YAAYA,CAACC,OAAO,EAAE;MACpBhB,OAAO,CAACC,GAAG,CAAC,KAAK,EAAEe,OAAO,CAAC;MAC3B;MACA,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC;QAChBC,IAAI,EAAE,QAAQ;QACdC,KAAK,EAAE;UAAEC,MAAM,EAAEL;QAAQ;MAC3B,CAAC,CAAC;IACJ,CAAC;IAED;IACAM,mBAAmBA,CAACC,OAAO,EAAE;MAC3BvB,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEsB,OAAO,CAAC;IAC/B,CAAC;IAED;IACAC,iBAAiBA,CAAA,EAAG;MAClBxB,OAAO,CAACC,GAAG,CAAC,MAAM,CAAC;IACrB,CAAC;IAED;IACAwB,SAASA,CAACC,IAAI,EAAE;MACd1B,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEyB,IAAI,CAACxC,IAAI,CAAC;MAChC,IAAI,CAAC0B,QAAQ,CAACC,OAAO,CAAC,UAAUa,IAAI,CAACxC,IAAI,EAAE,CAAC;IAC9C,CAAC;IAED;IACAyC,cAAcA,CAACD,IAAI,EAAE;MACnB1B,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEyB,IAAI,CAAC;MAC5B,IAAI,CAACd,QAAQ,CAACgB,IAAI,CAAC,WAAWF,IAAI,CAACxC,IAAI,EAAE,CAAC;IAC5C,CAAC;IAED;IACA2C,aAAaA,CAAA,EAAG;MACd,IAAI,CAACZ,OAAO,CAACC,IAAI,CAAC,QAAQ,CAAC;IAC7B;EACF,CAAC;EAEDY,OAAOA,CAAA,EAAG;IACR,IAAI,CAACtC,qBAAqB,CAAC,CAAC;EAC9B,CAAC;EAEDuC,OAAOA,CAAA,EAAG;IACR,IAAI,CAACvC,qBAAqB,CAAC,CAAC;EAC9B;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}