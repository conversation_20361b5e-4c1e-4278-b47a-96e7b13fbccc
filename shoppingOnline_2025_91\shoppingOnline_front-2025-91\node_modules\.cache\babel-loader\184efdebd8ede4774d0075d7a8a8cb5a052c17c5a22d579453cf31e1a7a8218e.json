{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, toDisplayString as _toDisplayString, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nimport _imports_0 from '@/resource/logo.png';\nconst _hoisted_1 = {\n  class: \"main-layout\"\n};\nconst _hoisted_2 = {\n  class: \"header\"\n};\nconst _hoisted_3 = {\n  class: \"header-content\"\n};\nconst _hoisted_4 = {\n  class: \"nav-menu\"\n};\nconst _hoisted_5 = {\n  class: \"user-actions\"\n};\nconst _hoisted_6 = {\n  key: 0,\n  class: \"user-info\"\n};\nconst _hoisted_7 = {\n  class: \"user-dropdown\"\n};\nconst _hoisted_8 = {\n  class: \"username\"\n};\nconst _hoisted_9 = {\n  key: 1,\n  class: \"login-actions\"\n};\nconst _hoisted_10 = {\n  class: \"main-content\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_menu_item = _resolveComponent(\"el-menu-item\");\n  const _component_el_menu = _resolveComponent(\"el-menu\");\n  const _component_User = _resolveComponent(\"User\");\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_el_avatar = _resolveComponent(\"el-avatar\");\n  const _component_ArrowDown = _resolveComponent(\"ArrowDown\");\n  const _component_el_dropdown_item = _resolveComponent(\"el-dropdown-item\");\n  const _component_List = _resolveComponent(\"List\");\n  const _component_ShoppingCart = _resolveComponent(\"ShoppingCart\");\n  const _component_SwitchButton = _resolveComponent(\"SwitchButton\");\n  const _component_el_dropdown_menu = _resolveComponent(\"el-dropdown-menu\");\n  const _component_el_dropdown = _resolveComponent(\"el-dropdown\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_router_view = _resolveComponent(\"router-view\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" 顶部导航栏 \"), _createElementVNode(\"header\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_cache[11] || (_cache[11] = _createElementVNode(\"div\", {\n    class: \"logo\"\n  }, [_createElementVNode(\"img\", {\n    src: _imports_0,\n    alt: \"商城Logo\",\n    class: \"logo-img\"\n  }), _createElementVNode(\"span\", {\n    class: \"logo-text\"\n  }, \"在线购物商城\")], -1 /* CACHED */)), _createCommentVNode(\" 导航菜单 \"), _createElementVNode(\"nav\", _hoisted_4, [_createVNode(_component_el_menu, {\n    mode: \"horizontal\",\n    \"default-active\": $data.activeIndex,\n    class: \"nav-menu-el\",\n    onSelect: $options.handleMenuSelect\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_menu_item, {\n      index: \"home\"\n    }, {\n      default: _withCtx(() => [...(_cache[0] || (_cache[0] = [_createTextVNode(\"首页\", -1 /* CACHED */)]))]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_menu_item, {\n      index: \"goods\"\n    }, {\n      default: _withCtx(() => [...(_cache[1] || (_cache[1] = [_createTextVNode(\"商品\", -1 /* CACHED */)]))]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_menu_item, {\n      index: \"test\"\n    }, {\n      default: _withCtx(() => [...(_cache[2] || (_cache[2] = [_createTextVNode(\"功能测试\", -1 /* CACHED */)]))]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_menu_item, {\n      index: \"category\"\n    }, {\n      default: _withCtx(() => [...(_cache[3] || (_cache[3] = [_createTextVNode(\"分类\", -1 /* CACHED */)]))]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_menu_item, {\n      index: \"cart\"\n    }, {\n      default: _withCtx(() => [...(_cache[4] || (_cache[4] = [_createTextVNode(\"购物车\", -1 /* CACHED */)]))]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"default-active\", \"onSelect\"])]), _createCommentVNode(\" 用户操作区 \"), _createElementVNode(\"div\", _hoisted_5, [$options.isLoggedIn ? (_openBlock(), _createElementBlock(\"div\", _hoisted_6, [_createVNode(_component_el_dropdown, {\n    onCommand: $options.handleUserCommand\n  }, {\n    dropdown: _withCtx(() => [_createVNode(_component_el_dropdown_menu, null, {\n      default: _withCtx(() => [_createVNode(_component_el_dropdown_item, {\n        command: \"profile\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n          default: _withCtx(() => [_createVNode(_component_User)]),\n          _: 1 /* STABLE */\n        }), _cache[5] || (_cache[5] = _createTextVNode(\" 个人中心 \", -1 /* CACHED */))]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_dropdown_item, {\n        command: \"orders\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n          default: _withCtx(() => [_createVNode(_component_List)]),\n          _: 1 /* STABLE */\n        }), _cache[6] || (_cache[6] = _createTextVNode(\" 我的订单 \", -1 /* CACHED */))]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_dropdown_item, {\n        command: \"cart\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n          default: _withCtx(() => [_createVNode(_component_ShoppingCart)]),\n          _: 1 /* STABLE */\n        }), _cache[7] || (_cache[7] = _createTextVNode(\" 购物车 \", -1 /* CACHED */))]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_dropdown_item, {\n        divided: \"\",\n        command: \"logout\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n          default: _withCtx(() => [_createVNode(_component_SwitchButton)]),\n          _: 1 /* STABLE */\n        }), _cache[8] || (_cache[8] = _createTextVNode(\" 退出登录 \", -1 /* CACHED */))]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    })]),\n    default: _withCtx(() => [_createElementVNode(\"span\", _hoisted_7, [_createVNode(_component_el_avatar, {\n      size: 32,\n      src: $options.userAvatar\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode(_component_User)]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"src\"]), _createElementVNode(\"span\", _hoisted_8, _toDisplayString($options.username), 1 /* TEXT */), _createVNode(_component_el_icon, {\n      class: \"dropdown-icon\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_ArrowDown)]),\n      _: 1 /* STABLE */\n    })])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onCommand\"])])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_9, [_createVNode(_component_el_button, {\n    type: \"text\",\n    onClick: $options.goToLogin\n  }, {\n    default: _withCtx(() => [...(_cache[9] || (_cache[9] = [_createTextVNode(\"登录\", -1 /* CACHED */)]))]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClick\"]), _createVNode(_component_el_button, {\n    type: \"primary\",\n    onClick: $options.goToRegister\n  }, {\n    default: _withCtx(() => [...(_cache[10] || (_cache[10] = [_createTextVNode(\"注册\", -1 /* CACHED */)]))]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClick\"])]))])])]), _createCommentVNode(\" 主要内容区域 \"), _createElementVNode(\"main\", _hoisted_10, [_createVNode(_component_router_view)]), _createCommentVNode(\" 底部 \"), _cache[12] || (_cache[12] = _createElementVNode(\"footer\", {\n    class: \"footer\"\n  }, [_createElementVNode(\"div\", {\n    class: \"footer-content\"\n  }, [_createElementVNode(\"p\", null, \"© 2025 在线购物商城. All rights reserved.\")])], -1 /* CACHED */))]);\n}", "map": {"version": 3, "names": ["_imports_0", "class", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "_hoisted_2", "_hoisted_3", "src", "alt", "_hoisted_4", "_createVNode", "_component_el_menu", "mode", "$data", "activeIndex", "onSelect", "$options", "handleMenuSelect", "_component_el_menu_item", "index", "_cache", "_hoisted_5", "isLoggedIn", "_hoisted_6", "_component_el_dropdown", "onCommand", "handleUserCommand", "dropdown", "_withCtx", "_component_el_dropdown_menu", "_component_el_dropdown_item", "command", "_component_el_icon", "_component_User", "_component_List", "_component_ShoppingCart", "divided", "_component_SwitchButton", "_hoisted_7", "_component_el_avatar", "size", "userAvatar", "_hoisted_8", "_toDisplayString", "username", "_component_ArrowDown", "_hoisted_9", "_component_el_button", "type", "onClick", "goToLogin", "goToRegister", "_hoisted_10", "_component_router_view"], "sources": ["D:\\2025_down\\project\\shoppingOnline_2025_91\\shoppingOnline_front-2025-91\\src\\views\\MainLayout.vue"], "sourcesContent": ["<template>\n  <div class=\"main-layout\">\n    <!-- 顶部导航栏 -->\n    <header class=\"header\">\n      <div class=\"header-content\">\n        <div class=\"logo\">\n          <img src=\"@/resource/logo.png\" alt=\"商城Logo\" class=\"logo-img\" />\n          <span class=\"logo-text\">在线购物商城</span>\n        </div>\n        \n        <!-- 导航菜单 -->\n        <nav class=\"nav-menu\">\n          <el-menu\n            mode=\"horizontal\"\n            :default-active=\"activeIndex\"\n            class=\"nav-menu-el\"\n            @select=\"handleMenuSelect\"\n          >\n            <el-menu-item index=\"home\">首页</el-menu-item>\n            <el-menu-item index=\"goods\">商品</el-menu-item>\n            <el-menu-item index=\"test\">功能测试</el-menu-item>\n            <el-menu-item index=\"category\">分类</el-menu-item>\n            <el-menu-item index=\"cart\">购物车</el-menu-item>\n          </el-menu>\n        </nav>\n        \n        <!-- 用户操作区 -->\n        <div class=\"user-actions\">\n          <div v-if=\"isLoggedIn\" class=\"user-info\">\n            <el-dropdown @command=\"handleUserCommand\">\n              <span class=\"user-dropdown\">\n                <el-avatar :size=\"32\" :src=\"userAvatar\">\n                  <el-icon><User /></el-icon>\n                </el-avatar>\n                <span class=\"username\">{{ username }}</span>\n                <el-icon class=\"dropdown-icon\"><ArrowDown /></el-icon>\n              </span>\n              <template #dropdown>\n                <el-dropdown-menu>\n                  <el-dropdown-item command=\"profile\">\n                    <el-icon><User /></el-icon>\n                    个人中心\n                  </el-dropdown-item>\n                  <el-dropdown-item command=\"orders\">\n                    <el-icon><List /></el-icon>\n                    我的订单\n                  </el-dropdown-item>\n                  <el-dropdown-item command=\"cart\">\n                    <el-icon><ShoppingCart /></el-icon>\n                    购物车\n                  </el-dropdown-item>\n                  <el-dropdown-item divided command=\"logout\">\n                    <el-icon><SwitchButton /></el-icon>\n                    退出登录\n                  </el-dropdown-item>\n                </el-dropdown-menu>\n              </template>\n            </el-dropdown>\n          </div>\n          <div v-else class=\"login-actions\">\n            <el-button type=\"text\" @click=\"goToLogin\">登录</el-button>\n            <el-button type=\"primary\" @click=\"goToRegister\">注册</el-button>\n          </div>\n        </div>\n      </div>\n    </header>\n    \n    <!-- 主要内容区域 -->\n    <main class=\"main-content\">\n      <router-view />\n    </main>\n    \n    <!-- 底部 -->\n    <footer class=\"footer\">\n      <div class=\"footer-content\">\n        <p>&copy; 2025 在线购物商城. All rights reserved.</p>\n      </div>\n    </footer>\n  </div>\n</template>\n\n<script>\nimport { useUserStore } from '@/store/index'\nimport { User, ArrowDown, List, ShoppingCart, SwitchButton } from '@element-plus/icons-vue'\n\nexport default {\n  name: 'MainLayout',\n\n  components: {\n    User,\n    ArrowDown,\n    List,\n    ShoppingCart,\n    SwitchButton\n  },\n\n  data() {\n    return {\n      activeIndex: 'goods'\n    }\n  },\n\n  computed: {\n    isLoggedIn() {\n      const userStore = useUserStore();\n      return userStore.isAuthenticated || false\n    },\n    username() {\n      const userStore = useUserStore();\n      return userStore.username || '用户'\n    },\n    userAvatar() {\n      const userStore = useUserStore();\n      return userStore.userInfo?.avatar || ''\n    }\n  },\n  \n  methods: {\n    handleMenuSelect(key) {\n      this.activeIndex = key;\n      switch (key) {\n        case 'home':\n          this.$router.push('/home');\n          break;\n        case 'goods':\n          this.$router.push('/goods');\n          break;\n        case 'test':\n          this.$router.push('/test');\n          break;\n        case 'category':\n          this.$message.info('分类页面开发中...');\n          break;\n        case 'cart':\n          this.$message.info('购物车页面开发中...');\n          break;\n      }\n    },\n\n    // 处理用户下拉菜单命令\n    handleUserCommand(command) {\n      switch (command) {\n        case 'profile':\n          this.$message.info('个人中心功能开发中...');\n          break;\n        case 'orders':\n          this.$message.info('我的订单功能开发中...');\n          break;\n        case 'cart':\n          this.$message.info('购物车功能开发中...');\n          break;\n        case 'logout':\n          this.handleLogout();\n          break;\n      }\n    },\n\n    // 退出登录\n    handleLogout() {\n      this.$confirm('确定要退出登录吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        // 清除登录状态\n        const userStore = useUserStore();\n        userStore.logout();\n\n        this.$message.success('已退出登录');\n\n        // 跳转到登录页\n        this.$router.push('/login');\n      }).catch(() => {\n        // 取消退出\n      });\n    },\n\n    goToLogin() {\n      this.$router.push('/login');\n    },\n\n    goToRegister() {\n      this.$router.push('/register');\n    }\n  },\n  \n  created() {\n    // 初始化用户store\n    this.userStore = useUserStore();\n    // 恢复登录状态\n    this.userStore.restoreLoginState();\n  },\n\n  mounted() {\n    // 根据当前路由设置活动菜单项\n    const path = this.$route.path;\n    if (path.includes('/goods')) {\n      this.activeIndex = 'goods';\n    } else if (path.includes('/home')) {\n      this.activeIndex = 'home';\n    } else if (path.includes('/test')) {\n      this.activeIndex = 'test';\n    }\n  }\n}\n</script>\n\n<style scoped>\n.main-layout {\n  min-height: 100vh;\n  display: flex;\n  flex-direction: column;\n}\n\n.header {\n  background: white;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  position: sticky;\n  top: 0;\n  z-index: 1000;\n}\n\n.header-content {\n  max-width: 1200px;\n  margin: 0 auto;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0 16px;\n  height: 64px;\n}\n\n.logo {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.logo-img {\n  height: 40px;\n  width: auto;\n}\n\n.logo-text {\n  font-size: 20px;\n  font-weight: bold;\n  color: #333;\n}\n\n.nav-menu-el {\n  border-bottom: none;\n}\n\n.user-actions {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.user-info {\n  display: flex;\n  align-items: center;\n}\n\n.user-dropdown {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  cursor: pointer;\n  padding: 8px 12px;\n  border-radius: 6px;\n  transition: background 0.2s;\n}\n\n.user-dropdown:hover {\n  background: #f5f5f5;\n}\n\n.username {\n  font-size: 14px;\n  color: #333;\n  font-weight: 500;\n}\n\n.dropdown-icon {\n  font-size: 12px;\n  color: #666;\n}\n\n.login-actions {\n  display: flex;\n  gap: 8px;\n}\n\n.main-content {\n  flex: 1;\n  background: #f5f5f5;\n  min-height: calc(100vh - 64px - 60px);\n}\n\n.footer {\n  background: #333;\n  color: white;\n  text-align: center;\n  padding: 20px 0;\n}\n\n.footer-content {\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .header-content {\n    padding: 0 12px;\n  }\n  \n  .logo-text {\n    display: none;\n  }\n  \n  .nav-menu {\n    display: none;\n  }\n  \n  .user-actions {\n    gap: 4px;\n  }\n}\n</style>\n"], "mappings": ";OAMeA,UAAyB;;EALjCC,KAAK,EAAC;AAAa;;EAEdA,KAAK,EAAC;AAAQ;;EACfA,KAAK,EAAC;AAAgB;;EAOpBA,KAAK,EAAC;AAAU;;EAgBhBA,KAAK,EAAC;AAAc;;;EACAA,KAAK,EAAC;;;EAEnBA,KAAK,EAAC;AAAe;;EAInBA,KAAK,EAAC;AAAU;;;EAyBhBA,KAAK,EAAC;;;EASlBA,KAAK,EAAC;AAAc;;;;;;;;;;;;;;;;uBAnE5BC,mBAAA,CA6EM,OA7ENC,UA6EM,GA5EJC,mBAAA,WAAc,EACdC,mBAAA,CA8DS,UA9DTC,UA8DS,GA7DPD,mBAAA,CA4DM,OA5DNE,UA4DM,G,4BA3DJF,mBAAA,CAGM;IAHDJ,KAAK,EAAC;EAAM,IACfI,mBAAA,CAA+D;IAA1DG,GAAyB,EAAzBR,UAAyB;IAACS,GAAG,EAAC,QAAQ;IAACR,KAAK,EAAC;MAClDI,mBAAA,CAAqC;IAA/BJ,KAAK,EAAC;EAAW,GAAC,QAAM,E,qBAGhCG,mBAAA,UAAa,EACbC,mBAAA,CAaM,OAbNK,UAaM,GAZJC,YAAA,CAWUC,kBAAA;IAVRC,IAAI,EAAC,YAAY;IAChB,gBAAc,EAAEC,KAAA,CAAAC,WAAW;IAC5Bd,KAAK,EAAC,aAAa;IAClBe,QAAM,EAAEC,QAAA,CAAAC;;sBAET,MAA4C,CAA5CP,YAAA,CAA4CQ,uBAAA;MAA9BC,KAAK,EAAC;IAAM;wBAAC,MAAE,KAAAC,MAAA,QAAAA,MAAA,O,iBAAF,IAAE,mB;;QAC7BV,YAAA,CAA6CQ,uBAAA;MAA/BC,KAAK,EAAC;IAAO;wBAAC,MAAE,KAAAC,MAAA,QAAAA,MAAA,O,iBAAF,IAAE,mB;;QAC9BV,YAAA,CAA8CQ,uBAAA;MAAhCC,KAAK,EAAC;IAAM;wBAAC,MAAI,KAAAC,MAAA,QAAAA,MAAA,O,iBAAJ,MAAI,mB;;QAC/BV,YAAA,CAAgDQ,uBAAA;MAAlCC,KAAK,EAAC;IAAU;wBAAC,MAAE,KAAAC,MAAA,QAAAA,MAAA,O,iBAAF,IAAE,mB;;QACjCV,YAAA,CAA6CQ,uBAAA;MAA/BC,KAAK,EAAC;IAAM;wBAAC,MAAG,KAAAC,MAAA,QAAAA,MAAA,O,iBAAH,KAAG,mB;;;;uDAIlCjB,mBAAA,WAAc,EACdC,mBAAA,CAoCM,OApCNiB,UAoCM,GAnCOL,QAAA,CAAAM,UAAU,I,cAArBrB,mBAAA,CA8BM,OA9BNsB,UA8BM,GA7BJb,YAAA,CA4Bcc,sBAAA;IA5BAC,SAAO,EAAET,QAAA,CAAAU;EAAiB;IAQ3BC,QAAQ,EAAAC,QAAA,CACjB,MAiBmB,CAjBnBlB,YAAA,CAiBmBmB,2BAAA;wBAhBjB,MAGmB,CAHnBnB,YAAA,CAGmBoB,2BAAA;QAHDC,OAAO,EAAC;MAAS;0BACjC,MAA2B,CAA3BrB,YAAA,CAA2BsB,kBAAA;4BAAlB,MAAQ,CAARtB,YAAA,CAAQuB,eAAA,E;;uDAAU,QAE7B,oB;;UACAvB,YAAA,CAGmBoB,2BAAA;QAHDC,OAAO,EAAC;MAAQ;0BAChC,MAA2B,CAA3BrB,YAAA,CAA2BsB,kBAAA;4BAAlB,MAAQ,CAARtB,YAAA,CAAQwB,eAAA,E;;uDAAU,QAE7B,oB;;UACAxB,YAAA,CAGmBoB,2BAAA;QAHDC,OAAO,EAAC;MAAM;0BAC9B,MAAmC,CAAnCrB,YAAA,CAAmCsB,kBAAA;4BAA1B,MAAgB,CAAhBtB,YAAA,CAAgByB,uBAAA,E;;uDAAU,OAErC,oB;;UACAzB,YAAA,CAGmBoB,2BAAA;QAHDM,OAAO,EAAP,EAAO;QAACL,OAAO,EAAC;;0BAChC,MAAmC,CAAnCrB,YAAA,CAAmCsB,kBAAA;4BAA1B,MAAgB,CAAhBtB,YAAA,CAAgB2B,uBAAA,E;;uDAAU,QAErC,oB;;;;;sBAxBJ,MAMO,CANPjC,mBAAA,CAMO,QANPkC,UAMO,GALL5B,YAAA,CAEY6B,oBAAA;MAFAC,IAAI,EAAE,EAAE;MAAGjC,GAAG,EAAES,QAAA,CAAAyB;;wBAC1B,MAA2B,CAA3B/B,YAAA,CAA2BsB,kBAAA;0BAAlB,MAAQ,CAARtB,YAAA,CAAQuB,eAAA,E;;;;gCAEnB7B,mBAAA,CAA4C,QAA5CsC,UAA4C,EAAAC,gBAAA,CAAlB3B,QAAA,CAAA4B,QAAQ,kBAClClC,YAAA,CAAsDsB,kBAAA;MAA7ChC,KAAK,EAAC;IAAe;wBAAC,MAAa,CAAbU,YAAA,CAAamC,oBAAA,E;;;;uDAwBlD5C,mBAAA,CAGM,OAHN6C,UAGM,GAFJpC,YAAA,CAAwDqC,oBAAA;IAA7CC,IAAI,EAAC,MAAM;IAAEC,OAAK,EAAEjC,QAAA,CAAAkC;;sBAAW,MAAE,KAAA9B,MAAA,QAAAA,MAAA,O,iBAAF,IAAE,mB;;kCAC5CV,YAAA,CAA8DqC,oBAAA;IAAnDC,IAAI,EAAC,SAAS;IAAEC,OAAK,EAAEjC,QAAA,CAAAmC;;sBAAc,MAAE,KAAA/B,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,mB;;2CAM1DjB,mBAAA,YAAe,EACfC,mBAAA,CAEO,QAFPgD,WAEO,GADL1C,YAAA,CAAe2C,sBAAA,E,GAGjBlD,mBAAA,QAAW,E,4BACXC,mBAAA,CAIS;IAJDJ,KAAK,EAAC;EAAQ,IACpBI,mBAAA,CAEM;IAFDJ,KAAK,EAAC;EAAgB,IACzBI,mBAAA,CAA+C,WAA5C,qCAAwC,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}