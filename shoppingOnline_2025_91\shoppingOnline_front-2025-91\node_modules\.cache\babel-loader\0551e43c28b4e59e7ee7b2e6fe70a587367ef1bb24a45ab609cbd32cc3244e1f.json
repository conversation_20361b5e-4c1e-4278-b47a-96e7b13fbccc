{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport request from '@/utils/request.js';\nimport CarouselComponent from '@/components/Carousel.vue';\nimport SearchBox from '@/components/SearchBox.vue';\nimport ProductCategory from '@/components/ProductCategory.vue';\nimport ProductDetail from '@/components/ProductDetail.vue';\nexport default {\n  name: 'GoodsView',\n  components: {\n    CarouselComponent,\n    SearchBox,\n    ProductCategory,\n    ProductDetail\n  },\n  data() {\n    return {\n      name: '',\n      awesome: false,\n      loading: true,\n      goodsList: [],\n      searchKeyword: '',\n      // 当前搜索关键词\n      currentPage: 1,\n      pageSize: 20,\n      totalCount: 0,\n      isSearchMode: false,\n      // 是否处于搜索模式\n      selectedMainCategory: null,\n      // 选中的主分类\n      selectedSubCategory: null,\n      // 选中的子分类\n      hasCategoryFilter: false,\n      // 是否有分类筛选\n      showDetailDialog: false,\n      // 是否显示商品详情弹窗\n      selectedProduct: {} // 选中的商品\n    };\n  },\n  computed: {\n    // 计算商品总数\n    totalGoods() {\n      return this.goodsList.length;\n    }\n  },\n  watch: {\n    // 监听路由参数变化\n    '$route.query.search': {\n      handler(newSearch) {\n        if (newSearch) {\n          this.handleSearch(newSearch);\n        } else {\n          this.handleClearSearch();\n        }\n      },\n      immediate: false\n    }\n  },\n  created() {\n    // 检查是否有搜索参数\n    const searchQuery = this.$route.query.search;\n    const categoryId = this.$route.query.categoryId;\n    if (searchQuery) {\n      this.handleSearch(searchQuery);\n    } else if (categoryId) {\n      // 如果有分类参数，设置分类筛选\n      this.selectedMainCategory = parseInt(categoryId);\n      this.hasCategoryFilter = true;\n      this.findFrontGoods();\n    } else {\n      this.findFrontGoods();\n    }\n  },\n  mounted() {\n    // 检查路由参数中是否有分类信息\n    this.checkRouteParams();\n    // 调试：输出图片路径信息\n    console.log('当前页面URL:', window.location.href);\n    console.log('图片路径测试:', './images/women-top.jpg');\n    console.log('绝对路径测试:', '/images/women-top.jpg');\n\n    // 查询推荐商品\n    this.findFrontGoods();\n  },\n  methods: {\n    // 处理分类变化\n    handleCategoryChange(category) {\n      console.log('分类变化:', category);\n      this.selectedMainCategory = category.categoryId;\n      this.hasCategoryFilter = category.categoryId !== null;\n      this.currentPage = 1;\n      if (this.isSearchMode) {\n        this.searchGoods();\n      } else {\n        this.findFrontGoods();\n      }\n    },\n    // 图片加载错误处理\n    handleImageError(event) {\n      console.error('图片加载失败:', event.target.src);\n      console.error('图片元素:', event.target);\n      // 设置默认图片或隐藏图片\n      event.target.style.display = 'none';\n      event.target.style.backgroundColor = '#f0f0f0';\n      event.target.style.border = '1px solid #ccc';\n    },\n    // 图片加载成功处理\n    handleImageLoad(event) {\n      console.log('图片加载成功:', event.target.src);\n      console.log('图片尺寸:', event.target.naturalWidth, 'x', event.target.naturalHeight);\n    },\n    // 显示商品详情\n    showProductDetail(good) {\n      this.selectedProduct = good;\n      this.showDetailDialog = true;\n      console.log('查看商品详情:', good);\n    },\n    // 添加到购物车\n    addToCart(good) {\n      console.log('加入购物车:', good.name);\n      this.$emit('add-to-cart', good);\n      this.$message.success(`已加入购物车：${good.name}`);\n    },\n    // 立即购买\n    buyNow(good) {\n      console.log('立即购买:', good.name);\n      this.$message.info(`立即购买：${good.name}`);\n      // 这里可以跳转到订单页面\n    },\n    // 查询推荐商品\n    async findFrontGoods() {\n      try {\n        this.loading = true;\n        let params = {};\n\n        // 如果有分类筛选，添加分类参数\n        if (this.hasCategoryFilter && this.selectedMainCategory) {\n          params.categoryId = this.selectedMainCategory;\n        }\n        const response = await request.get('/good', params);\n        if (Array.isArray(response) && response.length > 0) {\n          // 使用后端数据，并处理图片URL和确保描述存在\n          this.goodsList = response.map((item, index) => {\n            // 根据商品名称匹配对应的图片\n            let imgUrl;\n            switch (item.name) {\n              case '女上衣':\n                imgUrl = require('@/assets/女上衣.png');\n                break;\n              case '休闲鞋':\n                imgUrl = require('@/assets/休闲鞋.png');\n                break;\n              case '威士忌 大瓶':\n                imgUrl = require('@/assets/威士忌 大瓶.png');\n                break;\n              case '墨镜':\n                imgUrl = require('@/assets/墨镜.png');\n                break;\n              case '桌椅套装':\n                imgUrl = require('@/assets/桌椅套装.png');\n                break;\n              case '儿童简笔画册':\n                imgUrl = require('@/assets/儿童简笔画册.png');\n                break;\n              case '英文版图书':\n                imgUrl = require('@/assets/英文版图书.png');\n                break;\n              case '衬衫':\n                imgUrl = require('@/assets/衬衫.png');\n                break;\n              default:\n                {\n                  // 默认图片，根据索引循环使用\n                  const defaultImages = [require('@/assets/女上衣.png'), require('@/assets/休闲鞋.png'), require('@/assets/威士忌 大瓶.png'), require('@/assets/墨镜.png'), require('@/assets/桌椅套装.png')];\n                  imgUrl = defaultImages[index % defaultImages.length];\n                  break;\n                }\n            }\n            return {\n              ...item,\n              imgs: imgUrl,\n              description: item.description || this.getDefaultDescription(item.name),\n              // 使用默认描述\n              price: item.price || (Math.random() * 200 + 50).toFixed(2) // 确保有价格\n            };\n          });\n          console.log('使用后端商品数据，添加图片:', this.goodsList);\n        } else {\n          // 如果后端没有数据，使用本地图片的模拟数据\n          this.goodsList = this.getMockProductsWithLocalImages();\n          console.log('使用本地图片的模拟商品数据:', this.goodsList);\n        }\n        this.totalCount = this.goodsList.length;\n        this.isSearchMode = false;\n      } catch (error) {\n        console.error('请求失败:', error.message || error);\n        // 使用本地图片的模拟数据作为后备\n        this.goodsList = this.getMockProductsWithLocalImages();\n        this.totalCount = this.goodsList.length;\n        console.log('API请求失败，使用本地图片的模拟数据:', this.goodsList);\n      } finally {\n        this.loading = false;\n      }\n    },\n    // 获取使用本地图片的模拟商品数据\n    getMockProductsWithLocalImages() {\n      return [{\n        id: 1,\n        name: '女上衣',\n        description: '时尚女性上衣，舒适面料，多种颜色可选，适合日常穿着和商务场合，版型修身显瘦',\n        imgs: require('@/assets/女上衣.png'),\n        price: 102.00,\n        discount: 0.85,\n        sales: 120,\n        recommend: true\n      }, {\n        id: 2,\n        name: '休闲鞋',\n        description: '舒适透气的休闲运动鞋，适合日常穿着，防滑耐磨，多种尺码可选，时尚百搭',\n        imgs: require('@/assets/休闲鞋.png'),\n        price: 162.00,\n        discount: 0.90,\n        sales: 85,\n        recommend: true\n      }, {\n        id: 3,\n        name: '威士忌 大瓶',\n        description: '优质威士忌，口感醇厚，适合收藏和品鉴，酒精度40%，容量750ml，包装精美',\n        imgs: require('@/assets/威士忌 大瓶.png'),\n        price: 427.50,\n        discount: 0.95,\n        sales: 45,\n        recommend: true\n      }, {\n        id: 4,\n        name: '墨镜',\n        description: '时尚墨镜，防紫外线，多种款式可选',\n        imgs: require('@/assets/墨镜.png'),\n        price: 199.00,\n        discount: 0.80,\n        sales: 65,\n        recommend: true\n      }, {\n        id: 5,\n        name: '桌椅套装',\n        description: '舒适桌椅套装，适合家庭使用',\n        imgs: require('@/assets/桌椅套装.png'),\n        price: 1299.00,\n        discount: 0.85,\n        sales: 30,\n        recommend: true\n      }, {\n        id: 6,\n        name: '儿童简笔画册',\n        description: '儿童简笔画册，培养孩子的绘画兴趣',\n        imgs: require('@/assets/儿童简笔画册.png'),\n        price: 39.90,\n        discount: 0.90,\n        sales: 120,\n        recommend: true\n      }, {\n        id: 7,\n        name: '英文版图书',\n        description: '英文版图书，提高英语阅读能力',\n        imgs: require('@/assets/英文版图书.png'),\n        price: 68.00,\n        discount: 0.95,\n        sales: 45,\n        recommend: true\n      }, {\n        id: 8,\n        name: '衬衫',\n        description: '时尚衬衫，舒适面料，多种颜色可选',\n        imgs: require('@/assets/衬衫.png'),\n        price: 129.00,\n        discount: 0.85,\n        sales: 80,\n        recommend: true\n      }];\n    },\n    // 处理搜索\n    async handleSearch(keyword) {\n      this.searchKeyword = keyword;\n      this.isSearchMode = true;\n      this.currentPage = 1;\n      await this.searchGoods();\n    },\n    // 搜索商品\n    async searchGoods() {\n      try {\n        this.loading = true;\n        let params = {\n          searchText: this.searchKeyword,\n          pageNum: this.currentPage,\n          pageSize: this.pageSize\n        };\n\n        // 如果有分类筛选，添加分类参数\n        if (this.hasCategoryFilter) {\n          params = {\n            ...params,\n            mainCategory: this.selectedMainCategory,\n            subCategory: this.selectedSubCategory\n          };\n        }\n        const response = await request.get('/good/page', params);\n        if (response && response.records) {\n          this.goodsList = response.records;\n          this.totalCount = response.total || 0;\n        } else {\n          this.goodsList = [];\n          this.totalCount = 0;\n        }\n      } catch (error) {\n        console.error('搜索失败:', error);\n        this.goodsList = [];\n        this.totalCount = 0;\n        this.$message.error('搜索失败，请重试');\n      } finally {\n        this.loading = false;\n      }\n    },\n    // 处理搜索结果\n    handleSearchResults(results) {\n      if (results && results.records) {\n        this.goodsList = results.records;\n        this.totalCount = results.total || 0;\n      }\n    },\n    // 清除搜索\n    handleClearSearch() {\n      this.searchKeyword = '';\n      this.isSearchMode = false;\n      this.currentPage = 1;\n\n      // 只有在没有分类筛选时才清除所有筛选\n      if (!this.hasCategoryFilter) {\n        this.findFrontGoods();\n      } else {\n        // 有分类筛选时，重新搜索\n        this.searchGoods();\n      }\n    },\n    // 清除分类筛选\n    clearCategoryFilter() {\n      // 触发ProductCategory组件的clearCategoryFilter方法\n      const productCategory = this.$refs.productCategory;\n      if (productCategory && typeof productCategory.clearCategoryFilter === 'function') {\n        productCategory.clearCategoryFilter();\n      }\n    },\n    // 检查路由参数\n    checkRouteParams() {\n      const routeQuery = this.$route.query;\n      if (routeQuery.mainCategory || routeQuery.subCategory) {\n        this.selectedMainCategory = routeQuery.mainCategory || null;\n        this.selectedSubCategory = routeQuery.subCategory || null;\n        this.hasCategoryFilter = true;\n\n        // 在组件挂载后设置分类\n        this.$nextTick(() => {\n          const productCategory = this.$refs.productCategory;\n          if (productCategory && typeof productCategory.setSelectedCategory === 'function') {\n            productCategory.setSelectedCategory({\n              mainCategory: this.selectedMainCategory,\n              subCategory: this.selectedSubCategory\n            });\n          }\n        });\n      }\n    },\n    // 处理轮播图点击\n    handleCarouselClick(item) {\n      console.log('点击轮播图商品:', item);\n      // 可以跳转到商品详情页\n      // this.$router.push(`/goods/${item.goodId}`);\n    },\n    // 处理页面大小变化\n    handleSizeChange(newSize) {\n      this.pageSize = newSize;\n      this.currentPage = 1;\n      if (this.isSearchMode) {\n        this.searchGoods();\n      } else {\n        this.findFrontGoods();\n      }\n    },\n    // 处理当前页变化\n    handleCurrentChange(newPage) {\n      this.currentPage = newPage;\n      if (this.isSearchMode) {\n        this.searchGoods();\n      } else {\n        this.findFrontGoods();\n      }\n    }\n  },\n  beforeUnmount() {\n    console.log('商品页面即将卸载');\n  }\n};", "map": {"version": 3, "names": ["request", "CarouselComponent", "SearchBox", "ProductCategory", "ProductDetail", "name", "components", "data", "awesome", "loading", "goodsList", "searchKeyword", "currentPage", "pageSize", "totalCount", "isSearchMode", "selected<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectedSubCategory", "hasCate<PERSON><PERSON><PERSON><PERSON><PERSON>", "showDetailDialog", "selectedProduct", "computed", "totalGoods", "length", "watch", "handler", "newSearch", "handleSearch", "handleClearSearch", "immediate", "created", "searchQuery", "$route", "query", "search", "categoryId", "parseInt", "findFrontGoods", "mounted", "checkRouteParams", "console", "log", "window", "location", "href", "methods", "handleCategoryChange", "category", "searchGoods", "handleImageError", "event", "error", "target", "src", "style", "display", "backgroundColor", "border", "handleImageLoad", "naturalWidth", "naturalHeight", "showProductDetail", "good", "addToCart", "$emit", "$message", "success", "buyNow", "info", "params", "response", "get", "Array", "isArray", "map", "item", "index", "imgUrl", "require", "defaultImages", "imgs", "description", "getDefaultDescription", "price", "Math", "random", "toFixed", "getMockProductsWithLocalImages", "message", "id", "discount", "sales", "recommend", "keyword", "searchText", "pageNum", "mainCategory", "subCategory", "records", "total", "handleSearchResults", "results", "clearCategory<PERSON><PERSON>er", "productCategory", "$refs", "routeQuery", "$nextTick", "setSelectedCategory", "handleCarouselClick", "handleSizeChange", "newSize", "handleCurrentChange", "newPage", "beforeUnmount"], "sources": ["D:\\2025_down\\project\\shoppingOnline_2025_91\\shoppingOnline_front-2025-91\\src\\views\\goods.vue"], "sourcesContent": ["<template>\r\n  <div class=\"goods-container\">\r\n    <!-- 页面标题 -->\r\n    <h1 class=\"page-title\">在线购物商城</h1>\r\n\r\n    <!-- 轮播图组件 -->\r\n    <CarouselComponent @carousel-click=\"handleCarouselClick\" />\r\n\r\n    <!-- 搜索框组件 -->\r\n    <SearchBox\r\n      @search=\"handleSearch\"\r\n      @search-results=\"handleSearchResults\"\r\n      @clear-search=\"handleClearSearch\"\r\n    />\r\n\r\n    <!-- 商品分类 -->\r\n    <ProductCategory ref=\"productCategory\" @category-change=\"handleCategoryChange\" />\r\n\r\n    <!-- 搜索结果和分类筛选提示 -->\r\n    <div v-if=\"searchKeyword || hasCategoryFilter\" class=\"search-info\">\r\n      <!-- 搜索结果标签 -->\r\n      <el-tag v-if=\"searchKeyword\" type=\"info\" closable @close=\"handleClearSearch\">\r\n        搜索：{{ searchKeyword }}\r\n      </el-tag>\r\n      \r\n      <!-- 分类筛选标签 -->\r\n      <el-tag v-if=\"selectedMainCategory\" type=\"success\" closable @close=\"clearCategoryFilter\">\r\n        分类筛选\r\n      </el-tag>\r\n      \r\n      <span class=\"search-count\">共找到 {{ totalCount }} 件商品</span>\r\n    </div>\r\n\r\n    <!-- 加载状态 -->\r\n    <div v-if=\"loading\" class=\"loading\">\r\n      <el-skeleton :rows=\"3\" animated />\r\n    </div>\r\n\r\n    <!-- 商品列表 -->\r\n    <div v-else class=\"goods-list\">\r\n      <div\r\n        class=\"goods-card\"\r\n        v-for=\"good in goodsList\"\r\n        :key=\"good.id\"\r\n        @click=\"showProductDetail(good)\"\r\n      >\r\n        <img\r\n          :src=\"good.imgs\"\r\n          :alt=\"good.name\"\r\n          class=\"goods-image\"\r\n          @error=\"handleImageError\"\r\n          @load=\"handleImageLoad\"\r\n        />\r\n        <div class=\"goods-info\">\r\n          <h3 class=\"goods-name\">{{ good.name }}</h3>\r\n          <p class=\"goods-desc\">{{ good.description || '暂无商品描述' }}</p>\r\n          <p class=\"goods-price\"><strong>¥{{ good.price }}</strong></p>\r\n          <button class=\"btn-add-cart\" @click.stop=\"addToCart(good)\">\r\n            加入购物车\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 商品详情弹窗 -->\r\n    <ProductDetail\r\n      v-model=\"showDetailDialog\"\r\n      :product=\"selectedProduct\"\r\n      @add-to-cart=\"addToCart\"\r\n      @buy-now=\"buyNow\"\r\n    />\r\n\r\n    <!-- 无数据-->\r\n    <div v-if=\"!loading && goodsList.length === 0\" class=\"no-data\">\r\n      <el-empty description=\"暂无商品\" />\r\n    </div>\r\n\r\n    <!-- 分页组件 -->\r\n    <div v-if=\"totalCount > pageSize\" class=\"pagination-wrapper\">\r\n      <el-pagination\r\n        v-model:current-page=\"currentPage\"\r\n        v-model:page-size=\"pageSize\"\r\n        :page-sizes=\"[10, 20, 50, 100]\"\r\n        :total=\"totalCount\"\r\n        layout=\"total, sizes, prev, pager, next, jumper\"\r\n        @size-change=\"handleSizeChange\"\r\n        @current-change=\"handleCurrentChange\"\r\n      />\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport request from '@/utils/request.js'\r\nimport CarouselComponent from '@/components/Carousel.vue'\r\nimport SearchBox from '@/components/SearchBox.vue'\r\nimport ProductCategory from '@/components/ProductCategory.vue'\r\nimport ProductDetail from '@/components/ProductDetail.vue'\r\n\r\n\r\nexport default {\r\n  name: 'GoodsView',\r\n\r\n  components: {\r\n      CarouselComponent,\r\n      SearchBox,\r\n      ProductCategory,\r\n      ProductDetail\r\n    },\r\n\r\n  data() {\r\n    return {\r\n      name: '',\r\n      awesome: false,\r\n      loading: true,\r\n      goodsList: [],\r\n      searchKeyword: '', // 当前搜索关键词\r\n      currentPage: 1,\r\n      pageSize: 20,\r\n      totalCount: 0,\r\n      isSearchMode: false, // 是否处于搜索模式\r\n      selectedMainCategory: null, // 选中的主分类\r\n      selectedSubCategory: null, // 选中的子分类\r\n      hasCategoryFilter: false, // 是否有分类筛选\r\n      showDetailDialog: false, // 是否显示商品详情弹窗\r\n      selectedProduct: {} // 选中的商品\r\n    };\r\n  },\r\n\r\n  computed: {\r\n    // 计算商品总数\r\n    totalGoods() {\r\n      return this.goodsList.length;\r\n    }\r\n  },\r\n\r\n  watch: {\r\n    // 监听路由参数变化\r\n    '$route.query.search': {\r\n      handler(newSearch) {\r\n        if (newSearch) {\r\n          this.handleSearch(newSearch);\r\n        } else {\r\n          this.handleClearSearch();\r\n        }\r\n      },\r\n      immediate: false\r\n    }\r\n  },\r\n\r\n  created() {\r\n    // 检查是否有搜索参数\r\n    const searchQuery = this.$route.query.search;\r\n    const categoryId = this.$route.query.categoryId;\r\n\r\n    if (searchQuery) {\r\n      this.handleSearch(searchQuery);\r\n    } else if (categoryId) {\r\n      // 如果有分类参数，设置分类筛选\r\n      this.selectedMainCategory = parseInt(categoryId);\r\n      this.hasCategoryFilter = true;\r\n      this.findFrontGoods();\r\n    } else {\r\n      this.findFrontGoods();\r\n    }\r\n  },\r\n\r\n  mounted() {\r\n    // 检查路由参数中是否有分类信息\r\n    this.checkRouteParams();\r\n    // 调试：输出图片路径信息\r\n    console.log('当前页面URL:', window.location.href);\r\n    console.log('图片路径测试:', './images/women-top.jpg');\r\n    console.log('绝对路径测试:', '/images/women-top.jpg');\r\n\r\n    // 查询推荐商品\r\n    this.findFrontGoods();\r\n  },\r\n\r\n  methods: {\r\n    // 处理分类变化\r\n    handleCategoryChange(category) {\r\n      console.log('分类变化:', category);\r\n      this.selectedMainCategory = category.categoryId;\r\n      this.hasCategoryFilter = category.categoryId !== null;\r\n      this.currentPage = 1;\r\n\r\n      if (this.isSearchMode) {\r\n        this.searchGoods();\r\n      } else {\r\n        this.findFrontGoods();\r\n      }\r\n    },\r\n\r\n    // 图片加载错误处理\r\n    handleImageError(event) {\r\n      console.error('图片加载失败:', event.target.src);\r\n      console.error('图片元素:', event.target);\r\n      // 设置默认图片或隐藏图片\r\n      event.target.style.display = 'none';\r\n      event.target.style.backgroundColor = '#f0f0f0';\r\n      event.target.style.border = '1px solid #ccc';\r\n    },\r\n\r\n    // 图片加载成功处理\r\n    handleImageLoad(event) {\r\n      console.log('图片加载成功:', event.target.src);\r\n      console.log('图片尺寸:', event.target.naturalWidth, 'x', event.target.naturalHeight);\r\n    },\r\n\r\n    // 显示商品详情\r\n    showProductDetail(good) {\r\n      this.selectedProduct = good;\r\n      this.showDetailDialog = true;\r\n      console.log('查看商品详情:', good);\r\n    },\r\n\r\n    // 添加到购物车\r\n    addToCart(good) {\r\n      console.log('加入购物车:', good.name);\r\n      this.$emit('add-to-cart', good);\r\n      this.$message.success(`已加入购物车：${good.name}`);\r\n    },\r\n\r\n    // 立即购买\r\n    buyNow(good) {\r\n      console.log('立即购买:', good.name);\r\n      this.$message.info(`立即购买：${good.name}`);\r\n      // 这里可以跳转到订单页面\r\n    },\r\n\r\n    // 查询推荐商品\r\n    async findFrontGoods() {\r\n      try {\r\n        this.loading = true;\r\n        let params = {};\r\n        \r\n        // 如果有分类筛选，添加分类参数\r\n        if (this.hasCategoryFilter && this.selectedMainCategory) {\r\n          params.categoryId = this.selectedMainCategory;\r\n        }\r\n        \r\n        const response = await request.get('/good', params);\r\n\r\n        if (Array.isArray(response) && response.length > 0) {\r\n          // 使用后端数据，并处理图片URL和确保描述存在\r\n          this.goodsList = response.map((item, index) => {\r\n            // 根据商品名称匹配对应的图片\r\n            let imgUrl;\r\n            switch(item.name) {\r\n              case '女上衣':\r\n                imgUrl = require('@/assets/女上衣.png');\r\n                break;\r\n              case '休闲鞋':\r\n                imgUrl = require('@/assets/休闲鞋.png');\r\n                break;\r\n              case '威士忌 大瓶':\r\n                imgUrl = require('@/assets/威士忌 大瓶.png');\r\n                break;\r\n              case '墨镜':\r\n                imgUrl = require('@/assets/墨镜.png');\r\n                break;\r\n              case '桌椅套装':\r\n                imgUrl = require('@/assets/桌椅套装.png');\r\n                break;\r\n              case '儿童简笔画册':\r\n                imgUrl = require('@/assets/儿童简笔画册.png');\r\n                break;\r\n              case '英文版图书':\r\n                imgUrl = require('@/assets/英文版图书.png');\r\n                break;\r\n              case '衬衫':\r\n                imgUrl = require('@/assets/衬衫.png');\r\n                break;\r\n              default: {\r\n                // 默认图片，根据索引循环使用\r\n                const defaultImages = [\r\n                  require('@/assets/女上衣.png'),\r\n                  require('@/assets/休闲鞋.png'),\r\n                  require('@/assets/威士忌 大瓶.png'),\r\n                  require('@/assets/墨镜.png'),\r\n                  require('@/assets/桌椅套装.png')\r\n                ];\r\n                imgUrl = defaultImages[index % defaultImages.length];\r\n                break;\r\n              }\r\n            }\r\n            return {\r\n              ...item,\r\n              imgs: imgUrl,\r\n              description: item.description || this.getDefaultDescription(item.name), // 使用默认描述\r\n              price: item.price || (Math.random() * 200 + 50).toFixed(2) // 确保有价格\r\n            };\r\n          });\r\n          console.log('使用后端商品数据，添加图片:', this.goodsList);\r\n        } else {\r\n          // 如果后端没有数据，使用本地图片的模拟数据\r\n          this.goodsList = this.getMockProductsWithLocalImages();\r\n          console.log('使用本地图片的模拟商品数据:', this.goodsList);\r\n        }\r\n\r\n        this.totalCount = this.goodsList.length;\r\n        this.isSearchMode = false;\r\n      } catch (error) {\r\n        console.error('请求失败:', error.message || error);\r\n        // 使用本地图片的模拟数据作为后备\r\n        this.goodsList = this.getMockProductsWithLocalImages();\r\n        this.totalCount = this.goodsList.length;\r\n        console.log('API请求失败，使用本地图片的模拟数据:', this.goodsList);\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n\r\n    // 获取使用本地图片的模拟商品数据\r\n    getMockProductsWithLocalImages() {\r\n      return [\r\n        {\r\n          id: 1,\r\n          name: '女上衣',\r\n          description: '时尚女性上衣，舒适面料，多种颜色可选，适合日常穿着和商务场合，版型修身显瘦',\r\n          imgs: require('@/assets/女上衣.png'),\r\n          price: 102.00,\r\n          discount: 0.85,\r\n          sales: 120,\r\n          recommend: true\r\n        },\r\n        {\r\n          id: 2,\r\n          name: '休闲鞋',\r\n          description: '舒适透气的休闲运动鞋，适合日常穿着，防滑耐磨，多种尺码可选，时尚百搭',\r\n          imgs: require('@/assets/休闲鞋.png'),\r\n          price: 162.00,\r\n          discount: 0.90,\r\n          sales: 85,\r\n          recommend: true\r\n        },\r\n        {\r\n          id: 3,\r\n          name: '威士忌 大瓶',\r\n          description: '优质威士忌，口感醇厚，适合收藏和品鉴，酒精度40%，容量750ml，包装精美',\r\n          imgs: require('@/assets/威士忌 大瓶.png'),\r\n          price: 427.50,\r\n          discount: 0.95,\r\n          sales: 45,\r\n          recommend: true\r\n        },\r\n        {\r\n          id: 4,\r\n          name: '墨镜',\r\n          description: '时尚墨镜，防紫外线，多种款式可选',\r\n          imgs: require('@/assets/墨镜.png'),\r\n          price: 199.00,\r\n          discount: 0.80,\r\n          sales: 65,\r\n          recommend: true\r\n        },\r\n        {\r\n          id: 5,\r\n          name: '桌椅套装',\r\n          description: '舒适桌椅套装，适合家庭使用',\r\n          imgs: require('@/assets/桌椅套装.png'),\r\n          price: 1299.00,\r\n          discount: 0.85,\r\n          sales: 30,\r\n          recommend: true\r\n        },\r\n        {\r\n          id: 6,\r\n          name: '儿童简笔画册',\r\n          description: '儿童简笔画册，培养孩子的绘画兴趣',\r\n          imgs: require('@/assets/儿童简笔画册.png'),\r\n          price: 39.90,\r\n          discount: 0.90,\r\n          sales: 120,\r\n          recommend: true\r\n        },\r\n        {\r\n          id: 7,\r\n          name: '英文版图书',\r\n          description: '英文版图书，提高英语阅读能力',\r\n          imgs: require('@/assets/英文版图书.png'),\r\n          price: 68.00,\r\n          discount: 0.95,\r\n          sales: 45,\r\n          recommend: true\r\n        },\r\n        {\r\n          id: 8,\r\n          name: '衬衫',\r\n          description: '时尚衬衫，舒适面料，多种颜色可选',\r\n          imgs: require('@/assets/衬衫.png'),\r\n          price: 129.00,\r\n          discount: 0.85,\r\n          sales: 80,\r\n          recommend: true\r\n        }\r\n      ];\r\n    },\r\n\r\n    // 处理搜索\r\n    async handleSearch(keyword) {\r\n      this.searchKeyword = keyword;\r\n      this.isSearchMode = true;\r\n      this.currentPage = 1;\r\n      await this.searchGoods();\r\n    },\r\n\r\n    // 搜索商品\r\n    async searchGoods() {\r\n      try {\r\n        this.loading = true;\r\n        let params = {\r\n          searchText: this.searchKeyword,\r\n          pageNum: this.currentPage,\r\n          pageSize: this.pageSize\r\n        };\r\n        \r\n        // 如果有分类筛选，添加分类参数\r\n        if (this.hasCategoryFilter) {\r\n          params = {\r\n            ...params,\r\n            mainCategory: this.selectedMainCategory,\r\n            subCategory: this.selectedSubCategory\r\n          };\r\n        }\r\n        \r\n        const response = await request.get('/good/page', params);\r\n\r\n        if (response && response.records) {\r\n          this.goodsList = response.records;\r\n          this.totalCount = response.total || 0;\r\n        } else {\r\n          this.goodsList = [];\r\n          this.totalCount = 0;\r\n        }\r\n      } catch (error) {\r\n        console.error('搜索失败:', error);\r\n        this.goodsList = [];\r\n        this.totalCount = 0;\r\n        this.$message.error('搜索失败，请重试');\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n\r\n    // 处理搜索结果\r\n    handleSearchResults(results) {\r\n      if (results && results.records) {\r\n        this.goodsList = results.records;\r\n        this.totalCount = results.total || 0;\r\n      }\r\n    },\r\n\r\n    // 清除搜索\r\n    handleClearSearch() {\r\n      this.searchKeyword = '';\r\n      this.isSearchMode = false;\r\n      this.currentPage = 1;\r\n      \r\n      // 只有在没有分类筛选时才清除所有筛选\r\n      if (!this.hasCategoryFilter) {\r\n        this.findFrontGoods();\r\n      } else {\r\n        // 有分类筛选时，重新搜索\r\n        this.searchGoods();\r\n      }\r\n    },\r\n    \r\n    // 清除分类筛选\r\n    clearCategoryFilter() {\r\n      // 触发ProductCategory组件的clearCategoryFilter方法\r\n      const productCategory = this.$refs.productCategory;\r\n      if (productCategory && typeof productCategory.clearCategoryFilter === 'function') {\r\n        productCategory.clearCategoryFilter();\r\n      }\r\n    },\r\n    \r\n    // 检查路由参数\r\n    checkRouteParams() {\r\n      const routeQuery = this.$route.query;\r\n      if (routeQuery.mainCategory || routeQuery.subCategory) {\r\n        this.selectedMainCategory = routeQuery.mainCategory || null;\r\n        this.selectedSubCategory = routeQuery.subCategory || null;\r\n        this.hasCategoryFilter = true;\r\n        \r\n        // 在组件挂载后设置分类\r\n        this.$nextTick(() => {\r\n          const productCategory = this.$refs.productCategory;\r\n          if (productCategory && typeof productCategory.setSelectedCategory === 'function') {\r\n            productCategory.setSelectedCategory({\r\n              mainCategory: this.selectedMainCategory,\r\n              subCategory: this.selectedSubCategory\r\n            });\r\n          }\r\n        });\r\n      }\r\n    },\r\n\r\n    // 处理轮播图点击\r\n    handleCarouselClick(item) {\r\n      console.log('点击轮播图商品:', item);\r\n      // 可以跳转到商品详情页\r\n      // this.$router.push(`/goods/${item.goodId}`);\r\n    },\r\n\r\n    // 处理页面大小变化\r\n    handleSizeChange(newSize) {\r\n      this.pageSize = newSize;\r\n      this.currentPage = 1;\r\n      if (this.isSearchMode) {\r\n        this.searchGoods();\r\n      } else {\r\n        this.findFrontGoods();\r\n      }\r\n    },\r\n\r\n    // 处理当前页变化\r\n    handleCurrentChange(newPage) {\r\n      this.currentPage = newPage;\r\n      if (this.isSearchMode) {\r\n        this.searchGoods();\r\n      } else {\r\n        this.findFrontGoods();\r\n      }\r\n    }\r\n  },\r\n\r\n\r\n\r\n  beforeUnmount() {\r\n    console.log('商品页面即将卸载');\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.goods-container {\r\n  max-width: 1200px;\r\n  margin: 20px auto;\r\n  padding: 0 16px;\r\n}\r\n\r\n.page-title {\r\n  text-align: center;\r\n  color: #333;\r\n  margin-bottom: 30px;\r\n  font-size: 32px;\r\n  font-weight: bold;\r\n  background: linear-gradient(45deg, #409eff, #67c23a);\r\n  -webkit-background-clip: text;\r\n  -webkit-text-fill-color: transparent;\r\n  background-clip: text;\r\n}\r\n\r\n.search-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 15px;\r\n  margin-bottom: 20px;\r\n  padding: 15px;\r\n  background: #f8f9fa;\r\n  border-radius: 8px;\r\n  border-left: 4px solid #409eff;\r\n}\r\n\r\n.search-count {\r\n  color: #666;\r\n  font-size: 14px;\r\n}\r\n\r\n.loading {\r\n  padding: 40px 0;\r\n  /* 添加最小高度，避免布局跳动 */\r\n  min-height: 400px;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n.no-data {\r\n  text-align: center;\r\n  padding: 60px 0;\r\n  /* 添加最小高度，避免布局跳动 */\r\n  min-height: 400px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n.goods-list {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\r\n  gap: 24px;\r\n  margin-bottom: 30px;\r\n  /* 添加最小高度，避免布局跳动 */\r\n  min-height: 400px;\r\n  /* 添加过渡效果 */\r\n  transition: all 0.3s ease-in-out;\r\n}\r\n\r\n@keyframes fadeIn {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(20px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n.goods-card {\r\n  border: 1px solid #e0e0e0;\r\n  border-radius: 16px;\r\n  overflow: hidden;\r\n  background: white;\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);\r\n  transition: all 0.3s ease;\r\n  position: relative;\r\n  cursor: pointer;\r\n  /* 添加动画效果 */\r\n  animation: fadeIn 0.6s ease-out;\r\n  /* 添加will-change优化性能 */\r\n  will-change: transform, opacity;\r\n}\r\n\r\n.goods-card:hover {\r\n  transform: translateY(-8px);\r\n  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);\r\n  border-color: #409eff;\r\n}\r\n\r\n.goods-image {\r\n  width: 100%;\r\n  height: 200px;\r\n  object-fit: cover;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.goods-card:hover .goods-image {\r\n  transform: scale(1.05);\r\n}\r\n\r\n.goods-info {\r\n  padding: 20px;\r\n}\r\n\r\n.goods-name {\r\n  font-size: 18px;\r\n  color: #333;\r\n  margin: 0 0 8px;\r\n  font-weight: bold;\r\n  line-height: 1.4;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n  overflow: hidden;\r\n}\r\n\r\n.goods-desc {\r\n  color: #666;\r\n  font-size: 14px;\r\n  margin: 0 0 12px;\r\n  line-height: 1.5;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n  overflow: hidden;\r\n}\r\n\r\n.goods-price {\r\n  color: #e60000;\r\n  margin: 15px 0;\r\n  font-size: 20px;\r\n  font-weight: bold;\r\n}\r\n\r\n.btn-add-cart {\r\n  width: 100%;\r\n  padding: 12px;\r\n  background: linear-gradient(45deg, #409eff, #67c23a);\r\n  color: white;\r\n  border: none;\r\n  border-radius: 8px;\r\n  cursor: pointer;\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  transition: all 0.3s ease;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.btn-add-cart:hover {\r\n  background: linear-gradient(45deg, #337ecc, #5daf34);\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);\r\n}\r\n\r\n.btn-add-cart:active {\r\n  transform: translateY(0);\r\n}\r\n\r\n.pagination-wrapper {\r\n  display: flex;\r\n  justify-content: center;\r\n  margin-top: 40px;\r\n  padding: 20px 0;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .goods-container {\r\n    padding: 0 12px;\r\n  }\r\n\r\n  .page-title {\r\n    font-size: 24px;\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .goods-list {\r\n    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\r\n    gap: 16px;\r\n  }\r\n\r\n  .goods-card {\r\n    border-radius: 12px;\r\n  }\r\n\r\n  .goods-image {\r\n    height: 160px;\r\n  }\r\n\r\n  .goods-info {\r\n    padding: 16px;\r\n  }\r\n\r\n  .goods-name {\r\n    font-size: 16px;\r\n  }\r\n\r\n  .goods-price {\r\n    font-size: 18px;\r\n  }\r\n\r\n  .btn-add-cart {\r\n    padding: 10px;\r\n    font-size: 14px;\r\n  }\r\n\r\n  .search-info {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 8px;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .goods-list {\r\n    grid-template-columns: 1fr;\r\n  }\r\n\r\n  .page-title {\r\n    font-size: 20px;\r\n  }\r\n}\r\n</style>"], "mappings": ";;AA6FA,OAAOA,OAAM,MAAO,oBAAmB;AACvC,OAAOC,iBAAgB,MAAO,2BAA0B;AACxD,OAAOC,SAAQ,MAAO,4BAA2B;AACjD,OAAOC,eAAc,MAAO,kCAAiC;AAC7D,OAAOC,aAAY,MAAO,gCAA+B;AAGzD,eAAe;EACbC,IAAI,EAAE,WAAW;EAEjBC,UAAU,EAAE;IACRL,iBAAiB;IACjBC,SAAS;IACTC,eAAe;IACfC;EACF,CAAC;EAEHG,IAAIA,CAAA,EAAG;IACL,OAAO;MACLF,IAAI,EAAE,EAAE;MACRG,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE,IAAI;MACbC,SAAS,EAAE,EAAE;MACbC,aAAa,EAAE,EAAE;MAAE;MACnBC,WAAW,EAAE,CAAC;MACdC,QAAQ,EAAE,EAAE;MACZC,UAAU,EAAE,CAAC;MACbC,YAAY,EAAE,KAAK;MAAE;MACrBC,oBAAoB,EAAE,IAAI;MAAE;MAC5BC,mBAAmB,EAAE,IAAI;MAAE;MAC3BC,iBAAiB,EAAE,KAAK;MAAE;MAC1BC,gBAAgB,EAAE,KAAK;MAAE;MACzBC,eAAe,EAAE,CAAC,EAAE;IACtB,CAAC;EACH,CAAC;EAEDC,QAAQ,EAAE;IACR;IACAC,UAAUA,CAAA,EAAG;MACX,OAAO,IAAI,CAACZ,SAAS,CAACa,MAAM;IAC9B;EACF,CAAC;EAEDC,KAAK,EAAE;IACL;IACA,qBAAqB,EAAE;MACrBC,OAAOA,CAACC,SAAS,EAAE;QACjB,IAAIA,SAAS,EAAE;UACb,IAAI,CAACC,YAAY,CAACD,SAAS,CAAC;QAC9B,OAAO;UACL,IAAI,CAACE,iBAAiB,CAAC,CAAC;QAC1B;MACF,CAAC;MACDC,SAAS,EAAE;IACb;EACF,CAAC;EAEDC,OAAOA,CAAA,EAAG;IACR;IACA,MAAMC,WAAU,GAAI,IAAI,CAACC,MAAM,CAACC,KAAK,CAACC,MAAM;IAC5C,MAAMC,UAAS,GAAI,IAAI,CAACH,MAAM,CAACC,KAAK,CAACE,UAAU;IAE/C,IAAIJ,WAAW,EAAE;MACf,IAAI,CAACJ,YAAY,CAACI,WAAW,CAAC;IAChC,OAAO,IAAII,UAAU,EAAE;MACrB;MACA,IAAI,CAACnB,oBAAmB,GAAIoB,QAAQ,CAACD,UAAU,CAAC;MAChD,IAAI,CAACjB,iBAAgB,GAAI,IAAI;MAC7B,IAAI,CAACmB,cAAc,CAAC,CAAC;IACvB,OAAO;MACL,IAAI,CAACA,cAAc,CAAC,CAAC;IACvB;EACF,CAAC;EAEDC,OAAOA,CAAA,EAAG;IACR;IACA,IAAI,CAACC,gBAAgB,CAAC,CAAC;IACvB;IACAC,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEC,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAC;IAC7CJ,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE,wBAAwB,CAAC;IAChDD,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE,uBAAuB,CAAC;;IAE/C;IACA,IAAI,CAACJ,cAAc,CAAC,CAAC;EACvB,CAAC;EAEDQ,OAAO,EAAE;IACP;IACAC,oBAAoBA,CAACC,QAAQ,EAAE;MAC7BP,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEM,QAAQ,CAAC;MAC9B,IAAI,CAAC/B,oBAAmB,GAAI+B,QAAQ,CAACZ,UAAU;MAC/C,IAAI,CAACjB,iBAAgB,GAAI6B,QAAQ,CAACZ,UAAS,KAAM,IAAI;MACrD,IAAI,CAACvB,WAAU,GAAI,CAAC;MAEpB,IAAI,IAAI,CAACG,YAAY,EAAE;QACrB,IAAI,CAACiC,WAAW,CAAC,CAAC;MACpB,OAAO;QACL,IAAI,CAACX,cAAc,CAAC,CAAC;MACvB;IACF,CAAC;IAED;IACAY,gBAAgBA,CAACC,KAAK,EAAE;MACtBV,OAAO,CAACW,KAAK,CAAC,SAAS,EAAED,KAAK,CAACE,MAAM,CAACC,GAAG,CAAC;MAC1Cb,OAAO,CAACW,KAAK,CAAC,OAAO,EAAED,KAAK,CAACE,MAAM,CAAC;MACpC;MACAF,KAAK,CAACE,MAAM,CAACE,KAAK,CAACC,OAAM,GAAI,MAAM;MACnCL,KAAK,CAACE,MAAM,CAACE,KAAK,CAACE,eAAc,GAAI,SAAS;MAC9CN,KAAK,CAACE,MAAM,CAACE,KAAK,CAACG,MAAK,GAAI,gBAAgB;IAC9C,CAAC;IAED;IACAC,eAAeA,CAACR,KAAK,EAAE;MACrBV,OAAO,CAACC,GAAG,CAAC,SAAS,EAAES,KAAK,CAACE,MAAM,CAACC,GAAG,CAAC;MACxCb,OAAO,CAACC,GAAG,CAAC,OAAO,EAAES,KAAK,CAACE,MAAM,CAACO,YAAY,EAAE,GAAG,EAAET,KAAK,CAACE,MAAM,CAACQ,aAAa,CAAC;IAClF,CAAC;IAED;IACAC,iBAAiBA,CAACC,IAAI,EAAE;MACtB,IAAI,CAAC1C,eAAc,GAAI0C,IAAI;MAC3B,IAAI,CAAC3C,gBAAe,GAAI,IAAI;MAC5BqB,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEqB,IAAI,CAAC;IAC9B,CAAC;IAED;IACAC,SAASA,CAACD,IAAI,EAAE;MACdtB,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEqB,IAAI,CAACzD,IAAI,CAAC;MAChC,IAAI,CAAC2D,KAAK,CAAC,aAAa,EAAEF,IAAI,CAAC;MAC/B,IAAI,CAACG,QAAQ,CAACC,OAAO,CAAC,UAAUJ,IAAI,CAACzD,IAAI,EAAE,CAAC;IAC9C,CAAC;IAED;IACA8D,MAAMA,CAACL,IAAI,EAAE;MACXtB,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEqB,IAAI,CAACzD,IAAI,CAAC;MAC/B,IAAI,CAAC4D,QAAQ,CAACG,IAAI,CAAC,QAAQN,IAAI,CAACzD,IAAI,EAAE,CAAC;MACvC;IACF,CAAC;IAED;IACA,MAAMgC,cAAcA,CAAA,EAAG;MACrB,IAAI;QACF,IAAI,CAAC5B,OAAM,GAAI,IAAI;QACnB,IAAI4D,MAAK,GAAI,CAAC,CAAC;;QAEf;QACA,IAAI,IAAI,CAACnD,iBAAgB,IAAK,IAAI,CAACF,oBAAoB,EAAE;UACvDqD,MAAM,CAAClC,UAAS,GAAI,IAAI,CAACnB,oBAAoB;QAC/C;QAEA,MAAMsD,QAAO,GAAI,MAAMtE,OAAO,CAACuE,GAAG,CAAC,OAAO,EAAEF,MAAM,CAAC;QAEnD,IAAIG,KAAK,CAACC,OAAO,CAACH,QAAQ,KAAKA,QAAQ,CAAC/C,MAAK,GAAI,CAAC,EAAE;UAClD;UACA,IAAI,CAACb,SAAQ,GAAI4D,QAAQ,CAACI,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;YAC7C;YACA,IAAIC,MAAM;YACV,QAAOF,IAAI,CAACtE,IAAI;cACd,KAAK,KAAK;gBACRwE,MAAK,GAAIC,OAAO,CAAC,kBAAkB,CAAC;gBACpC;cACF,KAAK,KAAK;gBACRD,MAAK,GAAIC,OAAO,CAAC,kBAAkB,CAAC;gBACpC;cACF,KAAK,QAAQ;gBACXD,MAAK,GAAIC,OAAO,CAAC,qBAAqB,CAAC;gBACvC;cACF,KAAK,IAAI;gBACPD,MAAK,GAAIC,OAAO,CAAC,iBAAiB,CAAC;gBACnC;cACF,KAAK,MAAM;gBACTD,MAAK,GAAIC,OAAO,CAAC,mBAAmB,CAAC;gBACrC;cACF,KAAK,QAAQ;gBACXD,MAAK,GAAIC,OAAO,CAAC,qBAAqB,CAAC;gBACvC;cACF,KAAK,OAAO;gBACVD,MAAK,GAAIC,OAAO,CAAC,oBAAoB,CAAC;gBACtC;cACF,KAAK,IAAI;gBACPD,MAAK,GAAIC,OAAO,CAAC,iBAAiB,CAAC;gBACnC;cACF;gBAAS;kBACP;kBACA,MAAMC,aAAY,GAAI,CACpBD,OAAO,CAAC,kBAAkB,CAAC,EAC3BA,OAAO,CAAC,kBAAkB,CAAC,EAC3BA,OAAO,CAAC,qBAAqB,CAAC,EAC9BA,OAAO,CAAC,iBAAiB,CAAC,EAC1BA,OAAO,CAAC,mBAAmB,EAC5B;kBACDD,MAAK,GAAIE,aAAa,CAACH,KAAI,GAAIG,aAAa,CAACxD,MAAM,CAAC;kBACpD;gBACF;YACF;YACA,OAAO;cACL,GAAGoD,IAAI;cACPK,IAAI,EAAEH,MAAM;cACZI,WAAW,EAAEN,IAAI,CAACM,WAAU,IAAK,IAAI,CAACC,qBAAqB,CAACP,IAAI,CAACtE,IAAI,CAAC;cAAE;cACxE8E,KAAK,EAAER,IAAI,CAACQ,KAAI,IAAK,CAACC,IAAI,CAACC,MAAM,CAAC,IAAI,GAAE,GAAI,EAAE,EAAEC,OAAO,CAAC,CAAC,EAAE;YAC7D,CAAC;UACH,CAAC,CAAC;UACF9C,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAAC/B,SAAS,CAAC;QAC/C,OAAO;UACL;UACA,IAAI,CAACA,SAAQ,GAAI,IAAI,CAAC6E,8BAA8B,CAAC,CAAC;UACtD/C,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAAC/B,SAAS,CAAC;QAC/C;QAEA,IAAI,CAACI,UAAS,GAAI,IAAI,CAACJ,SAAS,CAACa,MAAM;QACvC,IAAI,CAACR,YAAW,GAAI,KAAK;MAC3B,EAAE,OAAOoC,KAAK,EAAE;QACdX,OAAO,CAACW,KAAK,CAAC,OAAO,EAAEA,KAAK,CAACqC,OAAM,IAAKrC,KAAK,CAAC;QAC9C;QACA,IAAI,CAACzC,SAAQ,GAAI,IAAI,CAAC6E,8BAA8B,CAAC,CAAC;QACtD,IAAI,CAACzE,UAAS,GAAI,IAAI,CAACJ,SAAS,CAACa,MAAM;QACvCiB,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAAC/B,SAAS,CAAC;MACrD,UAAU;QACR,IAAI,CAACD,OAAM,GAAI,KAAK;MACtB;IACF,CAAC;IAED;IACA8E,8BAA8BA,CAAA,EAAG;MAC/B,OAAO,CACL;QACEE,EAAE,EAAE,CAAC;QACLpF,IAAI,EAAE,KAAK;QACX4E,WAAW,EAAE,uCAAuC;QACpDD,IAAI,EAAEF,OAAO,CAAC,kBAAkB,CAAC;QACjCK,KAAK,EAAE,MAAM;QACbO,QAAQ,EAAE,IAAI;QACdC,KAAK,EAAE,GAAG;QACVC,SAAS,EAAE;MACb,CAAC,EACD;QACEH,EAAE,EAAE,CAAC;QACLpF,IAAI,EAAE,KAAK;QACX4E,WAAW,EAAE,oCAAoC;QACjDD,IAAI,EAAEF,OAAO,CAAC,kBAAkB,CAAC;QACjCK,KAAK,EAAE,MAAM;QACbO,QAAQ,EAAE,IAAI;QACdC,KAAK,EAAE,EAAE;QACTC,SAAS,EAAE;MACb,CAAC,EACD;QACEH,EAAE,EAAE,CAAC;QACLpF,IAAI,EAAE,QAAQ;QACd4E,WAAW,EAAE,wCAAwC;QACrDD,IAAI,EAAEF,OAAO,CAAC,qBAAqB,CAAC;QACpCK,KAAK,EAAE,MAAM;QACbO,QAAQ,EAAE,IAAI;QACdC,KAAK,EAAE,EAAE;QACTC,SAAS,EAAE;MACb,CAAC,EACD;QACEH,EAAE,EAAE,CAAC;QACLpF,IAAI,EAAE,IAAI;QACV4E,WAAW,EAAE,kBAAkB;QAC/BD,IAAI,EAAEF,OAAO,CAAC,iBAAiB,CAAC;QAChCK,KAAK,EAAE,MAAM;QACbO,QAAQ,EAAE,IAAI;QACdC,KAAK,EAAE,EAAE;QACTC,SAAS,EAAE;MACb,CAAC,EACD;QACEH,EAAE,EAAE,CAAC;QACLpF,IAAI,EAAE,MAAM;QACZ4E,WAAW,EAAE,eAAe;QAC5BD,IAAI,EAAEF,OAAO,CAAC,mBAAmB,CAAC;QAClCK,KAAK,EAAE,OAAO;QACdO,QAAQ,EAAE,IAAI;QACdC,KAAK,EAAE,EAAE;QACTC,SAAS,EAAE;MACb,CAAC,EACD;QACEH,EAAE,EAAE,CAAC;QACLpF,IAAI,EAAE,QAAQ;QACd4E,WAAW,EAAE,kBAAkB;QAC/BD,IAAI,EAAEF,OAAO,CAAC,qBAAqB,CAAC;QACpCK,KAAK,EAAE,KAAK;QACZO,QAAQ,EAAE,IAAI;QACdC,KAAK,EAAE,GAAG;QACVC,SAAS,EAAE;MACb,CAAC,EACD;QACEH,EAAE,EAAE,CAAC;QACLpF,IAAI,EAAE,OAAO;QACb4E,WAAW,EAAE,gBAAgB;QAC7BD,IAAI,EAAEF,OAAO,CAAC,oBAAoB,CAAC;QACnCK,KAAK,EAAE,KAAK;QACZO,QAAQ,EAAE,IAAI;QACdC,KAAK,EAAE,EAAE;QACTC,SAAS,EAAE;MACb,CAAC,EACD;QACEH,EAAE,EAAE,CAAC;QACLpF,IAAI,EAAE,IAAI;QACV4E,WAAW,EAAE,kBAAkB;QAC/BD,IAAI,EAAEF,OAAO,CAAC,iBAAiB,CAAC;QAChCK,KAAK,EAAE,MAAM;QACbO,QAAQ,EAAE,IAAI;QACdC,KAAK,EAAE,EAAE;QACTC,SAAS,EAAE;MACb,EACD;IACH,CAAC;IAED;IACA,MAAMjE,YAAYA,CAACkE,OAAO,EAAE;MAC1B,IAAI,CAAClF,aAAY,GAAIkF,OAAO;MAC5B,IAAI,CAAC9E,YAAW,GAAI,IAAI;MACxB,IAAI,CAACH,WAAU,GAAI,CAAC;MACpB,MAAM,IAAI,CAACoC,WAAW,CAAC,CAAC;IAC1B,CAAC;IAED;IACA,MAAMA,WAAWA,CAAA,EAAG;MAClB,IAAI;QACF,IAAI,CAACvC,OAAM,GAAI,IAAI;QACnB,IAAI4D,MAAK,GAAI;UACXyB,UAAU,EAAE,IAAI,CAACnF,aAAa;UAC9BoF,OAAO,EAAE,IAAI,CAACnF,WAAW;UACzBC,QAAQ,EAAE,IAAI,CAACA;QACjB,CAAC;;QAED;QACA,IAAI,IAAI,CAACK,iBAAiB,EAAE;UAC1BmD,MAAK,GAAI;YACP,GAAGA,MAAM;YACT2B,YAAY,EAAE,IAAI,CAAChF,oBAAoB;YACvCiF,WAAW,EAAE,IAAI,CAAChF;UACpB,CAAC;QACH;QAEA,MAAMqD,QAAO,GAAI,MAAMtE,OAAO,CAACuE,GAAG,CAAC,YAAY,EAAEF,MAAM,CAAC;QAExD,IAAIC,QAAO,IAAKA,QAAQ,CAAC4B,OAAO,EAAE;UAChC,IAAI,CAACxF,SAAQ,GAAI4D,QAAQ,CAAC4B,OAAO;UACjC,IAAI,CAACpF,UAAS,GAAIwD,QAAQ,CAAC6B,KAAI,IAAK,CAAC;QACvC,OAAO;UACL,IAAI,CAACzF,SAAQ,GAAI,EAAE;UACnB,IAAI,CAACI,UAAS,GAAI,CAAC;QACrB;MACF,EAAE,OAAOqC,KAAK,EAAE;QACdX,OAAO,CAACW,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;QAC7B,IAAI,CAACzC,SAAQ,GAAI,EAAE;QACnB,IAAI,CAACI,UAAS,GAAI,CAAC;QACnB,IAAI,CAACmD,QAAQ,CAACd,KAAK,CAAC,UAAU,CAAC;MACjC,UAAU;QACR,IAAI,CAAC1C,OAAM,GAAI,KAAK;MACtB;IACF,CAAC;IAED;IACA2F,mBAAmBA,CAACC,OAAO,EAAE;MAC3B,IAAIA,OAAM,IAAKA,OAAO,CAACH,OAAO,EAAE;QAC9B,IAAI,CAACxF,SAAQ,GAAI2F,OAAO,CAACH,OAAO;QAChC,IAAI,CAACpF,UAAS,GAAIuF,OAAO,CAACF,KAAI,IAAK,CAAC;MACtC;IACF,CAAC;IAED;IACAvE,iBAAiBA,CAAA,EAAG;MAClB,IAAI,CAACjB,aAAY,GAAI,EAAE;MACvB,IAAI,CAACI,YAAW,GAAI,KAAK;MACzB,IAAI,CAACH,WAAU,GAAI,CAAC;;MAEpB;MACA,IAAI,CAAC,IAAI,CAACM,iBAAiB,EAAE;QAC3B,IAAI,CAACmB,cAAc,CAAC,CAAC;MACvB,OAAO;QACL;QACA,IAAI,CAACW,WAAW,CAAC,CAAC;MACpB;IACF,CAAC;IAED;IACAsD,mBAAmBA,CAAA,EAAG;MACpB;MACA,MAAMC,eAAc,GAAI,IAAI,CAACC,KAAK,CAACD,eAAe;MAClD,IAAIA,eAAc,IAAK,OAAOA,eAAe,CAACD,mBAAkB,KAAM,UAAU,EAAE;QAChFC,eAAe,CAACD,mBAAmB,CAAC,CAAC;MACvC;IACF,CAAC;IAED;IACA/D,gBAAgBA,CAAA,EAAG;MACjB,MAAMkE,UAAS,GAAI,IAAI,CAACzE,MAAM,CAACC,KAAK;MACpC,IAAIwE,UAAU,CAACT,YAAW,IAAKS,UAAU,CAACR,WAAW,EAAE;QACrD,IAAI,CAACjF,oBAAmB,GAAIyF,UAAU,CAACT,YAAW,IAAK,IAAI;QAC3D,IAAI,CAAC/E,mBAAkB,GAAIwF,UAAU,CAACR,WAAU,IAAK,IAAI;QACzD,IAAI,CAAC/E,iBAAgB,GAAI,IAAI;;QAE7B;QACA,IAAI,CAACwF,SAAS,CAAC,MAAM;UACnB,MAAMH,eAAc,GAAI,IAAI,CAACC,KAAK,CAACD,eAAe;UAClD,IAAIA,eAAc,IAAK,OAAOA,eAAe,CAACI,mBAAkB,KAAM,UAAU,EAAE;YAChFJ,eAAe,CAACI,mBAAmB,CAAC;cAClCX,YAAY,EAAE,IAAI,CAAChF,oBAAoB;cACvCiF,WAAW,EAAE,IAAI,CAAChF;YACpB,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;MACJ;IACF,CAAC;IAED;IACA2F,mBAAmBA,CAACjC,IAAI,EAAE;MACxBnC,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEkC,IAAI,CAAC;MAC7B;MACA;IACF,CAAC;IAED;IACAkC,gBAAgBA,CAACC,OAAO,EAAE;MACxB,IAAI,CAACjG,QAAO,GAAIiG,OAAO;MACvB,IAAI,CAAClG,WAAU,GAAI,CAAC;MACpB,IAAI,IAAI,CAACG,YAAY,EAAE;QACrB,IAAI,CAACiC,WAAW,CAAC,CAAC;MACpB,OAAO;QACL,IAAI,CAACX,cAAc,CAAC,CAAC;MACvB;IACF,CAAC;IAED;IACA0E,mBAAmBA,CAACC,OAAO,EAAE;MAC3B,IAAI,CAACpG,WAAU,GAAIoG,OAAO;MAC1B,IAAI,IAAI,CAACjG,YAAY,EAAE;QACrB,IAAI,CAACiC,WAAW,CAAC,CAAC;MACpB,OAAO;QACL,IAAI,CAACX,cAAc,CAAC,CAAC;MACvB;IACF;EACF,CAAC;EAID4E,aAAaA,CAAA,EAAG;IACdzE,OAAO,CAACC,GAAG,CAAC,UAAU,CAAC;EACzB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}