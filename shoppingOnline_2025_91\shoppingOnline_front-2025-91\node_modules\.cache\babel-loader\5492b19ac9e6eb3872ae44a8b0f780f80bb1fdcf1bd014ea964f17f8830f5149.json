{"ast": null, "code": "// src/main.js\nimport { createApp } from 'vue';\nimport { createPinia } from 'pinia';\nimport App from './App.vue';\nimport ElementPlus from 'element-plus';\nimport 'element-plus/dist/index.css';\nimport * as ElementPlusIconsVue from '@element-plus/icons-vue';\nimport router from './router';\n\n// 创建应用\nconst app = createApp(App);\n\n// ✅ 使用 Pinia（注意：有括号！）\napp.use(createPinia());\n\n// 使用 Element Plus\napp.use(ElementPlus);\n\n// 注册所有图标\nfor (const [key, component] of Object.entries(ElementPlusIconsVue)) {\n  app.component(key, component);\n}\n\n// 使用 Vue Router\napp.use(router);\n\n// 抑制 ResizeObserver 错误\nconst resizeObserverErrorHandler = e => {\n  if (e.message === 'ResizeObserver loop completed with undelivered notifications.') {\n    e.stopImmediatePropagation();\n  }\n};\nwindow.addEventListener('error', resizeObserverErrorHandler);\n\n// 挂载\napp.mount('#app');", "map": {"version": 3, "names": ["createApp", "createPinia", "App", "ElementPlus", "ElementPlusIconsVue", "router", "app", "use", "key", "component", "Object", "entries", "resizeObserverErrorHandler", "e", "message", "stopImmediatePropagation", "window", "addEventListener", "mount"], "sources": ["D:/2025_down/project/shoppingOnline_2025_91/shoppingOnline_front-2025-91/src/main.js"], "sourcesContent": ["// src/main.js\r\nimport { createApp } from 'vue'\r\nimport { createPinia } from 'pinia'\r\nimport App from './App.vue'\r\nimport ElementPlus from 'element-plus'\r\nimport 'element-plus/dist/index.css'\r\nimport * as ElementPlusIconsVue from '@element-plus/icons-vue'\r\nimport router from './router'\r\n\r\n// 创建应用\r\nconst app = createApp(App)\r\n\r\n// ✅ 使用 Pinia（注意：有括号！）\r\napp.use(createPinia())\r\n\r\n// 使用 Element Plus\r\napp.use(ElementPlus)\r\n\r\n// 注册所有图标\r\nfor (const [key, component] of Object.entries(ElementPlusIconsVue)) {\r\n  app.component(key, component)\r\n}\r\n\r\n// 使用 Vue Router\r\napp.use(router)\r\n\r\n// 抑制 ResizeObserver 错误\r\nconst resizeObserverErrorHandler = (e) => {\r\n  if (e.message === 'ResizeObserver loop completed with undelivered notifications.') {\r\n    e.stopImmediatePropagation()\r\n  }\r\n}\r\nwindow.addEventListener('error', resizeObserverErrorHandler)\r\n\r\n// 挂载\r\napp.mount('#app')"], "mappings": "AAAA;AACA,SAASA,SAAS,QAAQ,KAAK;AAC/B,SAASC,WAAW,QAAQ,OAAO;AACnC,OAAOC,GAAG,MAAM,WAAW;AAC3B,OAAOC,WAAW,MAAM,cAAc;AACtC,OAAO,6BAA6B;AACpC,OAAO,KAAKC,mBAAmB,MAAM,yBAAyB;AAC9D,OAAOC,MAAM,MAAM,UAAU;;AAE7B;AACA,MAAMC,GAAG,GAAGN,SAAS,CAACE,GAAG,CAAC;;AAE1B;AACAI,GAAG,CAACC,GAAG,CAACN,WAAW,CAAC,CAAC,CAAC;;AAEtB;AACAK,GAAG,CAACC,GAAG,CAACJ,WAAW,CAAC;;AAEpB;AACA,KAAK,MAAM,CAACK,GAAG,EAAEC,SAAS,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACP,mBAAmB,CAAC,EAAE;EAClEE,GAAG,CAACG,SAAS,CAACD,GAAG,EAAEC,SAAS,CAAC;AAC/B;;AAEA;AACAH,GAAG,CAACC,GAAG,CAACF,MAAM,CAAC;;AAEf;AACA,MAAMO,0BAA0B,GAAIC,CAAC,IAAK;EACxC,IAAIA,CAAC,CAACC,OAAO,KAAK,+DAA+D,EAAE;IACjFD,CAAC,CAACE,wBAAwB,CAAC,CAAC;EAC9B;AACF,CAAC;AACDC,MAAM,CAACC,gBAAgB,CAAC,OAAO,EAAEL,0BAA0B,CAAC;;AAE5D;AACAN,GAAG,CAACY,KAAK,CAAC,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}