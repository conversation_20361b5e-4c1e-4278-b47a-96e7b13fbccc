{"ast": null, "code": "import { Search } from '@element-plus/icons-vue';\nimport request from '@/utils/request.js';\nexport default {\n  name: 'SearchBox',\n  components: {\n    Search\n  },\n  data() {\n    return {\n      searchText: '',\n      searching: false,\n      showSuggestions: false,\n      suggestions: [],\n      hotKeywords: ['手机', '电脑', '耳机', '键盘', '鼠标'],\n      debounceTimer: null\n    };\n  },\n  methods: {\n    // 处理搜索\n    async handleSearch() {\n      if (!this.searchText.trim()) {\n        this.$message.warning('请输入搜索关键词');\n        return;\n      }\n      this.searching = true;\n      this.showSuggestions = false;\n      try {\n        // 触发搜索事件，传递搜索文本给父组件\n        this.$emit('search', this.searchText.trim());\n\n        // 可以在这里调用搜索API\n        const response = await request.get('/good/page', {\n          searchText: this.searchText.trim(),\n          pageNum: 1,\n          pageSize: 20\n        });\n\n        // 将搜索结果传递给父组件\n        this.$emit('search-results', response);\n      } catch (error) {\n        console.error('搜索失败:', error);\n        this.$message.error('搜索失败，请重试');\n      } finally {\n        this.searching = false;\n      }\n    },\n    // 处理输入变化\n    handleInput() {\n      // 清除之前的定时器\n      if (this.debounceTimer) {\n        clearTimeout(this.debounceTimer);\n      }\n\n      // 设置防抖，500ms后执行搜索建议\n      this.debounceTimer = setTimeout(() => {\n        if (this.searchText.trim().length > 0) {\n          this.fetchSuggestions();\n        } else {\n          this.showSuggestions = false;\n        }\n      }, 500);\n    },\n    // 获取搜索建议\n    async fetchSuggestions() {\n      try {\n        const response = await request.get('/api/good/page', {\n          params: {\n            searchText: this.searchText.trim(),\n            pageNum: 1,\n            pageSize: 5\n          }\n        });\n        if (response && response.records) {\n          this.suggestions = response.records.slice(0, 5); // 最多显示5个建议\n          this.showSuggestions = true;\n        }\n      } catch (error) {\n        console.error('获取搜索建议失败:', error);\n        this.showSuggestions = false;\n      }\n    },\n    // 选择搜索建议\n    selectSuggestion(suggestion) {\n      this.searchText = suggestion.name;\n      this.showSuggestions = false;\n      this.handleSearch();\n    },\n    // 通过热门关键词搜索\n    searchByKeyword(keyword) {\n      this.searchText = keyword;\n      this.handleSearch();\n    },\n    // 清空搜索\n    clearSearch() {\n      this.searchText = '';\n      this.showSuggestions = false;\n      this.$emit('clear-search');\n    }\n  },\n  mounted() {\n    // 点击外部关闭建议框\n    document.addEventListener('click', e => {\n      if (!this.$el.contains(e.target)) {\n        this.showSuggestions = false;\n      }\n    });\n  },\n  beforeUnmount() {\n    if (this.debounceTimer) {\n      clearTimeout(this.debounceTimer);\n    }\n  }\n};", "map": {"version": 3, "names": ["Search", "request", "name", "components", "data", "searchText", "searching", "showSuggestions", "suggestions", "hotKeywords", "deboun<PERSON><PERSON><PERSON>r", "methods", "handleSearch", "trim", "$message", "warning", "$emit", "response", "get", "pageNum", "pageSize", "error", "console", "handleInput", "clearTimeout", "setTimeout", "length", "fetchSuggestions", "params", "records", "slice", "selectSuggestion", "suggestion", "searchByKeyword", "keyword", "clearSearch", "mounted", "document", "addEventListener", "e", "$el", "contains", "target", "beforeUnmount"], "sources": ["D:\\2025_down\\project\\shoppingOnline_2025_91\\shoppingOnline_front-2025-91\\src\\components\\SearchBox.vue"], "sourcesContent": ["<template>\n  <div class=\"search-container\">\n    <div class=\"search-wrapper\">\n      <el-input\n        v-model=\"searchText\"\n        placeholder=\"搜索商品...\"\n        size=\"large\"\n        class=\"search-input\"\n        @keyup.enter=\"handleSearch\"\n        @input=\"handleInput\"\n        clearable\n      >\n        <template #prefix>\n          <el-icon><Search /></el-icon>\n        </template>\n        <template #suffix>\n          <el-button \n            type=\"primary\" \n            @click=\"handleSearch\"\n            :loading=\"searching\"\n            class=\"search-btn\"\n          >\n            {{ searching ? '搜索中...' : '搜索' }}\n          </el-button>\n        </template>\n      </el-input>\n      \n      <!-- 搜索建议下拉框 -->\n      <div v-if=\"showSuggestions && suggestions.length > 0\" class=\"suggestions-dropdown\">\n        <div \n          v-for=\"(suggestion, index) in suggestions\" \n          :key=\"index\"\n          class=\"suggestion-item\"\n          @click=\"selectSuggestion(suggestion)\"\n        >\n          <el-icon><Search /></el-icon>\n          <span>{{ suggestion.name }}</span>\n          <span class=\"suggestion-price\">¥{{ suggestion.price }}</span>\n        </div>\n      </div>\n    </div>\n    \n    <!-- 热门搜索标签 -->\n    <div class=\"hot-search\" v-if=\"hotKeywords.length > 0\">\n      <span class=\"hot-label\">热门搜索：</span>\n      <el-tag \n        v-for=\"keyword in hotKeywords\" \n        :key=\"keyword\"\n        class=\"hot-tag\"\n        @click=\"searchByKeyword(keyword)\"\n        effect=\"plain\"\n      >\n        {{ keyword }}\n      </el-tag>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { Search } from '@element-plus/icons-vue'\nimport request from '@/utils/request.js'\n\nexport default {\n  name: 'SearchBox',\n  \n  components: {\n    Search\n  },\n  \n  data() {\n    return {\n      searchText: '',\n      searching: false,\n      showSuggestions: false,\n      suggestions: [],\n      hotKeywords: ['手机', '电脑', '耳机', '键盘', '鼠标'],\n      debounceTimer: null\n    }\n  },\n  \n  methods: {\n    // 处理搜索\n    async handleSearch() {\n      if (!this.searchText.trim()) {\n        this.$message.warning('请输入搜索关键词')\n        return\n      }\n      \n      this.searching = true\n      this.showSuggestions = false\n      \n      try {\n        // 触发搜索事件，传递搜索文本给父组件\n        this.$emit('search', this.searchText.trim())\n        \n        // 可以在这里调用搜索API\n        const response = await request.get('/good/page', {\n          searchText: this.searchText.trim(),\n          pageNum: 1,\n          pageSize: 20\n        })\n        \n        // 将搜索结果传递给父组件\n        this.$emit('search-results', response)\n        \n      } catch (error) {\n        console.error('搜索失败:', error)\n        this.$message.error('搜索失败，请重试')\n      } finally {\n        this.searching = false\n      }\n    },\n    \n    // 处理输入变化\n    handleInput() {\n      // 清除之前的定时器\n      if (this.debounceTimer) {\n        clearTimeout(this.debounceTimer)\n      }\n      \n      // 设置防抖，500ms后执行搜索建议\n      this.debounceTimer = setTimeout(() => {\n        if (this.searchText.trim().length > 0) {\n          this.fetchSuggestions()\n        } else {\n          this.showSuggestions = false\n        }\n      }, 500)\n    },\n    \n    // 获取搜索建议\n    async fetchSuggestions() {\n      try {\n        const response = await request.get('/api/good/page', {\n          params: {\n            searchText: this.searchText.trim(),\n            pageNum: 1,\n            pageSize: 5\n          }\n        })\n        \n        if (response && response.records) {\n          this.suggestions = response.records.slice(0, 5) // 最多显示5个建议\n          this.showSuggestions = true\n        }\n      } catch (error) {\n        console.error('获取搜索建议失败:', error)\n        this.showSuggestions = false\n      }\n    },\n    \n    // 选择搜索建议\n    selectSuggestion(suggestion) {\n      this.searchText = suggestion.name\n      this.showSuggestions = false\n      this.handleSearch()\n    },\n    \n    // 通过热门关键词搜索\n    searchByKeyword(keyword) {\n      this.searchText = keyword\n      this.handleSearch()\n    },\n    \n    // 清空搜索\n    clearSearch() {\n      this.searchText = ''\n      this.showSuggestions = false\n      this.$emit('clear-search')\n    }\n  },\n  \n  mounted() {\n    // 点击外部关闭建议框\n    document.addEventListener('click', (e) => {\n      if (!this.$el.contains(e.target)) {\n        this.showSuggestions = false\n      }\n    })\n  },\n  \n  beforeUnmount() {\n    if (this.debounceTimer) {\n      clearTimeout(this.debounceTimer)\n    }\n  }\n}\n</script>\n\n<style scoped>\n.search-container {\n  width: 100%;\n  max-width: 600px;\n  margin: 0 auto 20px;\n}\n\n.search-wrapper {\n  position: relative;\n}\n\n.search-input {\n  width: 100%;\n}\n\n.search-input :deep(.el-input__inner) {\n  border-radius: 25px;\n  padding-left: 45px;\n  padding-right: 120px;\n  height: 50px;\n  font-size: 16px;\n}\n\n.search-btn {\n  border-radius: 20px;\n  margin-right: 5px;\n}\n\n.suggestions-dropdown {\n  position: absolute;\n  top: 100%;\n  left: 0;\n  right: 0;\n  background: white;\n  border: 1px solid #e4e7ed;\n  border-radius: 8px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\n  z-index: 1000;\n  max-height: 300px;\n  overflow-y: auto;\n}\n\n.suggestion-item {\n  display: flex;\n  align-items: center;\n  padding: 12px 16px;\n  cursor: pointer;\n  transition: background 0.2s;\n  gap: 8px;\n}\n\n.suggestion-item:hover {\n  background: #f5f7fa;\n}\n\n.suggestion-item:not(:last-child) {\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.suggestion-price {\n  margin-left: auto;\n  color: #e60000;\n  font-weight: bold;\n}\n\n.hot-search {\n  display: flex;\n  align-items: center;\n  flex-wrap: wrap;\n  gap: 8px;\n  margin-top: 15px;\n  padding: 0 5px;\n}\n\n.hot-label {\n  color: #666;\n  font-size: 14px;\n  white-space: nowrap;\n}\n\n.hot-tag {\n  cursor: pointer;\n  transition: all 0.2s;\n}\n\n.hot-tag:hover {\n  background: #409eff;\n  color: white;\n  border-color: #409eff;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .search-input :deep(.el-input__inner) {\n    height: 45px;\n    font-size: 14px;\n    padding-right: 100px;\n  }\n  \n  .search-btn {\n    font-size: 14px;\n  }\n  \n  .hot-search {\n    margin-top: 10px;\n  }\n  \n  .hot-label {\n    font-size: 12px;\n  }\n}\n</style>\n"], "mappings": "AA2DA,SAASA,MAAK,QAAS,yBAAwB;AAC/C,OAAOC,OAAM,MAAO,oBAAmB;AAEvC,eAAe;EACbC,IAAI,EAAE,WAAW;EAEjBC,UAAU,EAAE;IACVH;EACF,CAAC;EAEDI,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,UAAU,EAAE,EAAE;MACdC,SAAS,EAAE,KAAK;MAChBC,eAAe,EAAE,KAAK;MACtBC,WAAW,EAAE,EAAE;MACfC,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;MAC3CC,aAAa,EAAE;IACjB;EACF,CAAC;EAEDC,OAAO,EAAE;IACP;IACA,MAAMC,YAAYA,CAAA,EAAG;MACnB,IAAI,CAAC,IAAI,CAACP,UAAU,CAACQ,IAAI,CAAC,CAAC,EAAE;QAC3B,IAAI,CAACC,QAAQ,CAACC,OAAO,CAAC,UAAU;QAChC;MACF;MAEA,IAAI,CAACT,SAAQ,GAAI,IAAG;MACpB,IAAI,CAACC,eAAc,GAAI,KAAI;MAE3B,IAAI;QACF;QACA,IAAI,CAACS,KAAK,CAAC,QAAQ,EAAE,IAAI,CAACX,UAAU,CAACQ,IAAI,CAAC,CAAC;;QAE3C;QACA,MAAMI,QAAO,GAAI,MAAMhB,OAAO,CAACiB,GAAG,CAAC,YAAY,EAAE;UAC/Cb,UAAU,EAAE,IAAI,CAACA,UAAU,CAACQ,IAAI,CAAC,CAAC;UAClCM,OAAO,EAAE,CAAC;UACVC,QAAQ,EAAE;QACZ,CAAC;;QAED;QACA,IAAI,CAACJ,KAAK,CAAC,gBAAgB,EAAEC,QAAQ;MAEvC,EAAE,OAAOI,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,OAAO,EAAEA,KAAK;QAC5B,IAAI,CAACP,QAAQ,CAACO,KAAK,CAAC,UAAU;MAChC,UAAU;QACR,IAAI,CAACf,SAAQ,GAAI,KAAI;MACvB;IACF,CAAC;IAED;IACAiB,WAAWA,CAAA,EAAG;MACZ;MACA,IAAI,IAAI,CAACb,aAAa,EAAE;QACtBc,YAAY,CAAC,IAAI,CAACd,aAAa;MACjC;;MAEA;MACA,IAAI,CAACA,aAAY,GAAIe,UAAU,CAAC,MAAM;QACpC,IAAI,IAAI,CAACpB,UAAU,CAACQ,IAAI,CAAC,CAAC,CAACa,MAAK,GAAI,CAAC,EAAE;UACrC,IAAI,CAACC,gBAAgB,CAAC;QACxB,OAAO;UACL,IAAI,CAACpB,eAAc,GAAI,KAAI;QAC7B;MACF,CAAC,EAAE,GAAG;IACR,CAAC;IAED;IACA,MAAMoB,gBAAgBA,CAAA,EAAG;MACvB,IAAI;QACF,MAAMV,QAAO,GAAI,MAAMhB,OAAO,CAACiB,GAAG,CAAC,gBAAgB,EAAE;UACnDU,MAAM,EAAE;YACNvB,UAAU,EAAE,IAAI,CAACA,UAAU,CAACQ,IAAI,CAAC,CAAC;YAClCM,OAAO,EAAE,CAAC;YACVC,QAAQ,EAAE;UACZ;QACF,CAAC;QAED,IAAIH,QAAO,IAAKA,QAAQ,CAACY,OAAO,EAAE;UAChC,IAAI,CAACrB,WAAU,GAAIS,QAAQ,CAACY,OAAO,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAE;UAChD,IAAI,CAACvB,eAAc,GAAI,IAAG;QAC5B;MACF,EAAE,OAAOc,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK;QAChC,IAAI,CAACd,eAAc,GAAI,KAAI;MAC7B;IACF,CAAC;IAED;IACAwB,gBAAgBA,CAACC,UAAU,EAAE;MAC3B,IAAI,CAAC3B,UAAS,GAAI2B,UAAU,CAAC9B,IAAG;MAChC,IAAI,CAACK,eAAc,GAAI,KAAI;MAC3B,IAAI,CAACK,YAAY,CAAC;IACpB,CAAC;IAED;IACAqB,eAAeA,CAACC,OAAO,EAAE;MACvB,IAAI,CAAC7B,UAAS,GAAI6B,OAAM;MACxB,IAAI,CAACtB,YAAY,CAAC;IACpB,CAAC;IAED;IACAuB,WAAWA,CAAA,EAAG;MACZ,IAAI,CAAC9B,UAAS,GAAI,EAAC;MACnB,IAAI,CAACE,eAAc,GAAI,KAAI;MAC3B,IAAI,CAACS,KAAK,CAAC,cAAc;IAC3B;EACF,CAAC;EAEDoB,OAAOA,CAAA,EAAG;IACR;IACAC,QAAQ,CAACC,gBAAgB,CAAC,OAAO,EAAGC,CAAC,IAAK;MACxC,IAAI,CAAC,IAAI,CAACC,GAAG,CAACC,QAAQ,CAACF,CAAC,CAACG,MAAM,CAAC,EAAE;QAChC,IAAI,CAACnC,eAAc,GAAI,KAAI;MAC7B;IACF,CAAC;EACH,CAAC;EAEDoC,aAAaA,CAAA,EAAG;IACd,IAAI,IAAI,CAACjC,aAAa,EAAE;MACtBc,YAAY,CAAC,IAAI,CAACd,aAAa;IACjC;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}