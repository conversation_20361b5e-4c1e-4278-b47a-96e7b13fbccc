{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createTextVNode as _createTextVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, renderList as _renderList, Fragment as _Fragment, toDisplayString as _toDisplayString, withModifiers as _withModifiers } from \"vue\";\nconst _hoisted_1 = {\n  class: \"home-container\"\n};\nconst _hoisted_2 = {\n  class: \"search-section\"\n};\nconst _hoisted_3 = {\n  class: \"recommended-section\"\n};\nconst _hoisted_4 = {\n  class: \"section-title\"\n};\nconst _hoisted_5 = {\n  key: 0,\n  class: \"loading\"\n};\nconst _hoisted_6 = {\n  class: \"goods-grid\"\n};\nconst _hoisted_7 = [\"onClick\"];\nconst _hoisted_8 = [\"src\", \"alt\"];\nconst _hoisted_9 = {\n  class: \"goods-info\"\n};\nconst _hoisted_10 = {\n  class: \"goods-name\"\n};\nconst _hoisted_11 = {\n  class: \"goods-desc\"\n};\nconst _hoisted_12 = {\n  class: \"goods-price\"\n};\nconst _hoisted_13 = {\n  class: \"more-goods\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_CarouselComponent = _resolveComponent(\"CarouselComponent\");\n  const _component_SearchBox = _resolveComponent(\"SearchBox\");\n  const _component_Star = _resolveComponent(\"Star\");\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_el_skeleton = _resolveComponent(\"el-skeleton\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_ProductDetail = _resolveComponent(\"ProductDetail\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" 欢迎横幅 \"), _cache[6] || (_cache[6] = _createElementVNode(\"div\", {\n    class: \"welcome-banner\"\n  }, [_createElementVNode(\"h1\", {\n    class: \"welcome-title\"\n  }, \"欢迎来到在线购物商城\"), _createElementVNode(\"p\", {\n    class: \"welcome-subtitle\"\n  }, \"发现优质商品，享受购物乐趣\")], -1 /* CACHED */)), _createCommentVNode(\" 轮播图组件 \"), _createVNode(_component_CarouselComponent, {\n    onCarouselClick: $options.handleCarouselClick\n  }, null, 8 /* PROPS */, [\"onCarouselClick\"]), _createCommentVNode(\" 搜索框组件 \"), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_SearchBox, {\n    onSearch: $options.handleSearch,\n    onSearchResults: $options.handleSearchResults,\n    onClearSearch: $options.handleClearSearch\n  }, null, 8 /* PROPS */, [\"onSearch\", \"onSearchResults\", \"onClearSearch\"])]), _createCommentVNode(\" 推荐商品区域 \"), _createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"h2\", _hoisted_4, [_createVNode(_component_el_icon, null, {\n    default: _withCtx(() => [_createVNode(_component_Star)]),\n    _: 1 /* STABLE */\n  }), _cache[3] || (_cache[3] = _createTextVNode(\" 推荐商品 \", -1 /* CACHED */))]), _createCommentVNode(\" 加载状态 \"), $data.loading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_5, [_createVNode(_component_el_skeleton, {\n    rows: 2,\n    animated: \"\"\n  })])) : (_openBlock(), _createElementBlock(_Fragment, {\n    key: 1\n  }, [_createCommentVNode(\" 商品列表 \"), _createElementVNode(\"div\", _hoisted_6, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.recommendedGoods, good => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      class: \"goods-card\",\n      key: good.id,\n      onClick: $event => $options.showProductDetail(good)\n    }, [_createElementVNode(\"img\", {\n      src: good.imgs,\n      alt: good.name,\n      class: \"goods-image\",\n      onError: _cache[0] || (_cache[0] = (...args) => $options.handleImageError && $options.handleImageError(...args)),\n      onLoad: _cache[1] || (_cache[1] = (...args) => $options.handleImageLoad && $options.handleImageLoad(...args))\n    }, null, 40 /* PROPS, NEED_HYDRATION */, _hoisted_8), _createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"h3\", _hoisted_10, _toDisplayString(good.name), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_11, _toDisplayString(good.description || '暂无商品描述'), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_12, \"¥\" + _toDisplayString(good.price), 1 /* TEXT */), _createVNode(_component_el_button, {\n      type: \"primary\",\n      size: \"small\",\n      onClick: _withModifiers($event => $options.addToCart(good), [\"stop\"])\n    }, {\n      default: _withCtx(() => [...(_cache[4] || (_cache[4] = [_createTextVNode(\" 加入购物车 \", -1 /* CACHED */)]))]),\n      _: 2 /* DYNAMIC */\n    }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])])], 8 /* PROPS */, _hoisted_7);\n  }), 128 /* KEYED_FRAGMENT */))])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)), _createCommentVNode(\" 查看更多按钮 \"), _createElementVNode(\"div\", _hoisted_13, [_createVNode(_component_el_button, {\n    type: \"primary\",\n    size: \"large\",\n    onClick: $options.goToGoodsPage\n  }, {\n    default: _withCtx(() => [...(_cache[5] || (_cache[5] = [_createTextVNode(\" 查看更多商品 \", -1 /* CACHED */)]))]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClick\"])])]), _createCommentVNode(\" 商品详情弹窗 \"), _createVNode(_component_ProductDetail, {\n    modelValue: $data.showDetailDialog,\n    \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $data.showDetailDialog = $event),\n    product: $data.selectedProduct,\n    onAddToCart: $options.addToCart,\n    onBuyNow: $options.buyNow\n  }, null, 8 /* PROPS */, [\"modelValue\", \"product\", \"onAddToCart\", \"onBuyNow\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "_createVNode", "_component_CarouselComponent", "onCarouselClick", "$options", "handleCarouselClick", "_hoisted_2", "_component_SearchBox", "onSearch", "handleSearch", "onSearchResults", "handleSearchResults", "onClearSearch", "handleClearSearch", "_hoisted_3", "_hoisted_4", "_component_el_icon", "_component_Star", "$data", "loading", "_hoisted_5", "_component_el_skeleton", "rows", "animated", "_Fragment", "key", "_hoisted_6", "_renderList", "recommendedGoods", "good", "id", "onClick", "$event", "showProductDetail", "src", "imgs", "alt", "name", "onError", "_cache", "args", "handleImageError", "onLoad", "handleImageLoad", "_hoisted_9", "_hoisted_10", "_toDisplayString", "_hoisted_11", "description", "_hoisted_12", "price", "_component_el_button", "type", "size", "_withModifiers", "addToCart", "_hoisted_13", "goToGoodsPage", "_component_ProductDetail", "showDetailDialog", "product", "selectedProduct", "onAddToCart", "onBuyNow", "buyNow"], "sources": ["D:\\2025_down\\project\\shoppingOnline_2025_91\\shoppingOnline_front-2025-91\\src\\views\\Home.vue"], "sourcesContent": ["<template>\r\n  <div class=\"home-container\">\r\n    <!-- 欢迎横幅 -->\r\n    <div class=\"welcome-banner\">\r\n      <h1 class=\"welcome-title\">欢迎来到在线购物商城</h1>\r\n      <p class=\"welcome-subtitle\">发现优质商品，享受购物乐趣</p>\r\n    </div>\r\n\r\n    <!-- 轮播图组件 -->\r\n    <CarouselComponent @carousel-click=\"handleCarouselClick\" />\r\n\r\n    <!-- 搜索框组件 -->\r\n    <div class=\"search-section\">\r\n      <SearchBox\r\n        @search=\"handleSearch\"\r\n        @search-results=\"handleSearchResults\"\r\n        @clear-search=\"handleClearSearch\"\r\n      />\r\n    </div>\r\n\r\n    <!-- 推荐商品区域 -->\r\n    <div class=\"recommended-section\">\r\n      <h2 class=\"section-title\">\r\n        <el-icon><Star /></el-icon>\r\n        推荐商品\r\n      </h2>\r\n\r\n      <!-- 加载状态 -->\r\n      <div v-if=\"loading\" class=\"loading\">\r\n        <el-skeleton :rows=\"2\" animated />\r\n      </div>\r\n\r\n      <!-- 商品列表 -->\r\n      <div v-else class=\"goods-grid\">\r\n        <div\r\n          class=\"goods-card\"\r\n          v-for=\"good in recommendedGoods\"\r\n          :key=\"good.id\"\r\n          @click=\"showProductDetail(good)\"\r\n        >\r\n          <img\r\n            :src=\"good.imgs\"\r\n            :alt=\"good.name\"\r\n            class=\"goods-image\"\r\n            @error=\"handleImageError\"\r\n            @load=\"handleImageLoad\"\r\n          />\r\n          <div class=\"goods-info\">\r\n            <h3 class=\"goods-name\">{{ good.name }}</h3>\r\n            <p class=\"goods-desc\">{{ good.description || '暂无商品描述' }}</p>\r\n            <p class=\"goods-price\">¥{{ good.price }}</p>\r\n            <el-button type=\"primary\" size=\"small\" @click.stop=\"addToCart(good)\">\r\n              加入购物车\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 查看更多按钮 -->\r\n      <div class=\"more-goods\">\r\n        <el-button type=\"primary\" size=\"large\" @click=\"goToGoodsPage\">\r\n          查看更多商品\r\n        </el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 商品详情弹窗 -->\r\n    <ProductDetail\r\n      v-model=\"showDetailDialog\"\r\n      :product=\"selectedProduct\"\r\n      @add-to-cart=\"addToCart\"\r\n      @buy-now=\"buyNow\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CarouselComponent from '@/components/Carousel.vue'\r\nimport SearchBox from '@/components/SearchBox.vue'\r\nimport ProductDetail from '@/components/ProductDetail.vue'\r\n\r\nimport { Star } from '@element-plus/icons-vue'\r\nimport request from '@/utils/request.js'\r\n\r\nexport default {\r\n  name: 'HomeView',\r\n\r\n  components: {\r\n    CarouselComponent,\r\n    SearchBox,\r\n    ProductDetail,\r\n    Star\r\n  },\r\n\r\n  data() {\r\n    return {\r\n      loading: true,\r\n      recommendedGoods: [],\r\n      selectedMainCategory: null, // 选中的主分类\r\n      selectedSubCategory: null, // 选中的子分类\r\n      hasCategoryFilter: false, // 是否有分类筛选\r\n      showDetailDialog: false, // 是否显示商品详情弹窗\r\n      selectedProduct: {} // 选中的商品\r\n    };\r\n  },\r\n\r\n  methods: {\r\n    // 处理分类变化\r\n    handleCategoryChange(category) {\r\n      this.selectedMainCategory = category.mainCategory\r\n      this.selectedSubCategory = category.subCategory\r\n      this.hasCategoryFilter = category.mainCategory !== null || category.subCategory !== null\r\n      this.fetchRecommendedGoods()\r\n    },\r\n\r\n    // 图片加载错误处理\r\n    handleImageError(event) {\r\n      console.error('首页商品图片加载失败:', event.target.src);\r\n      event.target.style.backgroundColor = '#f0f0f0';\r\n      event.target.style.border = '1px solid #ccc';\r\n    },\r\n\r\n    // 图片加载成功处理\r\n    handleImageLoad(event) {\r\n      console.log('首页商品图片加载成功:', event.target.src);\r\n    },\r\n\r\n    // 获取推荐商品\r\n    async fetchRecommendedGoods() {\r\n      try {\r\n        this.loading = true;\r\n        let params = {};\r\n        \r\n        // 如果有分类筛选，添加分类参数\r\n        if (this.hasCategoryFilter) {\r\n          params = {\r\n            mainCategory: this.selectedMainCategory,\r\n            subCategory: this.selectedSubCategory\r\n          };\r\n        }\r\n        \r\n        const response = await request.get('/good', params);\r\n\r\n        if (Array.isArray(response) && response.length > 0) {\r\n          // 使用后端数据，并处理图片URL和确保描述存在\r\n          this.recommendedGoods = response.slice(0, 8).map((item, index) => {\r\n            // 根据商品名称匹配对应的图片\r\n            let imgUrl;\r\n            switch(item.name) {\r\n              case '女上衣':\r\n                imgUrl = require('@/assets/女上衣.png');\r\n                break;\r\n              case '休闲鞋':\r\n                imgUrl = require('@/assets/休闲鞋.png');\r\n                break;\r\n              case '威士忌 大瓶':\r\n                imgUrl = require('@/assets/威士忌 大瓶.png');\r\n                break;\r\n              case '墨镜':\r\n                imgUrl = require('@/assets/墨镜.png');\r\n                break;\r\n              case '桌椅套装':\r\n                imgUrl = require('@/assets/桌椅套装.png');\r\n                break;\r\n              case '儿童简笔画册':\r\n                imgUrl = require('@/assets/儿童简笔画册.png');\r\n                break;\r\n              case '英文版图书':\r\n                imgUrl = require('@/assets/英文版图书.png');\r\n                break;\r\n              case '衬衫':\r\n                imgUrl = require('@/assets/衬衫.png');\r\n                break;\r\n              default: {\r\n                // 默认图片，根据索引循环使用\r\n                const defaultImages = [\r\n                  require('@/assets/女上衣.png'),\r\n                  require('@/assets/休闲鞋.png'),\r\n                  require('@/assets/威士忌 大瓶.png'),\r\n                  require('@/assets/墨镜.png'),\r\n                  require('@/assets/桌椅套装.png')\r\n                ];\r\n                imgUrl = defaultImages[index % defaultImages.length];\r\n                break;\r\n              }\r\n            }\r\n            return {\r\n              ...item,\r\n              imgs: imgUrl,\r\n              description: item.description || this.getDefaultDescription(item.name), // 使用默认描述，优先使用后端数据\r\n              price: item.price || (Math.random() * 200 + 50).toFixed(2) // 确保有价格\r\n            };\r\n          });\r\n          console.log('首页使用后端商品数据，添加图片:', this.recommendedGoods);\r\n        } else {\r\n          // 如果后端没有数据，使用本地图片的模拟数据\r\n          this.recommendedGoods = this.getMockProductsWithLocalImages();\r\n          console.log('首页使用本地图片的模拟商品数据:', this.recommendedGoods);\r\n        }\r\n      } catch (error) {\r\n        console.error('获取推荐商品失败:', error);\r\n        // 使用本地图片的模拟数据作为后备\r\n        this.recommendedGoods = this.getMockProductsWithLocalImages();\r\n        console.log('首页API请求失败，使用本地图片的模拟数据:', this.recommendedGoods);\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n\r\n    // 获取使用本地图片的模拟商品数据\r\n    getMockProductsWithLocalImages() {\r\n      return [\r\n        {\r\n          id: 1,\r\n          name: '女上衣',\r\n          description: '时尚女性上衣，舒适面料，多种颜色可选',\r\n          imgs: require('@/assets/女上衣.png'),\r\n          price: 102.00,\r\n          discount: 0.85,\r\n          sales: 120,\r\n          recommend: true\r\n        },\r\n        {\r\n          id: 2,\r\n          name: '休闲鞋',\r\n          description: '舒适透气的休闲运动鞋，适合日常穿着',\r\n          imgs: require('@/assets/休闲鞋.png'),\r\n          price: 162.00,\r\n          discount: 0.90,\r\n          sales: 85,\r\n          recommend: true\r\n        },\r\n        {\r\n          id: 3,\r\n          name: '威士忌 大瓶',\r\n          description: '优质威士忌，口感醇厚，适合收藏和品鉴',\r\n          imgs: require('@/assets/威士忌 大瓶.png'),\r\n          price: 427.50,\r\n          discount: 0.95,\r\n          sales: 45,\r\n          recommend: true\r\n        },\r\n        {\r\n          id: 4,\r\n          name: '墨镜',\r\n          description: '时尚墨镜，防紫外线，多种款式可选',\r\n          imgs: require('@/assets/墨镜.png'),\r\n          price: 199.00,\r\n          discount: 0.80,\r\n          sales: 65,\r\n          recommend: true\r\n        },\r\n        {\r\n          id: 5,\r\n          name: '桌椅套装',\r\n          description: '舒适桌椅套装，适合家庭使用',\r\n          imgs: require('@/assets/桌椅套装.png'),\r\n          price: 1299.00,\r\n          discount: 0.85,\r\n          sales: 30,\r\n          recommend: true\r\n        }\r\n      ];\r\n    },\r\n\r\n    // 处理轮播图点击\r\n    handleCarouselClick(item) {\r\n      console.log('点击轮播图商品:', item);\r\n      this.$message.success(`查看商品: ${item.goodName}`);\r\n      // 可以跳转到商品详情页\r\n    },\r\n\r\n    // 处理搜索\r\n    handleSearch(keyword) {\r\n      console.log('搜索:', keyword);\r\n      // 跳转到商品页面并传递搜索参数\r\n      this.$router.push({\r\n        path: '/goods',\r\n        query: { search: keyword }\r\n      });\r\n    },\r\n\r\n    // 处理搜索结果\r\n    handleSearchResults(results) {\r\n      console.log('搜索结果:', results);\r\n    },\r\n\r\n    // 清除搜索\r\n    handleClearSearch() {\r\n      console.log('清除搜索');\r\n    },\r\n\r\n    // 添加到购物车\r\n    addToCart(good) {\r\n      console.log('加入购物车:', good.name);\r\n      this.$message.success(`已加入购物车：${good.name}`);\r\n    },\r\n\r\n    // 显示商品详情\r\n    showProductDetail(good) {\r\n      this.selectedProduct = good;\r\n      this.showDetailDialog = true;\r\n      console.log('查看商品详情:', good);\r\n    },\r\n\r\n    // 立即购买\r\n    buyNow(good) {\r\n      console.log('立即购买:', good.name);\r\n      this.$message.info(`立即购买：${good.name}`);\r\n    },\r\n\r\n    // 跳转到商品页面\r\n    goToGoodsPage() {\r\n      // 如果有分类筛选，传递分类参数到商品页面\r\n      if (this.hasCategoryFilter) {\r\n        this.$router.push({\r\n          path: '/goods',\r\n          query: {\r\n            mainCategory: this.selectedMainCategory,\r\n            subCategory: this.selectedSubCategory\r\n          }\r\n        });\r\n      } else {\r\n        this.$router.push('/goods');\r\n      }\r\n    },\r\n    \r\n    // 清除分类筛选\r\n    clearCategoryFilter() {\r\n      // 触发Category组件的clearCategoryFilter方法\r\n      const categoryComponent = this.$refs.categoryComponent;\r\n      if (categoryComponent && typeof categoryComponent.clearCategoryFilter === 'function') {\r\n        categoryComponent.clearCategoryFilter();\r\n      }\r\n    }\r\n  },\r\n\r\n  created() {\r\n    this.fetchRecommendedGoods();\r\n  },\r\n\r\n  mounted() {\r\n    this.fetchRecommendedGoods();\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.home-container {\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  padding: 20px 16px;\r\n}\r\n\r\n.welcome-banner {\r\n  text-align: center;\r\n  margin-bottom: 40px;\r\n  padding: 40px 20px;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border-radius: 16px;\r\n  color: white;\r\n}\r\n\r\n.welcome-title {\r\n  font-size: 36px;\r\n  margin: 0 0 10px;\r\n  font-weight: bold;\r\n}\r\n\r\n.welcome-subtitle {\r\n  font-size: 18px;\r\n  margin: 0;\r\n  opacity: 0.9;\r\n}\r\n\r\n.search-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.search-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 15px;\r\n  margin-bottom: 30px;\r\n  padding: 15px;\r\n  background: #f8f9fa;\r\n  border-radius: 8px;\r\n  border-left: 4px solid #67c23a;\r\n}\r\n\r\n.search-count {\r\n  color: #666;\r\n  font-size: 14px;\r\n}\r\n\r\n.recommended-section {\r\n  margin-top: 50px;\r\n}\r\n\r\n.section-title {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  font-size: 24px;\r\n  color: #333;\r\n  margin-bottom: 30px;\r\n  padding-bottom: 10px;\r\n  border-bottom: 2px solid #409eff;\r\n}\r\n\r\n.loading {\r\n  padding: 40px 0;\r\n  /* 添加最小高度，避免布局跳动 */\r\n  min-height: 300px;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n.goods-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\r\n  gap: 20px;\r\n  margin-bottom: 40px;\r\n  /* 添加最小高度，避免布局跳动 */\r\n  min-height: 300px;\r\n  /* 添加过渡效果 */\r\n  transition: all 0.3s ease-in-out;\r\n}\r\n\r\n.goods-card {\r\n  border: 1px solid #e0e0e0;\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  background: white;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  transition: all 0.3s ease;\r\n  cursor: pointer;\r\n  /* 添加动画效果 */\r\n  animation: fadeIn 0.6s ease-out;\r\n  /* 添加will-change优化性能 */\r\n  will-change: transform, opacity;\r\n}\r\n\r\n@keyframes fadeIn {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(20px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n.goods-card:hover {\r\n  transform: translateY(-5px);\r\n  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);\r\n  border-color: #409eff;\r\n}\r\n\r\n.goods-image {\r\n  width: 100%;\r\n  height: 160px;\r\n  object-fit: cover;\r\n}\r\n\r\n.goods-info {\r\n  padding: 16px;\r\n}\r\n\r\n.goods-name {\r\n  font-size: 16px;\r\n  color: #333;\r\n  margin: 0 0 8px;\r\n  font-weight: bold;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n  overflow: hidden;\r\n}\r\n\r\n.goods-price {\r\n  color: #e60000;\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n  margin: 10px 0 15px;\r\n}\r\n\r\n.more-goods {\r\n  text-align: center;\r\n  margin-top: 40px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .home-container {\r\n    padding: 15px 12px;\r\n  }\r\n\r\n  .welcome-banner {\r\n    padding: 30px 15px;\r\n    margin-bottom: 30px;\r\n  }\r\n\r\n  .welcome-title {\r\n    font-size: 28px;\r\n  }\r\n\r\n  .welcome-subtitle {\r\n    font-size: 16px;\r\n  }\r\n\r\n  .goods-grid {\r\n    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\r\n    gap: 15px;\r\n  }\r\n\r\n  .section-title {\r\n    font-size: 20px;\r\n  }\r\n}\r\n</style>"], "mappings": ";;EACOA,KAAK,EAAC;AAAgB;;EAWpBA,KAAK,EAAC;AAAgB;;EAStBA,KAAK,EAAC;AAAqB;;EAC1BA,KAAK,EAAC;AAAe;;;EAMLA,KAAK,EAAC;;;EAKdA,KAAK,EAAC;AAAY;;;;EAcrBA,KAAK,EAAC;AAAY;;EACjBA,KAAK,EAAC;AAAY;;EACnBA,KAAK,EAAC;AAAY;;EAClBA,KAAK,EAAC;AAAa;;EASvBA,KAAK,EAAC;AAAY;;;;;;;;;uBA1D3BC,mBAAA,CAwEM,OAxENC,UAwEM,GAvEJC,mBAAA,UAAa,E,0BACbC,mBAAA,CAGM;IAHDJ,KAAK,EAAC;EAAgB,IACzBI,mBAAA,CAAyC;IAArCJ,KAAK,EAAC;EAAe,GAAC,YAAU,GACpCI,mBAAA,CAA6C;IAA1CJ,KAAK,EAAC;EAAkB,GAAC,eAAa,E,qBAG3CG,mBAAA,WAAc,EACdE,YAAA,CAA2DC,4BAAA;IAAvCC,eAAc,EAAEC,QAAA,CAAAC;EAAmB,8CAEvDN,mBAAA,WAAc,EACdC,mBAAA,CAMM,OANNM,UAMM,GALJL,YAAA,CAIEM,oBAAA;IAHCC,QAAM,EAAEJ,QAAA,CAAAK,YAAY;IACpBC,eAAc,EAAEN,QAAA,CAAAO,mBAAmB;IACnCC,aAAY,EAAER,QAAA,CAAAS;+EAInBd,mBAAA,YAAe,EACfC,mBAAA,CA2CM,OA3CNc,UA2CM,GA1CJd,mBAAA,CAGK,MAHLe,UAGK,GAFHd,YAAA,CAA2Be,kBAAA;sBAAlB,MAAQ,CAARf,YAAA,CAAQgB,eAAA,E;;iDAAU,QAE7B,oB,GAEAlB,mBAAA,UAAa,EACFmB,KAAA,CAAAC,OAAO,I,cAAlBtB,mBAAA,CAEM,OAFNuB,UAEM,GADJnB,YAAA,CAAkCoB,sBAAA;IAApBC,IAAI,EAAE,CAAC;IAAEC,QAAQ,EAAR;yBAIzB1B,mBAAA,CAuBM2B,SAAA;IAAAC,GAAA;EAAA,IAxBN1B,mBAAA,UAAa,EACbC,mBAAA,CAuBM,OAvBN0B,UAuBM,I,kBAtBJ7B,mBAAA,CAqBM2B,SAAA,QAAAG,WAAA,CAnBWT,KAAA,CAAAU,gBAAgB,EAAxBC,IAAI;yBAFbhC,mBAAA,CAqBM;MApBJD,KAAK,EAAC,YAAY;MAEjB6B,GAAG,EAAEI,IAAI,CAACC,EAAE;MACZC,OAAK,EAAAC,MAAA,IAAE5B,QAAA,CAAA6B,iBAAiB,CAACJ,IAAI;QAE9B7B,mBAAA,CAME;MALCkC,GAAG,EAAEL,IAAI,CAACM,IAAI;MACdC,GAAG,EAAEP,IAAI,CAACQ,IAAI;MACfzC,KAAK,EAAC,aAAa;MAClB0C,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEpC,QAAA,CAAAqC,gBAAA,IAAArC,QAAA,CAAAqC,gBAAA,IAAAD,IAAA,CAAgB;MACvBE,MAAI,EAAAH,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEpC,QAAA,CAAAuC,eAAA,IAAAvC,QAAA,CAAAuC,eAAA,IAAAH,IAAA,CAAe;0DAExBxC,mBAAA,CAOM,OAPN4C,UAOM,GANJ5C,mBAAA,CAA2C,MAA3C6C,WAA2C,EAAAC,gBAAA,CAAjBjB,IAAI,CAACQ,IAAI,kBACnCrC,mBAAA,CAA4D,KAA5D+C,WAA4D,EAAAD,gBAAA,CAAnCjB,IAAI,CAACmB,WAAW,8BACzChD,mBAAA,CAA4C,KAA5CiD,WAA4C,EAArB,GAAC,GAAAH,gBAAA,CAAGjB,IAAI,CAACqB,KAAK,kBACrCjD,YAAA,CAEYkD,oBAAA;MAFDC,IAAI,EAAC,SAAS;MAACC,IAAI,EAAC,OAAO;MAAEtB,OAAK,EAAAuB,cAAA,CAAAtB,MAAA,IAAO5B,QAAA,CAAAmD,SAAS,CAAC1B,IAAI;;wBAAG,MAErE,KAAAU,MAAA,QAAAA,MAAA,O,iBAFqE,SAErE,mB;;;sFAKNxC,mBAAA,YAAe,EACfC,mBAAA,CAIM,OAJNwD,WAIM,GAHJvD,YAAA,CAEYkD,oBAAA;IAFDC,IAAI,EAAC,SAAS;IAACC,IAAI,EAAC,OAAO;IAAEtB,OAAK,EAAE3B,QAAA,CAAAqD;;sBAAe,MAE9D,KAAAlB,MAAA,QAAAA,MAAA,O,iBAF8D,UAE9D,mB;;sCAIJxC,mBAAA,YAAe,EACfE,YAAA,CAKEyD,wBAAA;gBAJSxC,KAAA,CAAAyC,gBAAgB;+DAAhBzC,KAAA,CAAAyC,gBAAgB,GAAA3B,MAAA;IACxB4B,OAAO,EAAE1C,KAAA,CAAA2C,eAAe;IACxBC,WAAW,EAAE1D,QAAA,CAAAmD,SAAS;IACtBQ,QAAO,EAAE3D,QAAA,CAAA4D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}