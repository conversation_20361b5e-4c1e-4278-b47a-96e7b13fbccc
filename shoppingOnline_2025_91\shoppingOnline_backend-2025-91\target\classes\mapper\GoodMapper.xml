<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cn.mapper.GoodMapper">
    <insert id="insertGood" useGeneratedKeys="true" keyProperty="id">
        insert into good(name, description, discount, category_id, imgs, create_time) value (#{good.name},#{good.description},#{good.discount},#{good.categoryId},#{good.imgs},#{good.createTime})
    </insert>
    <update id="saleGood">
        update `good` set sales = sales + #{count} ,sale_money = sale_money + #{money} where good.id = #{id}
    </update>



    <select id="findFrontGoods"  resultType="com.cn.entity.dto.GoodDTO" >
        SELECT
            good.*, MIN(good_standard.price) * discount AS price
        FROM
            `good`
                LEFT JOIN good_standard ON good.id = good_standard.good_id
        WHERE
            is_delete = 0
          AND recommend = 1
        GROUP BY
            id
        ORDER BY
            price ASC
    </select>


</mapper>
