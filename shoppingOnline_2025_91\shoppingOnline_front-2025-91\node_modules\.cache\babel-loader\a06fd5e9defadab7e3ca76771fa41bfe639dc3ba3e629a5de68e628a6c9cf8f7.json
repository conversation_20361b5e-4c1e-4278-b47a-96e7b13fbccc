{"ast": null, "code": "import CarouselComponent from '@/components/Carousel.vue';\nimport SearchBox from '@/components/SearchBox.vue';\nimport request from '@/utils/request.js';\nexport default {\n  name: 'TestPage',\n  components: {\n    CarouselComponent,\n    SearchBox\n  },\n  data() {\n    return {\n      searchResults: [],\n      testResults: null\n    };\n  },\n  methods: {\n    handleCarouselClick(item) {\n      console.log('轮播图点击:', item);\n      this.$message.success(`点击了轮播图商品: ${item.goodName}`);\n    },\n    handleSearch(keyword) {\n      console.log('搜索关键词:', keyword);\n      this.$message.info(`搜索: ${keyword}`);\n    },\n    handleSearchResults(results) {\n      console.log('搜索结果:', results);\n      this.searchResults = results.records || [];\n    },\n    handleClearSearch() {\n      console.log('清除搜索');\n      this.searchResults = [];\n    },\n    async testCarouselAPI() {\n      try {\n        const response = await request.get('/carousel');\n        this.testResults = JSON.stringify(response, null, 2);\n        this.$message.success('轮播图API测试成功');\n      } catch (error) {\n        this.testResults = `轮播图API错误: ${error.message}`;\n        this.$message.error('轮播图API测试失败');\n      }\n    },\n    async testGoodsAPI() {\n      try {\n        const response = await request.get('/good');\n        this.testResults = JSON.stringify(response, null, 2);\n        this.$message.success('商品API测试成功');\n      } catch (error) {\n        this.testResults = `商品API错误: ${error.message}`;\n        this.$message.error('商品API测试失败');\n      }\n    },\n    async testSearchAPI() {\n      try {\n        const response = await request.get('/good/page', {\n          searchText: '手机',\n          pageNum: 1,\n          pageSize: 5\n        });\n        this.testResults = JSON.stringify(response, null, 2);\n        this.$message.success('搜索API测试成功');\n      } catch (error) {\n        this.testResults = `搜索API错误: ${error.message}`;\n        this.$message.error('搜索API测试失败');\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["CarouselComponent", "SearchBox", "request", "name", "components", "data", "searchResults", "testResults", "methods", "handleCarouselClick", "item", "console", "log", "$message", "success", "<PERSON><PERSON><PERSON>", "handleSearch", "keyword", "info", "handleSearchResults", "results", "records", "handleClearSearch", "testCarouselAPI", "response", "get", "JSON", "stringify", "error", "message", "testGoodsAPI", "testSearchAPI", "searchText", "pageNum", "pageSize"], "sources": ["D:\\2025_down\\project\\shoppingOnline_2025_91\\shoppingOnline_front-2025-91\\src\\views\\TestPage.vue"], "sourcesContent": ["<template>\n  <div class=\"test-page\">\n    <h2>功能测试页面</h2>\n    \n    <!-- 测试轮播图组件 -->\n    <div class=\"test-section\">\n      <h3>轮播图组件测试</h3>\n      <CarouselComponent @carousel-click=\"handleCarouselClick\" />\n    </div>\n    \n    <!-- 测试搜索框组件 -->\n    <div class=\"test-section\">\n      <h3>搜索框组件测试</h3>\n      <SearchBox \n        @search=\"handleSearch\" \n        @search-results=\"handleSearchResults\"\n        @clear-search=\"handleClearSearch\"\n      />\n      \n      <!-- 搜索结果显示 -->\n      <div v-if=\"searchResults.length > 0\" class=\"search-results\">\n        <h4>搜索结果：</h4>\n        <div class=\"results-list\">\n          <div v-for=\"item in searchResults\" :key=\"item.id\" class=\"result-item\">\n            <img :src=\"item.imgs\" :alt=\"item.name\" class=\"result-image\" />\n            <div class=\"result-info\">\n              <h5>{{ item.name }}</h5>\n              <p>¥{{ item.price }}</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n    \n    <!-- API测试按钮 -->\n    <div class=\"test-section\">\n      <h3>API测试</h3>\n      <el-button @click=\"testCarouselAPI\" type=\"primary\">测试轮播图API</el-button>\n      <el-button @click=\"testGoodsAPI\" type=\"success\">测试商品API</el-button>\n      <el-button @click=\"testSearchAPI\" type=\"warning\">测试搜索API</el-button>\n    </div>\n    \n    <!-- 测试结果显示 -->\n    <div class=\"test-section\" v-if=\"testResults\">\n      <h3>测试结果</h3>\n      <pre>{{ testResults }}</pre>\n    </div>\n  </div>\n</template>\n\n<script>\nimport CarouselComponent from '@/components/Carousel.vue'\nimport SearchBox from '@/components/SearchBox.vue'\nimport request from '@/utils/request.js'\n\nexport default {\n  name: 'TestPage',\n  \n  components: {\n    CarouselComponent,\n    SearchBox\n  },\n  \n  data() {\n    return {\n      searchResults: [],\n      testResults: null\n    }\n  },\n  \n  methods: {\n    handleCarouselClick(item) {\n      console.log('轮播图点击:', item)\n      this.$message.success(`点击了轮播图商品: ${item.goodName}`)\n    },\n    \n    handleSearch(keyword) {\n      console.log('搜索关键词:', keyword)\n      this.$message.info(`搜索: ${keyword}`)\n    },\n    \n    handleSearchResults(results) {\n      console.log('搜索结果:', results)\n      this.searchResults = results.records || []\n    },\n    \n    handleClearSearch() {\n      console.log('清除搜索')\n      this.searchResults = []\n    },\n    \n    async testCarouselAPI() {\n      try {\n        const response = await request.get('/carousel')\n        this.testResults = JSON.stringify(response, null, 2)\n        this.$message.success('轮播图API测试成功')\n      } catch (error) {\n        this.testResults = `轮播图API错误: ${error.message}`\n        this.$message.error('轮播图API测试失败')\n      }\n    },\n    \n    async testGoodsAPI() {\n      try {\n        const response = await request.get('/good')\n        this.testResults = JSON.stringify(response, null, 2)\n        this.$message.success('商品API测试成功')\n      } catch (error) {\n        this.testResults = `商品API错误: ${error.message}`\n        this.$message.error('商品API测试失败')\n      }\n    },\n    \n    async testSearchAPI() {\n      try {\n        const response = await request.get('/good/page', {\n          searchText: '手机',\n          pageNum: 1,\n          pageSize: 5\n        })\n        this.testResults = JSON.stringify(response, null, 2)\n        this.$message.success('搜索API测试成功')\n      } catch (error) {\n        this.testResults = `搜索API错误: ${error.message}`\n        this.$message.error('搜索API测试失败')\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.test-page {\n  max-width: 1000px;\n  margin: 20px auto;\n  padding: 20px;\n}\n\n.test-section {\n  margin-bottom: 40px;\n  padding: 20px;\n  border: 1px solid #e0e0e0;\n  border-radius: 8px;\n  background: white;\n}\n\n.test-section h3 {\n  margin-top: 0;\n  color: #333;\n  border-bottom: 2px solid #409eff;\n  padding-bottom: 10px;\n}\n\n.search-results {\n  margin-top: 20px;\n}\n\n.results-list {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\n  gap: 15px;\n  margin-top: 15px;\n}\n\n.result-item {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  padding: 10px;\n  border: 1px solid #e0e0e0;\n  border-radius: 8px;\n}\n\n.result-image {\n  width: 50px;\n  height: 50px;\n  object-fit: cover;\n  border-radius: 4px;\n}\n\n.result-info h5 {\n  margin: 0 0 5px;\n  font-size: 14px;\n}\n\n.result-info p {\n  margin: 0;\n  color: #e60000;\n  font-weight: bold;\n}\n\npre {\n  background: #f5f5f5;\n  padding: 15px;\n  border-radius: 4px;\n  overflow-x: auto;\n  font-size: 12px;\n}\n</style>\n"], "mappings": "AAmDA,OAAOA,iBAAgB,MAAO,2BAA0B;AACxD,OAAOC,SAAQ,MAAO,4BAA2B;AACjD,OAAOC,OAAM,MAAO,oBAAmB;AAEvC,eAAe;EACbC,IAAI,EAAE,UAAU;EAEhBC,UAAU,EAAE;IACVJ,iBAAiB;IACjBC;EACF,CAAC;EAEDI,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,aAAa,EAAE,EAAE;MACjBC,WAAW,EAAE;IACf;EACF,CAAC;EAEDC,OAAO,EAAE;IACPC,mBAAmBA,CAACC,IAAI,EAAE;MACxBC,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEF,IAAI;MAC1B,IAAI,CAACG,QAAQ,CAACC,OAAO,CAAC,aAAaJ,IAAI,CAACK,QAAQ,EAAE;IACpD,CAAC;IAEDC,YAAYA,CAACC,OAAO,EAAE;MACpBN,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEK,OAAO;MAC7B,IAAI,CAACJ,QAAQ,CAACK,IAAI,CAAC,OAAOD,OAAO,EAAE;IACrC,CAAC;IAEDE,mBAAmBA,CAACC,OAAO,EAAE;MAC3BT,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEQ,OAAO;MAC5B,IAAI,CAACd,aAAY,GAAIc,OAAO,CAACC,OAAM,IAAK,EAAC;IAC3C,CAAC;IAEDC,iBAAiBA,CAAA,EAAG;MAClBX,OAAO,CAACC,GAAG,CAAC,MAAM;MAClB,IAAI,CAACN,aAAY,GAAI,EAAC;IACxB,CAAC;IAED,MAAMiB,eAAeA,CAAA,EAAG;MACtB,IAAI;QACF,MAAMC,QAAO,GAAI,MAAMtB,OAAO,CAACuB,GAAG,CAAC,WAAW;QAC9C,IAAI,CAAClB,WAAU,GAAImB,IAAI,CAACC,SAAS,CAACH,QAAQ,EAAE,IAAI,EAAE,CAAC;QACnD,IAAI,CAACX,QAAQ,CAACC,OAAO,CAAC,YAAY;MACpC,EAAE,OAAOc,KAAK,EAAE;QACd,IAAI,CAACrB,WAAU,GAAI,aAAaqB,KAAK,CAACC,OAAO,EAAC;QAC9C,IAAI,CAAChB,QAAQ,CAACe,KAAK,CAAC,YAAY;MAClC;IACF,CAAC;IAED,MAAME,YAAYA,CAAA,EAAG;MACnB,IAAI;QACF,MAAMN,QAAO,GAAI,MAAMtB,OAAO,CAACuB,GAAG,CAAC,OAAO;QAC1C,IAAI,CAAClB,WAAU,GAAImB,IAAI,CAACC,SAAS,CAACH,QAAQ,EAAE,IAAI,EAAE,CAAC;QACnD,IAAI,CAACX,QAAQ,CAACC,OAAO,CAAC,WAAW;MACnC,EAAE,OAAOc,KAAK,EAAE;QACd,IAAI,CAACrB,WAAU,GAAI,YAAYqB,KAAK,CAACC,OAAO,EAAC;QAC7C,IAAI,CAAChB,QAAQ,CAACe,KAAK,CAAC,WAAW;MACjC;IACF,CAAC;IAED,MAAMG,aAAaA,CAAA,EAAG;MACpB,IAAI;QACF,MAAMP,QAAO,GAAI,MAAMtB,OAAO,CAACuB,GAAG,CAAC,YAAY,EAAE;UAC/CO,UAAU,EAAE,IAAI;UAChBC,OAAO,EAAE,CAAC;UACVC,QAAQ,EAAE;QACZ,CAAC;QACD,IAAI,CAAC3B,WAAU,GAAImB,IAAI,CAACC,SAAS,CAACH,QAAQ,EAAE,IAAI,EAAE,CAAC;QACnD,IAAI,CAACX,QAAQ,CAACC,OAAO,CAAC,WAAW;MACnC,EAAE,OAAOc,KAAK,EAAE;QACd,IAAI,CAACrB,WAAU,GAAI,YAAYqB,KAAK,CAACC,OAAO,EAAC;QAC7C,IAAI,CAAChB,QAAQ,CAACe,KAAK,CAAC,WAAW;MACjC;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}