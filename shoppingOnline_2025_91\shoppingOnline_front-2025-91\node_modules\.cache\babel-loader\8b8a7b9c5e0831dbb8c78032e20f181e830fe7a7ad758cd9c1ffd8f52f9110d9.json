{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, openBlock as _openBlock, createBlock as _createBlock, createVNode as _createVNode, normalizeClass as _normalizeClass, renderList as _renderList, Fragment as _Fragment, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, withModifiers as _withModifiers } from \"vue\";\nconst _hoisted_1 = {\n  class: \"category-container\"\n};\nconst _hoisted_2 = {\n  class: \"category-section\"\n};\nconst _hoisted_3 = {\n  class: \"category-header\"\n};\nconst _hoisted_4 = {\n  class: \"category-list\"\n};\nconst _hoisted_5 = {\n  key: 0,\n  class: \"current-category\"\n};\nconst _hoisted_6 = {\n  key: 1,\n  class: \"loading\"\n};\nconst _hoisted_7 = {\n  class: \"goods-section\"\n};\nconst _hoisted_8 = {\n  class: \"goods-header\"\n};\nconst _hoisted_9 = [\"onClick\"];\nconst _hoisted_10 = [\"src\", \"alt\"];\nconst _hoisted_11 = {\n  class: \"goods-info\"\n};\nconst _hoisted_12 = {\n  class: \"goods-name\"\n};\nconst _hoisted_13 = {\n  class: \"goods-desc\"\n};\nconst _hoisted_14 = {\n  class: \"goods-price\"\n};\nconst _hoisted_15 = {\n  class: \"goods-actions\"\n};\nconst _hoisted_16 = {\n  class: \"no-data\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_Grid = _resolveComponent(\"Grid\");\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_Box = _resolveComponent(\"Box\");\n  const _component_el_card = _resolveComponent(\"el-card\");\n  const _component_el_alert = _resolveComponent(\"el-alert\");\n  const _component_el_skeleton = _resolveComponent(\"el-skeleton\");\n  const _component_el_empty = _resolveComponent(\"el-empty\");\n  const _component_ProductDetail = _resolveComponent(\"ProductDetail\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" 页面标题 \"), _cache[8] || (_cache[8] = _createElementVNode(\"h1\", {\n    class: \"page-title\"\n  }, \"商品分类\", -1 /* CACHED */)), _createCommentVNode(\" 分类列表 \"), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_card, {\n    class: \"category-card\"\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_3, [_cache[4] || (_cache[4] = _createElementVNode(\"h2\", null, \"选择分类\", -1 /* CACHED */)), $data.selectedCategory ? (_openBlock(), _createBlock(_component_el_button, {\n      key: 0,\n      type: \"info\",\n      size: \"small\",\n      onClick: $options.clearCategory\n    }, {\n      default: _withCtx(() => [...(_cache[3] || (_cache[3] = [_createTextVNode(\" 清除筛选 \", -1 /* CACHED */)]))]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"onClick\"])) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_4, [_createVNode(_component_el_button, {\n      class: _normalizeClass(['category-btn', {\n        'active': $data.selectedCategory === null\n      }]),\n      onClick: _cache[0] || (_cache[0] = $event => $options.selectCategory(null)),\n      size: \"large\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode(_component_Grid)]),\n        _: 1 /* STABLE */\n      }), _cache[5] || (_cache[5] = _createTextVNode(\" 全部商品 \", -1 /* CACHED */))]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"class\"]), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.categories, category => {\n      return _openBlock(), _createBlock(_component_el_button, {\n        key: category.id,\n        class: _normalizeClass(['category-btn', {\n          'active': $data.selectedCategory === category.id\n        }]),\n        onClick: $event => $options.selectCategory(category),\n        size: \"large\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n          default: _withCtx(() => [_createVNode(_component_Box)]),\n          _: 1 /* STABLE */\n        }), _createTextVNode(\" \" + _toDisplayString(category.name), 1 /* TEXT */)]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"class\", \"onClick\"]);\n    }), 128 /* KEYED_FRAGMENT */))])]),\n    _: 1 /* STABLE */\n  })]), _createCommentVNode(\" 当前分类信息 \"), $data.selectedCategory ? (_openBlock(), _createElementBlock(\"div\", _hoisted_5, [_createVNode(_component_el_alert, {\n    title: `当前分类：${$data.selectedCategoryName}`,\n    type: \"info\",\n    closable: false,\n    \"show-icon\": \"\"\n  }, null, 8 /* PROPS */, [\"title\"])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 加载状态 \"), $data.loading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_6, [_createVNode(_component_el_skeleton, {\n    rows: 3,\n    animated: \"\"\n  })])) : $data.goodsList.length > 0 ? (_openBlock(), _createElementBlock(_Fragment, {\n    key: 2\n  }, [_createCommentVNode(\" 商品列表 \"), _createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"div\", _hoisted_8, [_createElementVNode(\"h3\", null, _toDisplayString($data.selectedCategoryName || '全部') + \"商品 (\" + _toDisplayString($data.goodsList.length) + \")\", 1 /* TEXT */), _createVNode(_component_el_button, {\n    type: \"primary\",\n    onClick: $options.goToGoodsPage\n  }, {\n    default: _withCtx(() => [...(_cache[6] || (_cache[6] = [_createTextVNode(\"查看更多\", -1 /* CACHED */)]))]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClick\"])]), _createElementVNode(\"div\", {\n    class: _normalizeClass([\"goods-list\", {\n      'loading-transition': $data.loading\n    }])\n  }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.goodsList, good => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      class: \"goods-card\",\n      key: good.id,\n      onClick: $event => $options.showProductDetail(good)\n    }, [_createElementVNode(\"img\", {\n      src: good.imgs,\n      alt: good.name,\n      class: \"goods-image\",\n      onError: _cache[1] || (_cache[1] = (...args) => $options.handleImageError && $options.handleImageError(...args))\n    }, null, 40 /* PROPS, NEED_HYDRATION */, _hoisted_10), _createElementVNode(\"div\", _hoisted_11, [_createElementVNode(\"h4\", _hoisted_12, _toDisplayString(good.name), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_13, _toDisplayString(good.description || '暂无商品描述'), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_14, \"¥\" + _toDisplayString(good.price), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_15, [_createVNode(_component_el_button, {\n      type: \"primary\",\n      size: \"small\",\n      onClick: _withModifiers($event => $options.addToCart(good), [\"stop\"])\n    }, {\n      default: _withCtx(() => [...(_cache[7] || (_cache[7] = [_createTextVNode(\" 加入购物车 \", -1 /* CACHED */)]))]),\n      _: 2 /* DYNAMIC */\n    }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])])])], 8 /* PROPS */, _hoisted_9);\n  }), 128 /* KEYED_FRAGMENT */))], 2 /* CLASS */)])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)) : (_openBlock(), _createElementBlock(_Fragment, {\n    key: 3\n  }, [_createCommentVNode(\" 无数据 \"), _createElementVNode(\"div\", _hoisted_16, [_createVNode(_component_el_empty, {\n    description: \"该分类暂无商品\"\n  })])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)), _createCommentVNode(\" 商品详情弹窗 \"), _createVNode(_component_ProductDetail, {\n    modelValue: $data.showDetailDialog,\n    \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $data.showDetailDialog = $event),\n    product: $data.selectedProduct,\n    onAddToCart: $options.addToCart,\n    onBuyNow: $options.buyNow\n  }, null, 8 /* PROPS */, [\"modelValue\", \"product\", \"onAddToCart\", \"onBuyNow\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "_hoisted_2", "_createVNode", "_component_el_card", "_hoisted_3", "$data", "selectedCate<PERSON><PERSON>", "_createBlock", "_component_el_button", "type", "size", "onClick", "$options", "clearCategory", "_cache", "_hoisted_4", "_normalizeClass", "$event", "selectCategory", "_component_el_icon", "_component_Grid", "_Fragment", "_renderList", "categories", "category", "key", "id", "_component_Box", "_toDisplayString", "name", "_hoisted_5", "_component_el_alert", "title", "selectedCategoryName", "closable", "loading", "_hoisted_6", "_component_el_skeleton", "rows", "animated", "goodsList", "length", "_hoisted_7", "_hoisted_8", "goToGoodsPage", "good", "showProductDetail", "src", "imgs", "alt", "onError", "args", "handleImageError", "_hoisted_11", "_hoisted_12", "_hoisted_13", "description", "_hoisted_14", "price", "_hoisted_15", "_withModifiers", "addToCart", "_hoisted_16", "_component_el_empty", "_component_ProductDetail", "showDetailDialog", "product", "selectedProduct", "onAddToCart", "onBuyNow", "buyNow"], "sources": ["D:\\2025_down\\project\\shoppingOnline_2025_91\\shoppingOnline_front-2025-91\\src\\views\\Category.vue"], "sourcesContent": ["<template>\n  <div class=\"category-container\">\n    <!-- 页面标题 -->\n    <h1 class=\"page-title\">商品分类</h1>\n\n    <!-- 分类列表 -->\n    <div class=\"category-section\">\n      <el-card class=\"category-card\">\n        <div class=\"category-header\">\n          <h2>选择分类</h2>\n          <el-button \n            v-if=\"selectedCategory\" \n            type=\"info\" \n            size=\"small\" \n            @click=\"clearCategory\"\n          >\n            清除筛选\n          </el-button>\n        </div>\n        \n        <div class=\"category-list\">\n          <el-button\n            :class=\"['category-btn', { 'active': selectedCategory === null }]\"\n            @click=\"selectCategory(null)\"\n            size=\"large\"\n          >\n            <el-icon><Grid /></el-icon>\n            全部商品\n          </el-button>\n          \n          <el-button\n            v-for=\"category in categories\"\n            :key=\"category.id\"\n            :class=\"['category-btn', { 'active': selectedCategory === category.id }]\"\n            @click=\"selectCategory(category)\"\n            size=\"large\"\n          >\n            <el-icon><Box /></el-icon>\n            {{ category.name }}\n          </el-button>\n        </div>\n      </el-card>\n    </div>\n\n    <!-- 当前分类信息 -->\n    <div v-if=\"selectedCategory\" class=\"current-category\">\n      <el-alert\n        :title=\"`当前分类：${selectedCategoryName}`\"\n        type=\"info\"\n        :closable=\"false\"\n        show-icon\n      />\n    </div>\n\n    <!-- 加载状态 -->\n    <div v-if=\"loading\" class=\"loading\">\n      <el-skeleton :rows=\"3\" animated />\n    </div>\n\n    <!-- 商品列表 -->\n    <div v-else-if=\"goodsList.length > 0\" class=\"goods-section\">\n      <div class=\"goods-header\">\n        <h3>{{ selectedCategoryName || '全部' }}商品 ({{ goodsList.length }})</h3>\n        <el-button type=\"primary\" @click=\"goToGoodsPage\">查看更多</el-button>\n      </div>\n      \n      <div class=\"goods-list\" :class=\"{ 'loading-transition': loading }\">\n        <div\n          class=\"goods-card\"\n          v-for=\"good in goodsList\"\n          :key=\"good.id\"\n          @click=\"showProductDetail(good)\"\n        >\n          <img\n            :src=\"good.imgs\"\n            :alt=\"good.name\"\n            class=\"goods-image\"\n            @error=\"handleImageError\"\n          />\n          <div class=\"goods-info\">\n            <h4 class=\"goods-name\">{{ good.name }}</h4>\n            <p class=\"goods-desc\">{{ good.description || '暂无商品描述' }}</p>\n            <p class=\"goods-price\">¥{{ good.price }}</p>\n            <div class=\"goods-actions\">\n              <el-button \n                type=\"primary\" \n                size=\"small\" \n                @click.stop=\"addToCart(good)\"\n              >\n                加入购物车\n              </el-button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 无数据 -->\n    <div v-else class=\"no-data\">\n      <el-empty description=\"该分类暂无商品\" />\n    </div>\n\n    <!-- 商品详情弹窗 -->\n    <ProductDetail\n      v-model=\"showDetailDialog\"\n      :product=\"selectedProduct\"\n      @add-to-cart=\"addToCart\"\n      @buy-now=\"buyNow\"\n    />\n  </div>\n</template>\n\n<script>\nimport { Grid, Box } from '@element-plus/icons-vue'\nimport request from '@/utils/request.js'\nimport ProductDetail from '@/components/ProductDetail.vue'\n\nexport default {\n  name: 'CategoryView',\n  \n  components: {\n    Grid,\n    Box,\n    ProductDetail\n  },\n  \n  data() {\n    return {\n      loading: false,\n      categories: [],\n      selectedCategory: null,\n      selectedCategoryName: '',\n      goodsList: [],\n      showDetailDialog: false,\n      selectedProduct: {}\n    }\n  },\n  \n  created() {\n    // 先获取分类数据，然后再获取商品数据，避免同时进行多个异步操作导致的闪烁\n    this.fetchCategories().then(() => {\n      this.fetchGoods()\n    })\n  },\n  \n  methods: {\n    // 获取分类数据\n    async fetchCategories() {\n      try {\n        const response = await request.get('/categoryAPI')\n        \n        if (Array.isArray(response)) {\n          this.categories = response\n          console.log('获取分类数据成功:', this.categories)\n        } else {\n          // 使用默认分类\n          this.categories = this.getDefaultCategories()\n        }\n        return Promise.resolve()\n      } catch (error) {\n        console.error('获取分类数据失败:', error)\n        this.categories = this.getDefaultCategories()\n        return Promise.resolve()\n      }\n    },\n    \n    // 获取默认分类数据\n    getDefaultCategories() {\n      return [\n        { id: 1, name: '服装' },\n        { id: 2, name: '鞋类' },\n        { id: 3, name: '酒类' },\n        { id: 4, name: '日用百货' },\n        { id: 5, name: '家用电器' },\n        { id: 6, name: '数码产品' }\n      ]\n    },\n    \n    // 选择分类\n    selectCategory(category) {\n      if (category === null) {\n        this.selectedCategory = null\n        this.selectedCategoryName = ''\n      } else {\n        this.selectedCategory = category.id\n        this.selectedCategoryName = category.name\n      }\n      this.fetchGoods()\n    },\n    \n    // 清除分类筛选\n    clearCategory() {\n      this.selectedCategory = null\n      this.selectedCategoryName = ''\n      this.fetchGoods()\n    },\n    \n    // 获取商品数据\n    async fetchGoods() {\n      try {\n        this.loading = true\n        let params = {}\n        \n        // 如果有分类筛选，添加分类参数\n        if (this.selectedCategory) {\n          params.categoryId = this.selectedCategory\n        }\n        \n        const response = await request.get('/good', { params })\n        \n        if (Array.isArray(response) && response.length > 0) {\n          // 使用后端数据，并为每个商品添加图片和确保描述存在\n          // 先存储数据，然后在nextTick中更新，减少闪烁\n          const processedGoods = response.map((item) => {\n            // 为每个分类提供对应的图片（使用实际存在的图片）\n            const imageMap = {\n              1: '/images/women-top.jpg', // 服装\n              2: '/images/casual-shoes.jpg', // 鞋类\n              3: '/images/whiskey.jpg', // 酒类\n              4: '/images/women-top.jpg', // 日用百货（使用女上衣图片）\n              5: '/images/casual-shoes.jpg', // 家用电器（使用休闲鞋图片）\n              6: '/images/whiskey.jpg' // 数码产品（使用威士忌图片）\n            }\n\n            // 根据商品名称匹配图片\n            let productImage = item.imgs\n            if (!productImage) {\n              // 根据商品名称匹配对应图片\n              if (item.name && item.name.includes('女上衣')) {\n                productImage = '/images/women-top.jpg'\n              } else if (item.name && item.name.includes('休闲鞋')) {\n                productImage = '/images/casual-shoes.jpg'\n              } else if (item.name && item.name.includes('威士忌')) {\n                productImage = '/images/whiskey.jpg'\n              } else {\n                // 使用分类对应的图片，或者循环使用\n                productImage = imageMap[item.categoryId] || imageMap[((index % 3) + 1)]\n              }\n            }\n            return {\n              ...item,\n              imgs: item.imgs || imageMap[item.categoryId] || imageMap[1],\n              description: item.description || this.getDefaultDescription(item.name), // 使用默认描述\n              price: item.price || (Math.random() * 200 + 50).toFixed(2) // 确保有价格\n            }\n          })\n          \n          // 使用nextTick确保DOM更新完成后再显示数据，减少闪烁\n          this.$nextTick(() => {\n            this.goodsList = processedGoods\n            console.log('分类页面获取商品数据成功:', this.goodsList)\n          })\n        } else {\n          // 使用模拟数据\n          const mockGoods = this.getMockProducts()\n          this.$nextTick(() => {\n            this.goodsList = mockGoods\n          })\n        }\n      } catch (error) {\n        console.error('获取商品数据失败:', error)\n        const mockGoods = this.getMockProducts()\n        this.$nextTick(() => {\n          this.goodsList = mockGoods\n        })\n      } finally {\n        // 延迟关闭loading状态，确保数据已经渲染完成\n        setTimeout(() => {\n          this.loading = false\n        }, 100)\n      }\n    },\n    \n    // 获取默认商品描述\n    getDefaultDescription(productName) {\n      const descriptions = {\n        '女上衣': '时尚女性上衣，舒适面料，多种颜色可选，适合日常穿着和商务场合，版型修身显瘦',\n        '休闲鞋': '舒适透气的休闲运动鞋，适合日常穿着，防滑耐磨，多种尺码可选，时尚百搭',\n        '威士忌 大瓶': '优质威士忌，口感醇厚，适合收藏和品鉴，酒精度40%，容量750ml，包装精美',\n        '墨镜': '时尚太阳镜，UV400防护，轻量化设计，多种款式可选',\n        '桌椅套装': '实木桌椅套装，环保材质，结实耐用，适合家庭使用',\n        '儿童简笔画册': '儿童启蒙绘画册，内容丰富，培养孩子创造力和想象力',\n        '英文版图书': '原版英文图书，提高英语阅读能力，内容精彩有趣',\n        '衬衫': '商务休闲衬衫，免烫面料，版型合身，适合各种场合'\n      }\n      return descriptions[productName] || '优质商品，品质保证，欢迎选购'\n    },\n\n    // 获取模拟商品数据\n    getMockProducts() {\n      // 为每个分类添加足够的模拟商品\n      const allProducts = [\n        // 服装分类\n        {\n          id: 1,\n          name: '女上衣',\n          description: '时尚女性上衣，舒适面料，多种颜色可选，适合日常穿着和商务场合',\n          imgs: '/images/women-top.jpg',\n          price: 102.00,\n          categoryId: 1\n        },\n        {\n          id: 8,\n          name: '男士衬衫',\n          description: '优质棉质男士衬衫，修身版型，多种颜色可选，适合商务和休闲场合',\n          imgs: '/images/women-top.jpg',\n          price: 129.00,\n          categoryId: 1\n        },\n        {\n          id: 9,\n          name: '牛仔裤',\n          description: '经典款式牛仔裤，弹力面料，舒适耐穿，多尺码可选',\n          imgs: '/images/women-top.jpg',\n          price: 159.00,\n          categoryId: 1\n        },\n        \n        // 鞋类分类\n        {\n          id: 2,\n          name: '休闲鞋',\n          description: '舒适透气的休闲运动鞋，适合日常穿着，防滑耐磨，多种尺码可选',\n          imgs: '/images/casual-shoes.jpg',\n          price: 162.00,\n          categoryId: 2\n        },\n        {\n          id: 10,\n          name: '正装皮鞋',\n          description: '头层牛皮正装皮鞋，舒适透气，经典款式，适合商务场合',\n          imgs: '/images/casual-shoes.jpg',\n          price: 399.00,\n          categoryId: 2\n        },\n        {\n          id: 11,\n          name: '运动鞋',\n          description: '专业跑步鞋，缓震设计，透气网面，提供舒适的运动体验',\n          imgs: '/images/casual-shoes.jpg',\n          price: 499.00,\n          categoryId: 2\n        },\n        \n        // 酒类分类\n        {\n          id: 3,\n          name: '威士忌 大瓶',\n          description: '优质威士忌，口感醇厚，适合收藏和品鉴，酒精度40%，容量750ml',\n          imgs: '/images/whiskey.jpg',\n          price: 427.50,\n          categoryId: 3\n        },\n        {\n          id: 12,\n          name: '红酒',\n          description: '法国进口红酒，醇厚果香，单宁柔和，适合搭配牛排等红肉',\n          imgs: '/images/whiskey.jpg',\n          price: 298.00,\n          categoryId: 3\n        },\n        {\n          id: 13,\n          name: '啤酒',\n          description: '精酿啤酒，原麦汁浓度高，口感醇厚，泡沫丰富',\n          imgs: '/images/whiskey.jpg',\n          price: 88.00,\n          categoryId: 3\n        },\n        \n        // 日用百货分类\n        {\n          id: 14,\n          name: '毛巾套装',\n          description: '纯棉毛巾套装，柔软吸水，不掉毛，多色可选',\n          imgs: '/images/women-top.jpg',\n          price: 59.90,\n          categoryId: 4\n        },\n        {\n          id: 15,\n          name: '洗发水',\n          description: '去屑控油洗发水，天然成分，温和不刺激，适合各种发质',\n          imgs: '/images/women-top.jpg',\n          price: 89.00,\n          categoryId: 4\n        },\n        {\n          id: 16,\n          name: '牙膏',\n          description: '美白牙膏，含氟配方，有效防蛀，清新口气',\n          imgs: '/images/women-top.jpg',\n          price: 29.90,\n          categoryId: 4\n        },\n        \n        // 家用电器分类\n        {\n          id: 17,\n          name: '电饭煲',\n          description: '智能电饭煲，多种烹饪模式，大容量，不粘内胆',\n          imgs: '/images/women-top.jpg',\n          price: 399.00,\n          categoryId: 5\n        },\n        {\n          id: 18,\n          name: '微波炉',\n          description: '家用微波炉，多种功能，智能控制，操作简便',\n          imgs: '/images/women-top.jpg',\n          price: 599.00,\n          categoryId: 5\n        },\n        {\n          id: 19,\n          name: '电风扇',\n          description: '落地电风扇，静音设计，多档风速，广角送风',\n          imgs: '/images/women-top.jpg',\n          price: 199.00,\n          categoryId: 5\n        },\n        \n        // 数码产品分类\n        {\n          id: 20,\n          name: '智能手机',\n          description: '新款智能手机，全面屏设计，高性能处理器，拍照清晰',\n          imgs: '/images/women-top.jpg',\n          price: 2999.00,\n          categoryId: 6\n        },\n        {\n          id: 21,\n          name: '无线耳机',\n          description: '真无线蓝牙耳机，主动降噪，长续航，音质出色',\n          imgs: '/images/women-top.jpg',\n          price: 799.00,\n          categoryId: 6\n        },\n        {\n          id: 22,\n          name: '智能手表',\n          description: '多功能智能手表，健康监测，运动追踪，防水设计',\n          imgs: '/images/women-top.jpg',\n          price: 1299.00,\n          categoryId: 6\n        }\n      ]\n      \n      // 根据选中的分类筛选商品\n      if (this.selectedCategory) {\n        return allProducts.filter(product => product.categoryId === this.selectedCategory)\n      }\n      \n      return allProducts\n    },\n    \n    // 显示商品详情\n    showProductDetail(good) {\n      this.selectedProduct = good\n      this.showDetailDialog = true\n    },\n    \n    // 添加到购物车\n    addToCart(good) {\n      console.log('加入购物车:', good.name)\n      this.$message.success(`已加入购物车：${good.name}`)\n    },\n    \n    // 立即购买\n    buyNow(good) {\n      console.log('立即购买:', good.name)\n      this.$message.info(`立即购买：${good.name}`)\n    },\n    \n    // 图片加载错误处理\n    handleImageError(event) {\n      event.target.src = '/images/women-top.jpg'\n    },\n    \n    // 跳转到商品页面\n    goToGoodsPage() {\n      if (this.selectedCategory) {\n        this.$router.push({\n          path: '/goods',\n          query: { categoryId: this.selectedCategory }\n        })\n      } else {\n        this.$router.push('/goods')\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.category-container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 20px;\n}\n\n.page-title {\n  text-align: center;\n  color: #333;\n  margin-bottom: 30px;\n  font-size: 28px;\n}\n\n.category-section {\n  margin-bottom: 30px;\n}\n\n.category-card {\n  border-radius: 12px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n}\n\n.category-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.category-header h2 {\n  margin: 0;\n  color: #333;\n}\n\n.category-list {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\n  gap: 15px;\n}\n\n.category-btn {\n  height: 60px;\n  border-radius: 8px;\n  border: 2px solid #e0e0e0;\n  background: white;\n  transition: all 0.3s ease;\n}\n\n.category-btn:hover {\n  border-color: #409eff;\n  color: #409eff;\n}\n\n.category-btn.active {\n  border-color: #409eff;\n  background: #409eff;\n  color: white;\n}\n\n.current-category {\n  margin-bottom: 20px;\n}\n\n/* 加载状态优化 */\n.loading {\n  padding: 40px 0;\n  /* 添加最小高度，避免布局跳动 */\n  min-height: 400px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.goods-section {\n  margin-top: 20px;\n}\n\n.goods-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.goods-header h3 {\n  margin: 0;\n  color: #333;\n}\n\n.goods-list {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\n  gap: 20px;\n  /* 添加过渡效果 */\n  transition: all 0.3s ease-in-out;\n  opacity: 1;\n  /* 添加最小高度，避免布局跳动 */\n  min-height: 400px;\n}\n\n/* 数据加载时的过渡效果 */\n.goods-list.loading-transition {\n  opacity: 0.7;\n  transform: scale(0.98);\n}\n\n.goods-card {\n  border: 1px solid #e0e0e0;\n  border-radius: 12px;\n  overflow: hidden;\n  background: white;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  transition: all 0.3s ease;\n  cursor: pointer;\n  /* 添加动画效果 */\n  animation: fadeIn 0.6s ease-out;\n  /* 添加will-change优化性能 */\n  will-change: transform, opacity;\n}\n\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n.goods-card:hover {\n  transform: translateY(-4px);\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);\n}\n\n.goods-image {\n  width: 100%;\n  height: 200px;\n  object-fit: cover;\n}\n\n.goods-info {\n  padding: 15px;\n}\n\n.goods-name {\n  font-size: 16px;\n  font-weight: bold;\n  margin: 0 0 8px;\n  color: #333;\n}\n\n.goods-desc {\n  font-size: 14px;\n  color: #666;\n  margin: 0 0 10px;\n  line-height: 1.4;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n}\n\n.goods-price {\n  font-size: 18px;\n  font-weight: bold;\n  color: #e60000;\n  margin: 0 0 12px;\n}\n\n.goods-actions {\n  text-align: center;\n}\n\n/* 无数据状态优化 */\n.no-data {\n  text-align: center;\n  padding: 60px 0;\n  /* 添加最小高度，避免布局跳动 */\n  min-height: 400px;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .category-list {\n    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));\n    gap: 10px;\n  }\n  \n  .category-btn {\n    height: 50px;\n    font-size: 14px;\n  }\n  \n  .goods-list {\n    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\n    gap: 15px;\n  }\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAoB;;EAKxBA,KAAK,EAAC;AAAkB;;EAEpBA,KAAK,EAAC;AAAiB;;EAYvBA,KAAK,EAAC;AAAe;;;EAyBDA,KAAK,EAAC;;;;EAUfA,KAAK,EAAC;;;EAKYA,KAAK,EAAC;AAAe;;EACpDA,KAAK,EAAC;AAAc;;;;EAkBhBA,KAAK,EAAC;AAAY;;EACjBA,KAAK,EAAC;AAAY;;EACnBA,KAAK,EAAC;AAAY;;EAClBA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAe;;EAetBA,KAAK,EAAC;AAAS;;;;;;;;;;;uBAjG7BC,mBAAA,CA4GM,OA5GNC,UA4GM,GA3GJC,mBAAA,UAAa,E,0BACbC,mBAAA,CAAgC;IAA5BJ,KAAK,EAAC;EAAY,GAAC,MAAI,qBAE3BG,mBAAA,UAAa,EACbC,mBAAA,CAoCM,OApCNC,UAoCM,GAnCJC,YAAA,CAkCUC,kBAAA;IAlCDP,KAAK,EAAC;EAAe;sBAC5B,MAUM,CAVNI,mBAAA,CAUM,OAVNI,UAUM,G,0BATJJ,mBAAA,CAAa,YAAT,MAAI,qBAEAK,KAAA,CAAAC,gBAAgB,I,cADxBC,YAAA,CAOYC,oBAAA;;MALVC,IAAI,EAAC,MAAM;MACXC,IAAI,EAAC,OAAO;MACXC,OAAK,EAAEC,QAAA,CAAAC;;wBACT,MAED,KAAAC,MAAA,QAAAA,MAAA,O,iBAFC,QAED,mB;;2EAGFd,mBAAA,CAoBM,OApBNe,UAoBM,GAnBJb,YAAA,CAOYM,oBAAA;MANTZ,KAAK,EAAAoB,eAAA;QAAA,UAA+BX,KAAA,CAAAC,gBAAgB;MAAA;MACpDK,OAAK,EAAAG,MAAA,QAAAA,MAAA,MAAAG,MAAA,IAAEL,QAAA,CAAAM,cAAc;MACtBR,IAAI,EAAC;;wBAEL,MAA2B,CAA3BR,YAAA,CAA2BiB,kBAAA;0BAAlB,MAAQ,CAARjB,YAAA,CAAQkB,eAAA,E;;qDAAU,QAE7B,oB;;qDAEAvB,mBAAA,CASYwB,SAAA,QAAAC,WAAA,CARSjB,KAAA,CAAAkB,UAAU,EAAtBC,QAAQ;2BADjBjB,YAAA,CASYC,oBAAA;QAPTiB,GAAG,EAAED,QAAQ,CAACE,EAAE;QAChB9B,KAAK,EAAAoB,eAAA;UAAA,UAA+BX,KAAA,CAAAC,gBAAgB,KAAKkB,QAAQ,CAACE;QAAE;QACpEf,OAAK,EAAAM,MAAA,IAAEL,QAAA,CAAAM,cAAc,CAACM,QAAQ;QAC/Bd,IAAI,EAAC;;0BAEL,MAA0B,CAA1BR,YAAA,CAA0BiB,kBAAA;4BAAjB,MAAO,CAAPjB,YAAA,CAAOyB,cAAA,E;;6BAAU,GAC1B,GAAAC,gBAAA,CAAGJ,QAAQ,CAACK,IAAI,iB;;;;;QAMxB9B,mBAAA,YAAe,EACJM,KAAA,CAAAC,gBAAgB,I,cAA3BT,mBAAA,CAOM,OAPNiC,UAOM,GANJ5B,YAAA,CAKE6B,mBAAA;IAJCC,KAAK,UAAU3B,KAAA,CAAA4B,oBAAoB;IACpCxB,IAAI,EAAC,MAAM;IACVyB,QAAQ,EAAE,KAAK;IAChB,WAAS,EAAT;6EAIJnC,mBAAA,UAAa,EACFM,KAAA,CAAA8B,OAAO,I,cAAlBtC,mBAAA,CAEM,OAFNuC,UAEM,GADJlC,YAAA,CAAkCmC,sBAAA;IAApBC,IAAI,EAAE,CAAC;IAAEC,QAAQ,EAAR;UAITlC,KAAA,CAAAmC,SAAS,CAACC,MAAM,Q,cAAhC5C,mBAAA,CAmCMwB,SAAA;IAAAI,GAAA;EAAA,IApCN1B,mBAAA,UAAa,EACbC,mBAAA,CAmCM,OAnCN0C,UAmCM,GAlCJ1C,mBAAA,CAGM,OAHN2C,UAGM,GAFJ3C,mBAAA,CAAsE,YAAA4B,gBAAA,CAA/DvB,KAAA,CAAA4B,oBAAoB,YAAW,MAAI,GAAAL,gBAAA,CAAGvB,KAAA,CAAAmC,SAAS,CAACC,MAAM,IAAG,GAAC,iBACjEvC,YAAA,CAAiEM,oBAAA;IAAtDC,IAAI,EAAC,SAAS;IAAEE,OAAK,EAAEC,QAAA,CAAAgC;;sBAAe,MAAI,KAAA9B,MAAA,QAAAA,MAAA,O,iBAAJ,MAAI,mB;;oCAGvDd,mBAAA,CA4BM;IA5BDJ,KAAK,EAAAoB,eAAA,EAAC,YAAY;MAAA,sBAAiCX,KAAA,CAAA8B;IAAO;yBAC7DtC,mBAAA,CA0BMwB,SAAA,QAAAC,WAAA,CAxBWjB,KAAA,CAAAmC,SAAS,EAAjBK,IAAI;yBAFbhD,mBAAA,CA0BM;MAzBJD,KAAK,EAAC,YAAY;MAEjB6B,GAAG,EAAEoB,IAAI,CAACnB,EAAE;MACZf,OAAK,EAAAM,MAAA,IAAEL,QAAA,CAAAkC,iBAAiB,CAACD,IAAI;QAE9B7C,mBAAA,CAKE;MAJC+C,GAAG,EAAEF,IAAI,CAACG,IAAI;MACdC,GAAG,EAAEJ,IAAI,CAAChB,IAAI;MACfjC,KAAK,EAAC,aAAa;MAClBsD,OAAK,EAAApC,MAAA,QAAAA,MAAA,UAAAqC,IAAA,KAAEvC,QAAA,CAAAwC,gBAAA,IAAAxC,QAAA,CAAAwC,gBAAA,IAAAD,IAAA,CAAgB;2DAE1BnD,mBAAA,CAaM,OAbNqD,WAaM,GAZJrD,mBAAA,CAA2C,MAA3CsD,WAA2C,EAAA1B,gBAAA,CAAjBiB,IAAI,CAAChB,IAAI,kBACnC7B,mBAAA,CAA4D,KAA5DuD,WAA4D,EAAA3B,gBAAA,CAAnCiB,IAAI,CAACW,WAAW,8BACzCxD,mBAAA,CAA4C,KAA5CyD,WAA4C,EAArB,GAAC,GAAA7B,gBAAA,CAAGiB,IAAI,CAACa,KAAK,kBACrC1D,mBAAA,CAQM,OARN2D,WAQM,GAPJzD,YAAA,CAMYM,oBAAA;MALVC,IAAI,EAAC,SAAS;MACdC,IAAI,EAAC,OAAO;MACXC,OAAK,EAAAiD,cAAA,CAAA3C,MAAA,IAAOL,QAAA,CAAAiD,SAAS,CAAChB,IAAI;;wBAC5B,MAED,KAAA/B,MAAA,QAAAA,MAAA,O,iBAFC,SAED,mB;;;uHAQVjB,mBAAA,CAEMwB,SAAA;IAAAI,GAAA;EAAA,IAHN1B,mBAAA,SAAY,EACZC,mBAAA,CAEM,OAFN8D,WAEM,GADJ5D,YAAA,CAAkC6D,mBAAA;IAAxBP,WAAW,EAAC;EAAS,G,qDAGjCzD,mBAAA,YAAe,EACfG,YAAA,CAKE8D,wBAAA;gBAJS3D,KAAA,CAAA4D,gBAAgB;+DAAhB5D,KAAA,CAAA4D,gBAAgB,GAAAhD,MAAA;IACxBiD,OAAO,EAAE7D,KAAA,CAAA8D,eAAe;IACxBC,WAAW,EAAExD,QAAA,CAAAiD,SAAS;IACtBQ,QAAO,EAAEzD,QAAA,CAAA0D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}