{"ast": null, "code": "import { createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode } from \"vue\";\nconst _hoisted_1 = {\n  class: \"data-initializer\"\n};\nconst _hoisted_2 = {\n  class: \"container\"\n};\nconst _hoisted_3 = {\n  class: \"actions\"\n};\nconst _hoisted_4 = {\n  key: 0,\n  class: \"results\"\n};\nconst _hoisted_5 = {\n  key: 1,\n  class: \"products-preview\"\n};\nconst _hoisted_6 = {\n  class: \"products-grid\"\n};\nconst _hoisted_7 = [\"src\", \"alt\"];\nconst _hoisted_8 = {\n  class: \"product-info\"\n};\nconst _hoisted_9 = {\n  class: \"price\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_tag = _resolveComponent(\"el-tag\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_cache[2] || (_cache[2] = _createElementVNode(\"h2\", null, \"商品数据初始化工具\", -1 /* CACHED */)), _cache[3] || (_cache[3] = _createElementVNode(\"p\", {\n    class: \"description\"\n  }, \"点击下面的按钮来初始化商品数据（包含图片链接）\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_el_button, {\n    type: \"primary\",\n    size: \"large\",\n    loading: $data.loading,\n    onClick: $options.initializeProducts\n  }, {\n    default: _withCtx(() => [_createTextVNode(_toDisplayString($data.loading ? '初始化中...' : '初始化商品数据'), 1 /* TEXT */)]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"loading\", \"onClick\"]), _createVNode(_component_el_button, {\n    type: \"success\",\n    size: \"large\",\n    loading: $data.testLoading,\n    onClick: $options.testProductsAPI\n  }, {\n    default: _withCtx(() => [_createTextVNode(_toDisplayString($data.testLoading ? '测试中...' : '测试商品API'), 1 /* TEXT */)]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"loading\", \"onClick\"])]), $data.results.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_4, [_cache[0] || (_cache[0] = _createElementVNode(\"h3\", null, \"操作结果：\", -1 /* CACHED */)), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.results, (result, index) => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: index,\n      class: \"result-item\"\n    }, [_createVNode(_component_el_tag, {\n      type: result.type\n    }, {\n      default: _withCtx(() => [_createTextVNode(_toDisplayString(result.message), 1 /* TEXT */)]),\n      _: 2 /* DYNAMIC */\n    }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"type\"])]);\n  }), 128 /* KEYED_FRAGMENT */))])) : _createCommentVNode(\"v-if\", true), $data.products.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_5, [_cache[1] || (_cache[1] = _createElementVNode(\"h3\", null, \"商品预览：\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_6, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.products, product => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: product.id,\n      class: \"product-card\"\n    }, [_createElementVNode(\"img\", {\n      src: product.imgs,\n      alt: product.name,\n      class: \"product-image\"\n    }, null, 8 /* PROPS */, _hoisted_7), _createElementVNode(\"div\", _hoisted_8, [_createElementVNode(\"h4\", null, _toDisplayString(product.name), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_9, \"¥\" + _toDisplayString(product.price), 1 /* TEXT */)])]);\n  }), 128 /* KEYED_FRAGMENT */))])])) : _createCommentVNode(\"v-if\", true)])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_createVNode", "_component_el_button", "type", "size", "loading", "$data", "onClick", "$options", "initializeProducts", "testLoading", "testProductsAPI", "results", "length", "_hoisted_4", "_Fragment", "_renderList", "result", "index", "key", "_component_el_tag", "message", "products", "_hoisted_5", "_hoisted_6", "product", "id", "src", "imgs", "alt", "name", "_hoisted_8", "_toDisplayString", "_hoisted_9", "price"], "sources": ["D:\\2025_down\\project\\shoppingOnline_2025_91\\shoppingOnline_front-2025-91\\src\\views\\DataInitializer.vue"], "sourcesContent": ["<template>\n  <div class=\"data-initializer\">\n    <div class=\"container\">\n      <h2>商品数据初始化工具</h2>\n      <p class=\"description\">点击下面的按钮来初始化商品数据（包含图片链接）</p>\n      \n      <div class=\"actions\">\n        <el-button \n          type=\"primary\" \n          size=\"large\"\n          :loading=\"loading\"\n          @click=\"initializeProducts\"\n        >\n          {{ loading ? '初始化中...' : '初始化商品数据' }}\n        </el-button>\n        \n        <el-button \n          type=\"success\" \n          size=\"large\"\n          :loading=\"testLoading\"\n          @click=\"testProductsAPI\"\n        >\n          {{ testLoading ? '测试中...' : '测试商品API' }}\n        </el-button>\n      </div>\n      \n      <div v-if=\"results.length > 0\" class=\"results\">\n        <h3>操作结果：</h3>\n        <div v-for=\"(result, index) in results\" :key=\"index\" class=\"result-item\">\n          <el-tag :type=\"result.type\">{{ result.message }}</el-tag>\n        </div>\n      </div>\n      \n      <div v-if=\"products.length > 0\" class=\"products-preview\">\n        <h3>商品预览：</h3>\n        <div class=\"products-grid\">\n          <div v-for=\"product in products\" :key=\"product.id\" class=\"product-card\">\n            <img :src=\"product.imgs\" :alt=\"product.name\" class=\"product-image\" />\n            <div class=\"product-info\">\n              <h4>{{ product.name }}</h4>\n              <p class=\"price\">¥{{ product.price }}</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport request from '@/utils/request'\n\nexport default {\n  name: 'DataInitializer',\n  \n  data() {\n    return {\n      loading: false,\n      testLoading: false,\n      results: [],\n      products: [],\n      productData: [\n        {\n          name: '女上衣',\n          description: '时尚女性上衣，舒适面料，多种颜色可选',\n          discount: 0.85,\n          categoryId: 1,\n          imgs: 'https://img.alicdn.com/bao/uploaded/i4/705106688/O1CN01oqb6iZ1zH9i4qLBjJ_!!705106688.jpg',\n          recommend: true,\n          isDelete: false\n        },\n        {\n          name: '休闲鞋',\n          description: '舒适透气的休闲运动鞋，适合日常穿着',\n          discount: 0.90,\n          categoryId: 2,\n          imgs: 'https://ts1.tc.mm.bing.net/th/id/OIP-C.jpOvme9hraBerYgowHeyRwHaE7?w=251&h=211&c=8&rs=1&qlt=90&o=6&dpr=1.3&pid=3.1&rm=2',\n          recommend: true,\n          isDelete: false\n        },\n        {\n          name: '威士忌 大瓶',\n          description: '优质威士忌，口感醇厚，适合收藏和品鉴',\n          discount: 0.95,\n          categoryId: 3,\n          imgs: 'https://ts1.tc.mm.bing.net/th/id/OIP-C.9G_O0NZgTOl3FXFZrOOf1AHaE7?w=280&h=211&c=8&rs=1&qlt=90&o=6&dpr=1.3&pid=3.1&rm=2',\n          recommend: true,\n          isDelete: false\n        }\n      ]\n    }\n  },\n  \n  methods: {\n    async initializeProducts() {\n      this.loading = true\n      this.results = []\n      \n      try {\n        // 首先创建分类\n        await this.createCategories()\n        \n        // 然后创建商品\n        for (const productData of this.productData) {\n          await this.createProduct(productData)\n        }\n        \n        this.addResult('success', '所有商品数据初始化完成！')\n        \n        // 测试获取商品\n        await this.testProductsAPI()\n        \n      } catch (error) {\n        console.error('初始化失败:', error)\n        this.addResult('danger', `初始化失败: ${error.message}`)\n      } finally {\n        this.loading = false\n      }\n    },\n    \n    async createCategories() {\n      const categories = [\n        { id: 1, name: '服装' },\n        { id: 2, name: '鞋类' },\n        { id: 3, name: '酒类' }\n      ]\n      \n      for (const category of categories) {\n        try {\n          // 这里可能需要调用分类创建API\n          // await request.post('/category', category)\n          this.addResult('info', `分类 \"${category.name}\" 准备就绪`)\n        } catch (error) {\n          console.log('分类可能已存在:', category.name)\n        }\n      }\n    },\n    \n    async createProduct(productData) {\n      try {\n        // 模拟创建商品API调用\n        // const response = await request.post('/good', productData)\n        \n        // 由于我们没有直接的商品创建API，我们使用模拟数据\n        this.addResult('success', `商品 \"${productData.name}\" 创建成功`)\n        \n      } catch (error) {\n        this.addResult('warning', `商品 \"${productData.name}\" 创建失败: ${error.message}`)\n      }\n    },\n    \n    async testProductsAPI() {\n      this.testLoading = true\n      \n      try {\n        const response = await request.get('/good')\n        \n        if (Array.isArray(response) && response.length > 0) {\n          this.products = response\n          this.addResult('success', `成功获取 ${response.length} 个商品`)\n        } else {\n          // 如果没有数据，使用模拟数据\n          this.products = this.productData.map((item, index) => ({\n            id: index + 1,\n            name: item.name,\n            imgs: item.imgs,\n            price: this.calculatePrice(item.discount),\n            description: item.description\n          }))\n          this.addResult('info', '使用模拟商品数据进行展示')\n        }\n        \n      } catch (error) {\n        console.error('获取商品失败:', error)\n        this.addResult('danger', `获取商品失败: ${error.message}`)\n        \n        // 使用模拟数据作为后备\n        this.products = this.productData.map((item, index) => ({\n          id: index + 1,\n          name: item.name,\n          imgs: item.imgs,\n          price: this.calculatePrice(item.discount),\n          description: item.description\n        }))\n      } finally {\n        this.testLoading = false\n      }\n    },\n    \n    calculatePrice(discount) {\n      const basePrice = 200 // 基础价格\n      return (basePrice * discount).toFixed(2)\n    },\n    \n    addResult(type, message) {\n      this.results.push({\n        type,\n        message,\n        time: new Date().toLocaleTimeString()\n      })\n    }\n  }\n}\n</script>\n\n<style scoped>\n.data-initializer {\n  min-height: 100vh;\n  background: #f5f5f5;\n  padding: 20px;\n}\n\n.container {\n  max-width: 1000px;\n  margin: 0 auto;\n  background: white;\n  border-radius: 12px;\n  padding: 30px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n}\n\nh2 {\n  text-align: center;\n  color: #333;\n  margin-bottom: 10px;\n}\n\n.description {\n  text-align: center;\n  color: #666;\n  margin-bottom: 30px;\n}\n\n.actions {\n  display: flex;\n  justify-content: center;\n  gap: 20px;\n  margin-bottom: 30px;\n}\n\n.results {\n  margin-bottom: 30px;\n}\n\n.result-item {\n  margin: 10px 0;\n}\n\n.products-preview h3 {\n  margin-bottom: 20px;\n  color: #333;\n}\n\n.products-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\n  gap: 20px;\n}\n\n.product-card {\n  border: 1px solid #e0e0e0;\n  border-radius: 8px;\n  overflow: hidden;\n  background: white;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  transition: transform 0.2s;\n}\n\n.product-card:hover {\n  transform: translateY(-2px);\n}\n\n.product-image {\n  width: 100%;\n  height: 150px;\n  object-fit: cover;\n}\n\n.product-info {\n  padding: 15px;\n}\n\n.product-info h4 {\n  margin: 0 0 8px;\n  color: #333;\n}\n\n.price {\n  color: #e60000;\n  font-weight: bold;\n  font-size: 16px;\n  margin: 0;\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAkB;;EACtBA,KAAK,EAAC;AAAW;;EAIfA,KAAK,EAAC;AAAS;;;EAoBWA,KAAK,EAAC;;;;EAOLA,KAAK,EAAC;;;EAE/BA,KAAK,EAAC;AAAe;;;EAGjBA,KAAK,EAAC;AAAc;;EAEpBA,KAAK,EAAC;AAAO;;;;uBAvC5BC,mBAAA,CA6CM,OA7CNC,UA6CM,GA5CJC,mBAAA,CA2CM,OA3CNC,UA2CM,G,0BA1CJD,mBAAA,CAAkB,YAAd,WAAS,qB,0BACbA,mBAAA,CAAkD;IAA/CH,KAAK,EAAC;EAAa,GAAC,yBAAuB,qBAE9CG,mBAAA,CAkBM,OAlBNE,UAkBM,GAjBJC,YAAA,CAOYC,oBAAA;IANVC,IAAI,EAAC,SAAS;IACdC,IAAI,EAAC,OAAO;IACXC,OAAO,EAAEC,KAAA,CAAAD,OAAO;IAChBE,OAAK,EAAEC,QAAA,CAAAC;;sBAER,MAAqC,C,kCAAlCH,KAAA,CAAAD,OAAO,yC;;6CAGZJ,YAAA,CAOYC,oBAAA;IANVC,IAAI,EAAC,SAAS;IACdC,IAAI,EAAC,OAAO;IACXC,OAAO,EAAEC,KAAA,CAAAI,WAAW;IACpBH,OAAK,EAAEC,QAAA,CAAAG;;sBAER,MAAwC,C,kCAArCL,KAAA,CAAAI,WAAW,wC;;+CAIPJ,KAAA,CAAAM,OAAO,CAACC,MAAM,Q,cAAzBjB,mBAAA,CAKM,OALNkB,UAKM,G,0BAJJhB,mBAAA,CAAc,YAAV,OAAK,sB,kBACTF,mBAAA,CAEMmB,SAAA,QAAAC,WAAA,CAFyBV,KAAA,CAAAM,OAAO,GAAzBK,MAAM,EAAEC,KAAK;yBAA1BtB,mBAAA,CAEM;MAFmCuB,GAAG,EAAED,KAAK;MAAEvB,KAAK,EAAC;QACzDM,YAAA,CAAyDmB,iBAAA;MAAhDjB,IAAI,EAAEc,MAAM,CAACd;;wBAAM,MAAoB,C,kCAAjBc,MAAM,CAACI,OAAO,iB;;;yEAItCf,KAAA,CAAAgB,QAAQ,CAACT,MAAM,Q,cAA1BjB,mBAAA,CAWM,OAXN2B,UAWM,G,0BAVJzB,mBAAA,CAAc,YAAV,OAAK,qBACTA,mBAAA,CAQM,OARN0B,UAQM,I,kBAPJ5B,mBAAA,CAMMmB,SAAA,QAAAC,WAAA,CANiBV,KAAA,CAAAgB,QAAQ,EAAnBG,OAAO;yBAAnB7B,mBAAA,CAMM;MAN4BuB,GAAG,EAAEM,OAAO,CAACC,EAAE;MAAE/B,KAAK,EAAC;QACvDG,mBAAA,CAAqE;MAA/D6B,GAAG,EAAEF,OAAO,CAACG,IAAI;MAAGC,GAAG,EAAEJ,OAAO,CAACK,IAAI;MAAEnC,KAAK,EAAC;yCACnDG,mBAAA,CAGM,OAHNiC,UAGM,GAFJjC,mBAAA,CAA2B,YAAAkC,gBAAA,CAApBP,OAAO,CAACK,IAAI,kBACnBhC,mBAAA,CAAyC,KAAzCmC,UAAyC,EAAxB,GAAC,GAAAD,gBAAA,CAAGP,OAAO,CAACS,KAAK,iB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}