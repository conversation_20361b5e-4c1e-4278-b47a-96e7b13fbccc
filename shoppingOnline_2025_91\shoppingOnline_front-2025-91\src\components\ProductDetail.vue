<template>
  <el-dialog
    v-model="visible"
    :title="product.name"
    width="800px"
    :before-close="handleClose"
    class="product-detail-dialog"
  >
    <div class="product-detail" v-if="product">
      <div class="product-main">
        <!-- 商品图片 -->
        <div class="product-image-section">
          <img 
            :src="product.imgs" 
            :alt="product.name" 
            class="product-image"
            @error="handleImageError"
          />
        </div>
        
        <!-- 商品信息 -->
        <div class="product-info-section">
          <h2 class="product-title">{{ product.name }}</h2>
          
          <div class="product-price">
            <span class="current-price">¥{{ product.price }}</span>
            <span v-if="product.discount && product.discount < 1" class="discount-info">
              {{ Math.round(product.discount * 100) }}折
            </span>
          </div>
          
          <div class="product-stats">
            <span class="sales-info">销量: {{ product.sales || 0 }}</span>
            <span class="category-info" v-if="product.categoryName">
              分类: {{ product.categoryName }}
            </span>
          </div>
          
          <!-- 商品描述 -->
          <div class="product-description">
            <h3>商品描述</h3>
            <p>{{ product.description || '暂无详细描述' }}</p>
          </div>
          
          <!-- 操作按钮 -->
          <div class="product-actions">
            <el-button 
              type="primary" 
              size="large"
              @click="addToCart"
              :loading="addingToCart"
            >
              <el-icon><ShoppingCart /></el-icon>
              加入购物车
            </el-button>
            <el-button 
              type="success" 
              size="large"
              @click="buyNow"
            >
              立即购买
            </el-button>
          </div>
        </div>
      </div>
      
      <!-- 详细信息 -->
      <div class="product-details">
        <el-tabs v-model="activeTab">
          <el-tab-pane label="商品详情" name="details">
            <div class="detail-content">
              <h4>商品详细信息</h4>
              <p>{{ product.description || '暂无详细描述' }}</p>
              
              <div class="product-attributes" v-if="product.attributes">
                <h4>商品属性</h4>
                <el-descriptions :column="2" border>
                  <el-descriptions-item label="商品ID">{{ product.id }}</el-descriptions-item>
                  <el-descriptions-item label="商品名称">{{ product.name }}</el-descriptions-item>
                  <el-descriptions-item label="价格">¥{{ product.price }}</el-descriptions-item>
                  <el-descriptions-item label="折扣" v-if="product.discount">
                    {{ Math.round(product.discount * 100) }}%
                  </el-descriptions-item>
                  <el-descriptions-item label="销量">{{ product.sales || 0 }}</el-descriptions-item>
                  <el-descriptions-item label="分类" v-if="product.categoryName">
                    {{ product.categoryName }}
                  </el-descriptions-item>
                </el-descriptions>
              </div>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="用户评价" name="reviews">
            <div class="reviews-content">
              <el-empty description="暂无用户评价" />
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { ShoppingCart } from '@element-plus/icons-vue'

export default {
  name: 'ProductDetail',
  
  components: {
    ShoppingCart
  },
  
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    product: {
      type: Object,
      default: () => ({})
    }
  },
  
  emits: ['update:modelValue', 'add-to-cart', 'buy-now'],
  
  data() {
    return {
      activeTab: 'details',
      addingToCart: false
    }
  },
  
  computed: {
    visible: {
      get() {
        return this.modelValue
      },
      set(value) {
        this.$emit('update:modelValue', value)
      }
    }
  },
  
  methods: {
    handleClose() {
      this.visible = false
    },
    
    handleImageError(event) {
      console.error('商品详情图片加载失败:', event.target.src)
      event.target.src = '/images/women-top.jpg' // 使用默认图片
    },
    
    async addToCart() {
      try {
        this.addingToCart = true
        this.$emit('add-to-cart', this.product)
        this.$message.success(`已加入购物车：${this.product.name}`)
      } catch (error) {
        this.$message.error('加入购物车失败')
      } finally {
        this.addingToCart = false
      }
    },
    
    buyNow() {
      this.$emit('buy-now', this.product)
      this.$message.info(`立即购买：${this.product.name}`)
    }
  }
}
</script>

<style scoped>
.product-detail-dialog {
  border-radius: 12px;
}

.product-detail {
  padding: 20px 0;
}

.product-main {
  display: flex;
  gap: 30px;
  margin-bottom: 30px;
}

.product-image-section {
  flex: 1;
  max-width: 350px;
}

.product-image {
  width: 100%;
  height: 350px;
  object-fit: cover;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.product-info-section {
  flex: 1;
  padding-left: 20px;
}

.product-title {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 15px;
  line-height: 1.4;
}

.product-price {
  margin-bottom: 15px;
}

.current-price {
  font-size: 28px;
  font-weight: bold;
  color: #e60000;
  margin-right: 10px;
}

.discount-info {
  background: #ff4757;
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.product-stats {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  font-size: 14px;
  color: #666;
}

.product-description {
  margin-bottom: 30px;
}

.product-description h3 {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 10px;
  color: #333;
}

.product-description p {
  color: #666;
  line-height: 1.6;
}

.product-actions {
  display: flex;
  gap: 15px;
}

.product-actions .el-button {
  flex: 1;
  height: 50px;
  font-size: 16px;
}

.product-details {
  border-top: 1px solid #f0f0f0;
  padding-top: 20px;
}

.detail-content h4 {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 15px;
  color: #333;
}

.detail-content p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 20px;
}

.product-attributes {
  margin-top: 20px;
}

.reviews-content {
  padding: 40px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .product-main {
    flex-direction: column;
    gap: 20px;
  }
  
  .product-info-section {
    padding-left: 0;
  }
  
  .product-actions {
    flex-direction: column;
  }
}
</style>
