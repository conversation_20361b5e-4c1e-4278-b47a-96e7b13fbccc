{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport md5 from \"js-md5\";\nimport { useUserStore } from '@/store/index';\nexport default {\n  name: 'LoginView',\n  components: {},\n  data() {\n    return {\n      userStore: null,\n      loginForm: {\n        username: '',\n        password: ''\n      },\n      registerForm: {\n        username: '',\n        email: '',\n        password: '',\n        confirmPassword: ''\n      },\n      rememberMe: false,\n      showRegister: false,\n      isLoading: false,\n      isRegistering: false,\n      usernameError: '',\n      passwordError: '',\n      message: {\n        show: false,\n        type: '',\n        text: ''\n      }\n      // 移除模拟用户数据，使用真实API\n    };\n  },\n  computed: {},\n  watch: {},\n  methods: {\n    // 验证用户名\n    validateUsername() {\n      if (!this.loginForm.username.trim()) {\n        this.usernameError = '请输入用户名';\n      } else {\n        this.usernameError = '';\n      }\n    },\n    // 验证密码\n    validatePassword() {\n      if (!this.loginForm.password.trim()) {\n        this.passwordError = '请输入密码';\n      } else {\n        this.passwordError = '';\n      }\n    },\n    // 登录API\n    async loginAPI(username, password) {\n      try {\n        const response = await fetch('http://127.0.0.1:9197/userAPI/login', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify({\n            username: username,\n            password: md5(password)\n          })\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        const data = await response.json();\n        return data;\n      } catch (error) {\n        throw new Error('登录请求失败: ' + error.message);\n      }\n    },\n    // 注册API（如果有注册接口的话）\n    async registerAPI(userData) {\n      try {\n        const response = await fetch('http://127.0.0.1:9197/userAPI/register', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify({\n            username: userData.username,\n            password: md5(userData.password)\n          })\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        const data = await response.json();\n        return data;\n      } catch (error) {\n        throw new Error('注册请求失败: ' + error.message);\n      }\n    },\n    // 处理登录\n    async handleLogin() {\n      this.validateUsername();\n      this.validatePassword();\n      if (this.usernameError || this.passwordError) {\n        return;\n      }\n      this.isLoading = true;\n      try {\n        const response = await this.loginAPI(this.loginForm.username, this.loginForm.password);\n\n        // 根据后端返回的数据结构处理\n        if (Number(response.code) === 200) {\n          this.showMessage('success', '登录成功！');\n          console.log('完整响应:', response);\n\n          // 保存token和用户信息\n          const token = response.data?.token || 'login-success-' + Date.now();\n          const user = response.data || {\n            username: this.loginForm.username,\n            role: 'user',\n            id: Date.now()\n          };\n\n          // 使用用户store保存登录状态\n          this.userStore.login(user, token);\n          console.log('登录成功，准备跳转到商品页');\n          this.$router.push('/goods');\n        } else {\n          // 登录失败\n          const errorMsg = response.message || response.msg || '登录失败，请重试';\n          this.showMessage('error', errorMsg);\n        }\n      } catch (error) {\n        console.error('登录错误:', error);\n        this.showMessage('error', error.message || '网络错误，请稍后重试');\n      } finally {\n        this.isLoading = false;\n      }\n    },\n    // 处理注册\n    async handleRegister() {\n      if (this.registerForm.password !== this.registerForm.confirmPassword) {\n        this.showMessage('error', '两次输入的密码不一致');\n        return;\n      }\n      this.isRegistering = true;\n      try {\n        const response = await this.registerAPI({\n          username: this.registerForm.username,\n          password: this.registerForm.password\n        });\n\n        // 根据后端返回的数据结构处理\n        if (Number(response.code) === 200) {\n          this.showMessage('success', '注册成功！请登录');\n          this.closeRegisterForm();\n          this.resetRegisterForm();\n        } else {\n          const errorMsg = response.message || response.msg || '注册失败，请重试';\n          this.showMessage('error', errorMsg);\n        }\n      } catch (error) {\n        console.error('注册错误:', error);\n        this.showMessage('error', error.message || '网络错误，请稍后重试');\n      } finally {\n        this.isRegistering = false;\n      }\n    },\n    // 显示注册表单\n    showRegisterForm() {\n      this.showRegister = true;\n    },\n    // 关闭注册表单\n    closeRegisterForm() {\n      this.showRegister = false;\n    },\n    // 重置注册表单\n    resetRegisterForm() {\n      this.registerForm = {\n        username: '',\n        password: '',\n        confirmPassword: ''\n      };\n    },\n    // 忘记密码\n    showForgotPassword() {\n      this.showMessage('info', '请联系管理员重置密码');\n    },\n    // 第三方登录\n    thirdPartyLogin(type) {\n      this.showMessage('info', `${type}登录功能开发中...`);\n    },\n    // 显示消息\n    showMessage(type, text) {\n      this.message = {\n        show: true,\n        type,\n        text\n      };\n      setTimeout(() => {\n        this.message.show = false;\n      }, 3000);\n    }\n  },\n  created() {\n    // 初始化用户store\n    this.userStore = useUserStore();\n\n    // 检查是否已经登录\n    this.userStore.restoreLoginState();\n    if (this.userStore.isAuthenticated) {\n      console.log('用户已登录，跳转到商品页');\n      this.$router.push('/goods');\n    }\n  },\n  mounted() {},\n  beforeUpdate() {},\n  updated() {},\n  beforeUnmount() {},\n  unmounted() {},\n  activated() {},\n  deactivated() {}\n};", "map": {"version": 3, "names": ["md5", "useUserStore", "name", "components", "data", "userStore", "loginForm", "username", "password", "registerForm", "email", "confirmPassword", "rememberMe", "showRegister", "isLoading", "isRegistering", "usernameError", "passwordError", "message", "show", "type", "text", "computed", "watch", "methods", "validateUsername", "trim", "validatePassword", "loginAPI", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "ok", "Error", "status", "json", "error", "registerAPI", "userData", "handleLogin", "Number", "code", "showMessage", "console", "log", "token", "Date", "now", "user", "role", "id", "login", "$router", "push", "errorMsg", "msg", "handleRegister", "closeRegisterForm", "resetRegisterForm", "showRegisterForm", "showForgotPassword", "thirdParty<PERSON><PERSON>in", "setTimeout", "created", "restoreLoginState", "isAuthenticated", "mounted", "beforeUpdate", "updated", "beforeUnmount", "unmounted", "activated", "deactivated"], "sources": ["D:\\2025_down\\project\\shoppingOnline_2025_91\\shoppingOnline_front-2025-91\\src\\views\\Login.vue"], "sourcesContent": ["\r\n<template>\r\n  <div class=\"login-container\">\r\n    <div class=\"login-card\">\r\n      <div class=\"login-header\">\r\n        <h2>欢迎登录</h2>\r\n        <p class=\"subtitle\">在线商城管理系统</p>\r\n      </div>\r\n     <!-- 登录页面 -->\r\n      <form @submit.prevent=\"handleLogin\" class=\"login-form\">\r\n        <div class=\"form-group\">\r\n          <input\r\n            v-model=\"loginForm.username\"\r\n            type=\"text\"\r\n            placeholder=\"用户名\"\r\n            class=\"form-input\"\r\n            :class=\"{ 'error': usernameError }\"\r\n            @blur=\"validateUsername\"\r\n          />\r\n          <span v-if=\"usernameError\" class=\"error-text\">{{ usernameError }}</span>\r\n        </div>\r\n\r\n        <div class=\"form-group\">\r\n          <input\r\n            v-model=\"loginForm.password\"\r\n            type=\"password\"\r\n            placeholder=\"密码\"\r\n            class=\"form-input\"\r\n            :class=\"{ 'error': passwordError }\"\r\n            @blur=\"validatePassword\"\r\n          />\r\n          <span v-if=\"passwordError\" class=\"error-text\">{{ passwordError }}</span>\r\n        </div>\r\n\r\n        <div class=\"form-options\">\r\n          <label class=\"checkbox-wrapper\">\r\n            <input v-model=\"rememberMe\" type=\"checkbox\" />\r\n            <span class=\"checkmark\"></span>\r\n            记住我\r\n          </label>\r\n          <a href=\"#\" class=\"forgot-link\" @click.prevent=\"showForgotPassword\">忘记密码?</a>\r\n        </div>\r\n\r\n        <button type=\"submit\" class=\"login-btn\" :disabled=\"isLoading\">\r\n          {{ isLoading ? '登录中...' : '登 录' }}\r\n        </button>\r\n      </form>\r\n\r\n      <div class=\"register-section\">\r\n        <span>没有账号？</span>\r\n        <button @click=\"showRegisterForm\" class=\"register-btn\">注册用户</button>\r\n      </div>\r\n\r\n      <div class=\"divider\">\r\n        <span>第三方登录</span>\r\n      </div>\r\n\r\n      <div class=\"social-login\">\r\n        <button class=\"social-btn wechat\" @click=\"thirdPartyLogin('wechat')\">\r\n          <div class=\"social-icon wechat-icon\"></div>\r\n        </button>\r\n        <button class=\"social-btn weibo\" @click=\"thirdPartyLogin('weibo')\">\r\n          <div class=\"social-icon weibo-icon\"></div>\r\n        </button>\r\n        <button class=\"social-btn qq\" @click=\"thirdPartyLogin('qq')\">\r\n          <div class=\"social-icon qq-icon\"></div>\r\n        </button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 注册弹窗 -->\r\n    <div v-if=\"showRegister\" class=\"modal-overlay\" @click=\"closeRegisterForm\">\r\n      <div class=\"modal-content\" @click.stop>\r\n        <div class=\"modal-header\">\r\n          <h3>用户注册</h3>\r\n          <button @click=\"closeRegisterForm\" class=\"close-btn\">&times;</button>\r\n        </div>\r\n        <form @submit.prevent=\"handleRegister\" class=\"register-form\">\r\n          <div class=\"form-group\">\r\n            <input\r\n              v-model=\"registerForm.username\"\r\n              type=\"text\"\r\n              placeholder=\"用户名\"\r\n              class=\"form-input\"\r\n              required\r\n            />\r\n          </div>\r\n          <div class=\"form-group\">\r\n            <input\r\n              v-model=\"registerForm.password\"\r\n              type=\"password\"\r\n              placeholder=\"密码\"\r\n              class=\"form-input\"\r\n              required\r\n            />\r\n          </div>\r\n          <div class=\"form-group\">\r\n            <input\r\n              v-model=\"registerForm.confirmPassword\"\r\n              type=\"password\"\r\n              placeholder=\"确认密码\"\r\n              class=\"form-input\"\r\n              required\r\n            />\r\n          </div>\r\n          <button type=\"submit\" class=\"register-submit-btn\" :disabled=\"isRegistering\">\r\n            {{ isRegistering ? '注册中...' : '注册' }}\r\n          </button>\r\n        </form>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 消息提示 -->\r\n    <div v-if=\"message.show\" :class=\"['message', message.type]\">\r\n      {{ message.text }}\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport md5 from \"js-md5\";\r\nimport { useUserStore } from '@/store/index';\r\n\r\nexport default {\r\n  name: 'LoginView',\r\n\r\n  components: {},\r\n\r\n  data() {\r\n    return {\r\n      userStore: null,\r\n      loginForm: {\r\n        username: '',\r\n        password: ''\r\n      },\r\n      registerForm: {\r\n        username: '',\r\n        email: '',\r\n        password: '',\r\n        confirmPassword: ''\r\n      },\r\n      rememberMe: false,\r\n      showRegister: false,\r\n      isLoading: false,\r\n      isRegistering: false,\r\n      usernameError: '',\r\n      passwordError: '',\r\n      message: {\r\n        show: false,\r\n        type: '',\r\n        text: ''\r\n      }\r\n      // 移除模拟用户数据，使用真实API\r\n    };\r\n  },\r\n\r\n  computed: {},\r\n\r\n  watch: {},\r\n\r\n  methods: {\r\n    // 验证用户名\r\n    validateUsername() {\r\n      if (!this.loginForm.username.trim()) {\r\n        this.usernameError = '请输入用户名';\r\n      } else {\r\n        this.usernameError = '';\r\n      }\r\n    },\r\n\r\n    // 验证密码\r\n    validatePassword() {\r\n      if (!this.loginForm.password.trim()) {\r\n        this.passwordError = '请输入密码';\r\n      } else {\r\n        this.passwordError = '';\r\n      }\r\n    },\r\n\r\n    // 登录API\r\n    async loginAPI(username, password) {\r\n      try {\r\n        const response = await fetch('http://127.0.0.1:9197/userAPI/login', {\r\n          method: 'POST',\r\n          headers: {\r\n            'Content-Type': 'application/json',\r\n          },\r\n          body: JSON.stringify({\r\n            username: username,\r\n            password: md5(password)\r\n          })\r\n        });\r\n\r\n        if (!response.ok) {\r\n          throw new Error(`HTTP error! status: ${response.status}`);\r\n        }\r\n\r\n        const data = await response.json();\r\n        return data;\r\n      } catch (error) {\r\n        throw new Error('登录请求失败: ' + error.message);\r\n      }\r\n    },\r\n\r\n    // 注册API（如果有注册接口的话）\r\n    async registerAPI(userData) {\r\n      try {\r\n        const response = await fetch('http://127.0.0.1:9197/userAPI/register', {\r\n          method: 'POST',\r\n          headers: {\r\n            'Content-Type': 'application/json',\r\n          },\r\n          body: JSON.stringify({\r\n            username: userData.username,\r\n            password: md5(userData.password)\r\n          })\r\n        });\r\n\r\n        if (!response.ok) {\r\n          throw new Error(`HTTP error! status: ${response.status}`);\r\n        }\r\n\r\n        const data = await response.json();\r\n        return data;\r\n      } catch (error) {\r\n        throw new Error('注册请求失败: ' + error.message);\r\n      }\r\n    },\r\n\r\n    // 处理登录\r\n    async handleLogin() {\r\n      this.validateUsername();\r\n      this.validatePassword();\r\n      \r\n      if (this.usernameError || this.passwordError) {\r\n        return;\r\n      }\r\n\r\n      this.isLoading = true;\r\n      \r\n      try {\r\n        const response = await this.loginAPI(\r\n          this.loginForm.username,\r\n          this.loginForm.password\r\n        );\r\n        \r\n      \r\n\r\n        // 根据后端返回的数据结构处理\r\n        if (Number(response.code) === 200) {\r\n          this.showMessage('success', '登录成功！');\r\n          console.log('完整响应:', response);\r\n\r\n          // 保存token和用户信息\r\n          const token = response.data?.token || 'login-success-' + Date.now();\r\n          const user = response.data || {\r\n            username: this.loginForm.username,\r\n            role: 'user',\r\n            id: Date.now()\r\n          };\r\n\r\n          // 使用用户store保存登录状态\r\n          this.userStore.login(user, token);\r\n\r\n          console.log('登录成功，准备跳转到商品页');\r\n          this.$router.push('/goods');\r\n          \r\n        } else {\r\n          // 登录失败\r\n          const errorMsg = response.message || response.msg || '登录失败，请重试';\r\n          this.showMessage('error', errorMsg);\r\n        }\r\n        \r\n      } catch (error) {\r\n        console.error('登录错误:', error);\r\n        this.showMessage('error', error.message || '网络错误，请稍后重试');\r\n      } finally {\r\n        this.isLoading = false;\r\n      }\r\n    },\r\n\r\n    // 处理注册\r\n    async handleRegister() {\r\n      if (this.registerForm.password !== this.registerForm.confirmPassword) {\r\n        this.showMessage('error', '两次输入的密码不一致');\r\n        return;\r\n      }\r\n\r\n      this.isRegistering = true;\r\n\r\n      try {\r\n        const response = await this.registerAPI({\r\n          username: this.registerForm.username,\r\n          password: this.registerForm.password\r\n        });\r\n\r\n        // 根据后端返回的数据结构处理\r\n        if (Number(response.code) === 200) {\r\n          this.showMessage('success', '注册成功！请登录');\r\n          this.closeRegisterForm();\r\n          this.resetRegisterForm();\r\n        } else {\r\n          const errorMsg = response.message || response.msg || '注册失败，请重试';\r\n          this.showMessage('error', errorMsg);\r\n        }\r\n\r\n      } catch (error) {\r\n        console.error('注册错误:', error);\r\n        this.showMessage('error', error.message || '网络错误，请稍后重试');\r\n      } finally {\r\n        this.isRegistering = false;\r\n      }\r\n    },\r\n\r\n    // 显示注册表单\r\n    showRegisterForm() {\r\n      this.showRegister = true;\r\n    },\r\n\r\n    // 关闭注册表单\r\n    closeRegisterForm() {\r\n      this.showRegister = false;\r\n    },\r\n\r\n    // 重置注册表单\r\n    resetRegisterForm() {\r\n      this.registerForm = {\r\n        username: '',\r\n        password: '',\r\n        confirmPassword: ''\r\n      };\r\n    },\r\n\r\n    // 忘记密码\r\n    showForgotPassword() {\r\n      this.showMessage('info', '请联系管理员重置密码');\r\n    },\r\n\r\n    // 第三方登录\r\n    thirdPartyLogin(type) {\r\n      this.showMessage('info', `${type}登录功能开发中...`);\r\n    },\r\n\r\n    // 显示消息\r\n    showMessage(type, text) {\r\n      this.message = { show: true, type, text };\r\n      setTimeout(() => {\r\n        this.message.show = false;\r\n      }, 3000);\r\n    }\r\n  },\r\n\r\n  created() {\r\n    // 初始化用户store\r\n    this.userStore = useUserStore();\r\n\r\n    // 检查是否已经登录\r\n    this.userStore.restoreLoginState();\r\n    if (this.userStore.isAuthenticated) {\r\n      console.log('用户已登录，跳转到商品页');\r\n      this.$router.push('/goods');\r\n    }\r\n  },\r\n\r\n  mounted() {},\r\n\r\n  beforeUpdate() {},\r\n\r\n  updated() {},\r\n\r\n  beforeUnmount() {},\r\n\r\n  unmounted() {},\r\n\r\n  activated() {},\r\n\r\n  deactivated() {}\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n* {\r\n  margin: 0;\r\n  padding: 0;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.login-container {\r\n  min-height: 100vh;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  padding: 20px;\r\n  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;\r\n}\r\n\r\n.login-card {\r\n  background: white;\r\n  border-radius: 20px;\r\n  padding: 40px;\r\n  width: 100%;\r\n  max-width: 400px;\r\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\r\n  position: relative;\r\n}\r\n\r\n.login-header {\r\n  text-align: center;\r\n  margin-bottom: 30px;\r\n}\r\n\r\n.login-header h2 {\r\n  color: #333;\r\n  font-size: 24px;\r\n  font-weight: 600;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.subtitle {\r\n  color: #666;\r\n  font-size: 14px;\r\n}\r\n\r\n.login-form {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.form-group {\r\n  margin-bottom: 20px;\r\n  position: relative;\r\n}\r\n\r\n.form-input {\r\n  width: 100%;\r\n  padding: 15px;\r\n  border: 2px solid #e1e5e9;\r\n  border-radius: 10px;\r\n  font-size: 14px;\r\n  transition: all 0.3s ease;\r\n  outline: none;\r\n}\r\n\r\n.form-input:focus {\r\n  border-color: #667eea;\r\n  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\r\n}\r\n\r\n.form-input.error {\r\n  border-color: #ff4757;\r\n}\r\n\r\n.error-text {\r\n  color: #ff4757;\r\n  font-size: 12px;\r\n  position: absolute;\r\n  top: 100%;\r\n  left: 0;\r\n  margin-top: 5px;\r\n}\r\n\r\n.form-options {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 25px;\r\n}\r\n\r\n.checkbox-wrapper {\r\n  display: flex;\r\n  align-items: center;\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n  color: #666;\r\n}\r\n\r\n.checkbox-wrapper input[type=\"checkbox\"] {\r\n  margin-right: 8px;\r\n}\r\n\r\n.forgot-link {\r\n  color: #667eea;\r\n  text-decoration: none;\r\n  font-size: 14px;\r\n  transition: color 0.3s ease;\r\n}\r\n\r\n.forgot-link:hover {\r\n  color: #5a67d8;\r\n}\r\n\r\n.login-btn {\r\n  width: 100%;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n  border: none;\r\n  padding: 15px;\r\n  border-radius: 10px;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.login-btn:hover:not(:disabled) {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);\r\n}\r\n\r\n.login-btn:disabled {\r\n  opacity: 0.7;\r\n  cursor: not-allowed;\r\n}\r\n\r\n.register-section {\r\n  text-align: center;\r\n  margin: 20px 0;\r\n  font-size: 14px;\r\n  color: #666;\r\n}\r\n\r\n.register-btn {\r\n  background: none;\r\n  border: none;\r\n  color: #667eea;\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n  margin-left: 5px;\r\n  text-decoration: underline;\r\n}\r\n\r\n.divider {\r\n  text-align: center;\r\n  margin: 30px 0 20px;\r\n  position: relative;\r\n  color: #999;\r\n  font-size: 14px;\r\n}\r\n\r\n.divider::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 0;\r\n  right: 0;\r\n  height: 1px;\r\n  background: #e1e5e9;\r\n  z-index: 1;\r\n}\r\n\r\n.divider span {\r\n  background: white;\r\n  padding: 0 15px;\r\n  position: relative;\r\n  z-index: 2;\r\n}\r\n\r\n.social-login {\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: 15px;\r\n}\r\n\r\n.social-btn {\r\n  width: 50px;\r\n  height: 50px;\r\n  border: none;\r\n  border-radius: 50%;\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.social-btn:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.social-btn.wechat {\r\n  background: #07c160;\r\n}\r\n\r\n.social-btn.weibo {\r\n  background: #e6162d;\r\n}\r\n\r\n.social-btn.qq {\r\n  background: #1296db;\r\n}\r\n\r\n.social-icon {\r\n  width: 24px;\r\n  height: 24px;\r\n  border-radius: 50%;\r\n}\r\n\r\n.wechat-icon {\r\n  background: #fff;\r\n}\r\n\r\n.weibo-icon {\r\n  background: #fff;\r\n}\r\n\r\n.qq-icon {\r\n  background: #fff;\r\n}\r\n\r\n/* 弹窗样式 */\r\n.modal-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.5);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  z-index: 1000;\r\n}\r\n\r\n.modal-content {\r\n  background: white;\r\n  border-radius: 15px;\r\n  padding: 30px;\r\n  width: 90%;\r\n  max-width: 400px;\r\n  position: relative;\r\n}\r\n\r\n.modal-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.modal-header h3 {\r\n  color: #333;\r\n  font-size: 20px;\r\n}\r\n\r\n.close-btn {\r\n  background: none;\r\n  border: none;\r\n  font-size: 24px;\r\n  cursor: pointer;\r\n  color: #999;\r\n}\r\n\r\n.register-submit-btn {\r\n  width: 100%;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n  border: none;\r\n  padding: 15px;\r\n  border-radius: 10px;\r\n  font-size: 16px;\r\n  cursor: pointer;\r\n  margin-top: 10px;\r\n}\r\n\r\n.register-submit-btn:hover:not(:disabled) {\r\n  opacity: 0.9;\r\n}\r\n\r\n/* 消息提示 */\r\n.message {\r\n  position: fixed;\r\n  top: 20px;\r\n  right: 20px;\r\n  padding: 15px 20px;\r\n  border-radius: 8px;\r\n  color: white;\r\n  font-weight: 500;\r\n  z-index: 2000;\r\n  animation: slideIn 0.3s ease;\r\n}\r\n\r\n.message.success {\r\n  background: #27ae60;\r\n}\r\n\r\n.message.error {\r\n  background: #e74c3c;\r\n}\r\n\r\n.message.info {\r\n  background: #3498db;\r\n}\r\n\r\n@keyframes slideIn {\r\n  from {\r\n    transform: translateX(100%);\r\n    opacity: 0;\r\n  }\r\n  to {\r\n    transform: translateX(0);\r\n    opacity: 1;\r\n  }\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 480px) {\r\n  .login-card {\r\n    padding: 30px 20px;\r\n    margin: 10px;\r\n  }\r\n  \r\n  .login-header h2 {\r\n    font-size: 20px;\r\n  }\r\n}\r\n</style>"], "mappings": ";AAwHA,OAAOA,GAAE,MAAO,QAAQ;AACxB,SAASC,YAAW,QAAS,eAAe;AAE5C,eAAe;EACbC,IAAI,EAAE,WAAW;EAEjBC,UAAU,EAAE,CAAC,CAAC;EAEdC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,SAAS,EAAE,IAAI;MACfC,SAAS,EAAE;QACTC,QAAQ,EAAE,EAAE;QACZC,QAAQ,EAAE;MACZ,CAAC;MACDC,YAAY,EAAE;QACZF,QAAQ,EAAE,EAAE;QACZG,KAAK,EAAE,EAAE;QACTF,QAAQ,EAAE,EAAE;QACZG,eAAe,EAAE;MACnB,CAAC;MACDC,UAAU,EAAE,KAAK;MACjBC,YAAY,EAAE,KAAK;MACnBC,SAAS,EAAE,KAAK;MAChBC,aAAa,EAAE,KAAK;MACpBC,aAAa,EAAE,EAAE;MACjBC,aAAa,EAAE,EAAE;MACjBC,OAAO,EAAE;QACPC,IAAI,EAAE,KAAK;QACXC,IAAI,EAAE,EAAE;QACRC,IAAI,EAAE;MACR;MACA;IACF,CAAC;EACH,CAAC;EAEDC,QAAQ,EAAE,CAAC,CAAC;EAEZC,KAAK,EAAE,CAAC,CAAC;EAETC,OAAO,EAAE;IACP;IACAC,gBAAgBA,CAAA,EAAG;MACjB,IAAI,CAAC,IAAI,CAACnB,SAAS,CAACC,QAAQ,CAACmB,IAAI,CAAC,CAAC,EAAE;QACnC,IAAI,CAACV,aAAY,GAAI,QAAQ;MAC/B,OAAO;QACL,IAAI,CAACA,aAAY,GAAI,EAAE;MACzB;IACF,CAAC;IAED;IACAW,gBAAgBA,CAAA,EAAG;MACjB,IAAI,CAAC,IAAI,CAACrB,SAAS,CAACE,QAAQ,CAACkB,IAAI,CAAC,CAAC,EAAE;QACnC,IAAI,CAACT,aAAY,GAAI,OAAO;MAC9B,OAAO;QACL,IAAI,CAACA,aAAY,GAAI,EAAE;MACzB;IACF,CAAC;IAED;IACA,MAAMW,QAAQA,CAACrB,QAAQ,EAAEC,QAAQ,EAAE;MACjC,IAAI;QACF,MAAMqB,QAAO,GAAI,MAAMC,KAAK,CAAC,qCAAqC,EAAE;UAClEC,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE;YACP,cAAc,EAAE;UAClB,CAAC;UACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;YACnB5B,QAAQ,EAAEA,QAAQ;YAClBC,QAAQ,EAAER,GAAG,CAACQ,QAAQ;UACxB,CAAC;QACH,CAAC,CAAC;QAEF,IAAI,CAACqB,QAAQ,CAACO,EAAE,EAAE;UAChB,MAAM,IAAIC,KAAK,CAAC,uBAAuBR,QAAQ,CAACS,MAAM,EAAE,CAAC;QAC3D;QAEA,MAAMlC,IAAG,GAAI,MAAMyB,QAAQ,CAACU,IAAI,CAAC,CAAC;QAClC,OAAOnC,IAAI;MACb,EAAE,OAAOoC,KAAK,EAAE;QACd,MAAM,IAAIH,KAAK,CAAC,UAAS,GAAIG,KAAK,CAACtB,OAAO,CAAC;MAC7C;IACF,CAAC;IAED;IACA,MAAMuB,WAAWA,CAACC,QAAQ,EAAE;MAC1B,IAAI;QACF,MAAMb,QAAO,GAAI,MAAMC,KAAK,CAAC,wCAAwC,EAAE;UACrEC,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE;YACP,cAAc,EAAE;UAClB,CAAC;UACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;YACnB5B,QAAQ,EAAEmC,QAAQ,CAACnC,QAAQ;YAC3BC,QAAQ,EAAER,GAAG,CAAC0C,QAAQ,CAAClC,QAAQ;UACjC,CAAC;QACH,CAAC,CAAC;QAEF,IAAI,CAACqB,QAAQ,CAACO,EAAE,EAAE;UAChB,MAAM,IAAIC,KAAK,CAAC,uBAAuBR,QAAQ,CAACS,MAAM,EAAE,CAAC;QAC3D;QAEA,MAAMlC,IAAG,GAAI,MAAMyB,QAAQ,CAACU,IAAI,CAAC,CAAC;QAClC,OAAOnC,IAAI;MACb,EAAE,OAAOoC,KAAK,EAAE;QACd,MAAM,IAAIH,KAAK,CAAC,UAAS,GAAIG,KAAK,CAACtB,OAAO,CAAC;MAC7C;IACF,CAAC;IAED;IACA,MAAMyB,WAAWA,CAAA,EAAG;MAClB,IAAI,CAAClB,gBAAgB,CAAC,CAAC;MACvB,IAAI,CAACE,gBAAgB,CAAC,CAAC;MAEvB,IAAI,IAAI,CAACX,aAAY,IAAK,IAAI,CAACC,aAAa,EAAE;QAC5C;MACF;MAEA,IAAI,CAACH,SAAQ,GAAI,IAAI;MAErB,IAAI;QACF,MAAMe,QAAO,GAAI,MAAM,IAAI,CAACD,QAAQ,CAClC,IAAI,CAACtB,SAAS,CAACC,QAAQ,EACvB,IAAI,CAACD,SAAS,CAACE,QACjB,CAAC;;QAID;QACA,IAAIoC,MAAM,CAACf,QAAQ,CAACgB,IAAI,MAAM,GAAG,EAAE;UACjC,IAAI,CAACC,WAAW,CAAC,SAAS,EAAE,OAAO,CAAC;UACpCC,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEnB,QAAQ,CAAC;;UAE9B;UACA,MAAMoB,KAAI,GAAIpB,QAAQ,CAACzB,IAAI,EAAE6C,KAAI,IAAK,gBAAe,GAAIC,IAAI,CAACC,GAAG,CAAC,CAAC;UACnE,MAAMC,IAAG,GAAIvB,QAAQ,CAACzB,IAAG,IAAK;YAC5BG,QAAQ,EAAE,IAAI,CAACD,SAAS,CAACC,QAAQ;YACjC8C,IAAI,EAAE,MAAM;YACZC,EAAE,EAAEJ,IAAI,CAACC,GAAG,CAAC;UACf,CAAC;;UAED;UACA,IAAI,CAAC9C,SAAS,CAACkD,KAAK,CAACH,IAAI,EAAEH,KAAK,CAAC;UAEjCF,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;UAC5B,IAAI,CAACQ,OAAO,CAACC,IAAI,CAAC,QAAQ,CAAC;QAE7B,OAAO;UACL;UACA,MAAMC,QAAO,GAAI7B,QAAQ,CAACX,OAAM,IAAKW,QAAQ,CAAC8B,GAAE,IAAK,UAAU;UAC/D,IAAI,CAACb,WAAW,CAAC,OAAO,EAAEY,QAAQ,CAAC;QACrC;MAEF,EAAE,OAAOlB,KAAK,EAAE;QACdO,OAAO,CAACP,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;QAC7B,IAAI,CAACM,WAAW,CAAC,OAAO,EAAEN,KAAK,CAACtB,OAAM,IAAK,YAAY,CAAC;MAC1D,UAAU;QACR,IAAI,CAACJ,SAAQ,GAAI,KAAK;MACxB;IACF,CAAC;IAED;IACA,MAAM8C,cAAcA,CAAA,EAAG;MACrB,IAAI,IAAI,CAACnD,YAAY,CAACD,QAAO,KAAM,IAAI,CAACC,YAAY,CAACE,eAAe,EAAE;QACpE,IAAI,CAACmC,WAAW,CAAC,OAAO,EAAE,YAAY,CAAC;QACvC;MACF;MAEA,IAAI,CAAC/B,aAAY,GAAI,IAAI;MAEzB,IAAI;QACF,MAAMc,QAAO,GAAI,MAAM,IAAI,CAACY,WAAW,CAAC;UACtClC,QAAQ,EAAE,IAAI,CAACE,YAAY,CAACF,QAAQ;UACpCC,QAAQ,EAAE,IAAI,CAACC,YAAY,CAACD;QAC9B,CAAC,CAAC;;QAEF;QACA,IAAIoC,MAAM,CAACf,QAAQ,CAACgB,IAAI,MAAM,GAAG,EAAE;UACjC,IAAI,CAACC,WAAW,CAAC,SAAS,EAAE,UAAU,CAAC;UACvC,IAAI,CAACe,iBAAiB,CAAC,CAAC;UACxB,IAAI,CAACC,iBAAiB,CAAC,CAAC;QAC1B,OAAO;UACL,MAAMJ,QAAO,GAAI7B,QAAQ,CAACX,OAAM,IAAKW,QAAQ,CAAC8B,GAAE,IAAK,UAAU;UAC/D,IAAI,CAACb,WAAW,CAAC,OAAO,EAAEY,QAAQ,CAAC;QACrC;MAEF,EAAE,OAAOlB,KAAK,EAAE;QACdO,OAAO,CAACP,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;QAC7B,IAAI,CAACM,WAAW,CAAC,OAAO,EAAEN,KAAK,CAACtB,OAAM,IAAK,YAAY,CAAC;MAC1D,UAAU;QACR,IAAI,CAACH,aAAY,GAAI,KAAK;MAC5B;IACF,CAAC;IAED;IACAgD,gBAAgBA,CAAA,EAAG;MACjB,IAAI,CAAClD,YAAW,GAAI,IAAI;IAC1B,CAAC;IAED;IACAgD,iBAAiBA,CAAA,EAAG;MAClB,IAAI,CAAChD,YAAW,GAAI,KAAK;IAC3B,CAAC;IAED;IACAiD,iBAAiBA,CAAA,EAAG;MAClB,IAAI,CAACrD,YAAW,GAAI;QAClBF,QAAQ,EAAE,EAAE;QACZC,QAAQ,EAAE,EAAE;QACZG,eAAe,EAAE;MACnB,CAAC;IACH,CAAC;IAED;IACAqD,kBAAkBA,CAAA,EAAG;MACnB,IAAI,CAAClB,WAAW,CAAC,MAAM,EAAE,YAAY,CAAC;IACxC,CAAC;IAED;IACAmB,eAAeA,CAAC7C,IAAI,EAAE;MACpB,IAAI,CAAC0B,WAAW,CAAC,MAAM,EAAE,GAAG1B,IAAI,YAAY,CAAC;IAC/C,CAAC;IAED;IACA0B,WAAWA,CAAC1B,IAAI,EAAEC,IAAI,EAAE;MACtB,IAAI,CAACH,OAAM,GAAI;QAAEC,IAAI,EAAE,IAAI;QAAEC,IAAI;QAAEC;MAAK,CAAC;MACzC6C,UAAU,CAAC,MAAM;QACf,IAAI,CAAChD,OAAO,CAACC,IAAG,GAAI,KAAK;MAC3B,CAAC,EAAE,IAAI,CAAC;IACV;EACF,CAAC;EAEDgD,OAAOA,CAAA,EAAG;IACR;IACA,IAAI,CAAC9D,SAAQ,GAAIJ,YAAY,CAAC,CAAC;;IAE/B;IACA,IAAI,CAACI,SAAS,CAAC+D,iBAAiB,CAAC,CAAC;IAClC,IAAI,IAAI,CAAC/D,SAAS,CAACgE,eAAe,EAAE;MAClCtB,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;MAC3B,IAAI,CAACQ,OAAO,CAACC,IAAI,CAAC,QAAQ,CAAC;IAC7B;EACF,CAAC;EAEDa,OAAOA,CAAA,EAAG,CAAC,CAAC;EAEZC,YAAYA,CAAA,EAAG,CAAC,CAAC;EAEjBC,OAAOA,CAAA,EAAG,CAAC,CAAC;EAEZC,aAAaA,CAAA,EAAG,CAAC,CAAC;EAElBC,SAASA,CAAA,EAAG,CAAC,CAAC;EAEdC,SAASA,CAAA,EAAG,CAAC,CAAC;EAEdC,WAAWA,CAAA,EAAG,CAAC;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}