{"ast": null, "code": "import request from '@/utils/request.js';\nexport default {\n  name: 'ProductCategory',\n  data() {\n    return {\n      selectedCategory: null,\n      categories: [],\n      loading: false\n    };\n  },\n  created() {\n    this.fetchCategories();\n  },\n  methods: {\n    // 获取分类数据\n    async fetchCategories() {\n      try {\n        this.loading = true;\n        const response = await request.get('/categoryAPI');\n        if (Array.isArray(response)) {\n          this.categories = response;\n          console.log('获取分类数据成功:', this.categories);\n        } else {\n          // 如果后端没有数据，使用默认分类\n          this.categories = this.getDefaultCategories();\n          console.log('使用默认分类数据');\n        }\n      } catch (error) {\n        console.error('获取分类数据失败:', error);\n        // 使用默认分类作为后备\n        this.categories = this.getDefaultCategories();\n      } finally {\n        this.loading = false;\n      }\n    },\n    // 获取默认分类数据\n    getDefaultCategories() {\n      return [{\n        id: 1,\n        name: '服装'\n      }, {\n        id: 2,\n        name: '鞋类'\n      }, {\n        id: 3,\n        name: '酒类'\n      }, {\n        id: 4,\n        name: '日用百货'\n      }, {\n        id: 5,\n        name: '家用电器'\n      }, {\n        id: 6,\n        name: '数码产品'\n      }];\n    },\n    handleCategoryClick(category) {\n      if (category === null) {\n        this.selectedCategory = null;\n        this.$emit('category-change', {\n          categoryId: null,\n          categoryName: '全部分类'\n        });\n      } else {\n        this.selectedCategory = category.id;\n        this.$emit('category-change', {\n          categoryId: category.id,\n          categoryName: category.name\n        });\n      }\n    },\n    clearCategory() {\n      this.selectedCategory = null;\n      this.$emit('category-change', {\n        categoryId: null,\n        categoryName: '全部分类'\n      });\n    }\n  }\n};", "map": {"version": 3, "names": ["request", "name", "data", "selectedCate<PERSON><PERSON>", "categories", "loading", "created", "fetchCategories", "methods", "response", "get", "Array", "isArray", "console", "log", "getDefaultCategories", "error", "id", "handleCategoryClick", "category", "$emit", "categoryId", "categoryName", "clearCategory"], "sources": ["D:\\2025_down\\project\\shoppingOnline_2025_91\\shoppingOnline_front-2025-91\\src\\components\\ProductCategory.vue"], "sourcesContent": ["<template>\n  <div class=\"product-category\">\n    <el-card class=\"category-card\">\n      <div class=\"category-title\">商品分类</div>\n      <div class=\"category-list\">\n        <el-button\n          :class=\"['category-btn', { 'active': selectedCategory === null }]\"\n          @click=\"handleCategoryClick(null)\"\n        >\n          全部分类\n        </el-button>\n        <el-button\n          v-for=\"category in categories\"\n          :key=\"category.id\"\n          :class=\"['category-btn', { 'active': selectedCategory === category.id }]\"\n          @click=\"handleCategoryClick(category)\"\n        >\n          {{ category.name }}\n        </el-button>\n      </div>\n    </el-card>\n  </div>\n</template>\n\n<script>\nimport request from '@/utils/request.js'\n\nexport default {\n  name: 'ProductCategory',\n  data() {\n    return {\n      selectedCategory: null,\n      categories: [],\n      loading: false\n    };\n  },\n\n  created() {\n    this.fetchCategories();\n  },\n\n  methods: {\n    // 获取分类数据\n    async fetchCategories() {\n      try {\n        this.loading = true;\n        const response = await request.get('/categoryAPI');\n\n        if (Array.isArray(response)) {\n          this.categories = response;\n          console.log('获取分类数据成功:', this.categories);\n        } else {\n          // 如果后端没有数据，使用默认分类\n          this.categories = this.getDefaultCategories();\n          console.log('使用默认分类数据');\n        }\n      } catch (error) {\n        console.error('获取分类数据失败:', error);\n        // 使用默认分类作为后备\n        this.categories = this.getDefaultCategories();\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    // 获取默认分类数据\n    getDefaultCategories() {\n      return [\n        { id: 1, name: '服装' },\n        { id: 2, name: '鞋类' },\n        { id: 3, name: '酒类' },\n        { id: 4, name: '日用百货' },\n        { id: 5, name: '家用电器' },\n        { id: 6, name: '数码产品' }\n      ];\n    },\n\n    handleCategoryClick(category) {\n      if (category === null) {\n        this.selectedCategory = null;\n        this.$emit('category-change', { categoryId: null, categoryName: '全部分类' });\n      } else {\n        this.selectedCategory = category.id;\n        this.$emit('category-change', { categoryId: category.id, categoryName: category.name });\n      }\n    },\n\n    clearCategory() {\n      this.selectedCategory = null;\n      this.$emit('category-change', { categoryId: null, categoryName: '全部分类' });\n    }\n  }\n};\n</script>\n\n<style scoped>\n.product-category {\n  margin-bottom: 20px;\n}\n\n.category-card {\n  border-radius: 8px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);\n}\n\n.category-title {\n  font-size: 16px;\n  font-weight: 500;\n  margin-bottom: 15px;\n  color: #303133;\n  padding-bottom: 10px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.category-list {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 10px;\n}\n\n.category-btn {\n  padding: 8px 16px;\n  border-radius: 20px;\n  background: #f5f5f5;\n  color: #606266;\n  border: none;\n  transition: all 0.3s;\n}\n\n.category-btn:hover {\n  background: #e9e9e9;\n  color: #409eff;\n}\n\n.category-btn.active {\n  background: #409eff;\n  color: white;\n}\n</style>"], "mappings": "AAyBA,OAAOA,OAAM,MAAO,oBAAmB;AAEvC,eAAe;EACbC,IAAI,EAAE,iBAAiB;EACvBC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,gBAAgB,EAAE,IAAI;MACtBC,UAAU,EAAE,EAAE;MACdC,OAAO,EAAE;IACX,CAAC;EACH,CAAC;EAEDC,OAAOA,CAAA,EAAG;IACR,IAAI,CAACC,eAAe,CAAC,CAAC;EACxB,CAAC;EAEDC,OAAO,EAAE;IACP;IACA,MAAMD,eAAeA,CAAA,EAAG;MACtB,IAAI;QACF,IAAI,CAACF,OAAM,GAAI,IAAI;QACnB,MAAMI,QAAO,GAAI,MAAMT,OAAO,CAACU,GAAG,CAAC,cAAc,CAAC;QAElD,IAAIC,KAAK,CAACC,OAAO,CAACH,QAAQ,CAAC,EAAE;UAC3B,IAAI,CAACL,UAAS,GAAIK,QAAQ;UAC1BI,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE,IAAI,CAACV,UAAU,CAAC;QAC3C,OAAO;UACL;UACA,IAAI,CAACA,UAAS,GAAI,IAAI,CAACW,oBAAoB,CAAC,CAAC;UAC7CF,OAAO,CAACC,GAAG,CAAC,UAAU,CAAC;QACzB;MACF,EAAE,OAAOE,KAAK,EAAE;QACdH,OAAO,CAACG,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjC;QACA,IAAI,CAACZ,UAAS,GAAI,IAAI,CAACW,oBAAoB,CAAC,CAAC;MAC/C,UAAU;QACR,IAAI,CAACV,OAAM,GAAI,KAAK;MACtB;IACF,CAAC;IAED;IACAU,oBAAoBA,CAAA,EAAG;MACrB,OAAO,CACL;QAAEE,EAAE,EAAE,CAAC;QAAEhB,IAAI,EAAE;MAAK,CAAC,EACrB;QAAEgB,EAAE,EAAE,CAAC;QAAEhB,IAAI,EAAE;MAAK,CAAC,EACrB;QAAEgB,EAAE,EAAE,CAAC;QAAEhB,IAAI,EAAE;MAAK,CAAC,EACrB;QAAEgB,EAAE,EAAE,CAAC;QAAEhB,IAAI,EAAE;MAAO,CAAC,EACvB;QAAEgB,EAAE,EAAE,CAAC;QAAEhB,IAAI,EAAE;MAAO,CAAC,EACvB;QAAEgB,EAAE,EAAE,CAAC;QAAEhB,IAAI,EAAE;MAAO,EACvB;IACH,CAAC;IAEDiB,mBAAmBA,CAACC,QAAQ,EAAE;MAC5B,IAAIA,QAAO,KAAM,IAAI,EAAE;QACrB,IAAI,CAAChB,gBAAe,GAAI,IAAI;QAC5B,IAAI,CAACiB,KAAK,CAAC,iBAAiB,EAAE;UAAEC,UAAU,EAAE,IAAI;UAAEC,YAAY,EAAE;QAAO,CAAC,CAAC;MAC3E,OAAO;QACL,IAAI,CAACnB,gBAAe,GAAIgB,QAAQ,CAACF,EAAE;QACnC,IAAI,CAACG,KAAK,CAAC,iBAAiB,EAAE;UAAEC,UAAU,EAAEF,QAAQ,CAACF,EAAE;UAAEK,YAAY,EAAEH,QAAQ,CAAClB;QAAK,CAAC,CAAC;MACzF;IACF,CAAC;IAEDsB,aAAaA,CAAA,EAAG;MACd,IAAI,CAACpB,gBAAe,GAAI,IAAI;MAC5B,IAAI,CAACiB,KAAK,CAAC,iBAAiB,EAAE;QAAEC,UAAU,EAAE,IAAI;QAAEC,YAAY,EAAE;MAAO,CAAC,CAAC;IAC3E;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}