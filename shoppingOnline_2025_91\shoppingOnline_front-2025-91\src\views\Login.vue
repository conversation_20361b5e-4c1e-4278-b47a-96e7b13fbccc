
<template>
  <div class="login-container">
    <div class="login-card">
      <div class="login-header">
        <h2>欢迎登录</h2>
        <p class="subtitle">在线商城管理系统</p>
      </div>
     <!-- 登录页面 -->
      <form @submit.prevent="handleLogin" class="login-form">
        <div class="form-group">
          <input
            v-model="loginForm.username"
            type="text"
            placeholder="用户名"
            class="form-input"
            :class="{ 'error': usernameError }"
            @blur="validateUsername"
          />
          <span v-if="usernameError" class="error-text">{{ usernameError }}</span>
        </div>

        <div class="form-group">
          <input
            v-model="loginForm.password"
            type="password"
            placeholder="密码"
            class="form-input"
            :class="{ 'error': passwordError }"
            @blur="validatePassword"
          />
          <span v-if="passwordError" class="error-text">{{ passwordError }}</span>
        </div>

        <div class="form-options">
          <label class="checkbox-wrapper">
            <input v-model="rememberMe" type="checkbox" />
            <span class="checkmark"></span>
            记住我
          </label>
          <a href="#" class="forgot-link" @click.prevent="showForgotPassword">忘记密码?</a>
        </div>

        <button type="submit" class="login-btn" :disabled="isLoading">
          {{ isLoading ? '登录中...' : '登 录' }}
        </button>
      </form>

      <div class="register-section">
        <span>没有账号？</span>
        <button @click="showRegisterForm" class="register-btn">注册用户</button>
      </div>

      <div class="divider">
        <span>第三方登录</span>
      </div>

      <div class="social-login">
        <button class="social-btn wechat" @click="thirdPartyLogin('wechat')">
          <div class="social-icon wechat-icon"></div>
        </button>
        <button class="social-btn weibo" @click="thirdPartyLogin('weibo')">
          <div class="social-icon weibo-icon"></div>
        </button>
        <button class="social-btn qq" @click="thirdPartyLogin('qq')">
          <div class="social-icon qq-icon"></div>
        </button>
      </div>
    </div>

    <!-- 注册弹窗 -->
    <div v-if="showRegister" class="modal-overlay" @click="closeRegisterForm">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>用户注册</h3>
          <button @click="closeRegisterForm" class="close-btn">&times;</button>
        </div>
        <form @submit.prevent="handleRegister" class="register-form">
          <div class="form-group">
            <input
              v-model="registerForm.username"
              type="text"
              placeholder="用户名"
              class="form-input"
              required
            />
          </div>
          <div class="form-group">
            <input
              v-model="registerForm.password"
              type="password"
              placeholder="密码"
              class="form-input"
              required
            />
          </div>
          <div class="form-group">
            <input
              v-model="registerForm.confirmPassword"
              type="password"
              placeholder="确认密码"
              class="form-input"
              required
            />
          </div>
          <button type="submit" class="register-submit-btn" :disabled="isRegistering">
            {{ isRegistering ? '注册中...' : '注册' }}
          </button>
        </form>
      </div>
    </div>

    <!-- 消息提示 -->
    <div v-if="message.show" :class="['message', message.type]">
      {{ message.text }}
    </div>
  </div>
</template>

<script>
import md5 from "js-md5";
import { useUserStore } from '@/store/index';

export default {
  name: 'LoginView',

  components: {},

  data() {
    return {
      userStore: null,
      loginForm: {
        username: '',
        password: ''
      },
      registerForm: {
        username: '',
        email: '',
        password: '',
        confirmPassword: ''
      },
      rememberMe: false,
      showRegister: false,
      isLoading: false,
      isRegistering: false,
      usernameError: '',
      passwordError: '',
      message: {
        show: false,
        type: '',
        text: ''
      }
      // 移除模拟用户数据，使用真实API
    };
  },

  computed: {},

  watch: {},

  methods: {
    // 验证用户名
    validateUsername() {
      if (!this.loginForm.username.trim()) {
        this.usernameError = '请输入用户名';
      } else {
        this.usernameError = '';
      }
    },

    // 验证密码
    validatePassword() {
      if (!this.loginForm.password.trim()) {
        this.passwordError = '请输入密码';
      } else {
        this.passwordError = '';
      }
    },

    // 登录API
    async loginAPI(username, password) {
      try {
        const response = await fetch('http://127.0.0.1:9197/userAPI/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            username: username,
            password: md5(password)
          })
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        return data;
      } catch (error) {
        throw new Error('登录请求失败: ' + error.message);
      }
    },

    // 注册API（如果有注册接口的话）
    async registerAPI(userData) {
      try {
        const response = await fetch('http://127.0.0.1:9197/userAPI/register', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            username: userData.username,
            password: md5(userData.password)
          })
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        return data;
      } catch (error) {
        throw new Error('注册请求失败: ' + error.message);
      }
    },

    // 处理登录
    async handleLogin() {
      this.validateUsername();
      this.validatePassword();
      
      if (this.usernameError || this.passwordError) {
        return;
      }

      this.isLoading = true;
      
      try {
        const response = await this.loginAPI(
          this.loginForm.username,
          this.loginForm.password
        );
        
      

        // 根据后端返回的数据结构处理
        if (Number(response.code) === 200) {
          this.showMessage('success', '登录成功！');
          console.log('完整响应:', response);

          // 保存token和用户信息
          const token = response.data?.token || 'login-success-' + Date.now();
          const user = response.data || {
            username: this.loginForm.username,
            role: 'user',
            id: Date.now()
          };

          // 使用用户store保存登录状态
          this.userStore.login(user, token);

          console.log('登录成功，准备跳转到商品页');
          this.$router.push('/goods');
          
        } else {
          // 登录失败
          const errorMsg = response.message || response.msg || '登录失败，请重试';
          this.showMessage('error', errorMsg);
        }
        
      } catch (error) {
        console.error('登录错误:', error);
        this.showMessage('error', error.message || '网络错误，请稍后重试');
      } finally {
        this.isLoading = false;
      }
    },

    // 处理注册
    async handleRegister() {
      if (this.registerForm.password !== this.registerForm.confirmPassword) {
        this.showMessage('error', '两次输入的密码不一致');
        return;
      }

      this.isRegistering = true;

      try {
        const response = await this.registerAPI({
          username: this.registerForm.username,
          password: this.registerForm.password
        });

        // 根据后端返回的数据结构处理
        if (Number(response.code) === 200) {
          this.showMessage('success', '注册成功！请登录');
          this.closeRegisterForm();
          this.resetRegisterForm();
        } else {
          const errorMsg = response.message || response.msg || '注册失败，请重试';
          this.showMessage('error', errorMsg);
        }

      } catch (error) {
        console.error('注册错误:', error);
        this.showMessage('error', error.message || '网络错误，请稍后重试');
      } finally {
        this.isRegistering = false;
      }
    },

    // 显示注册表单
    showRegisterForm() {
      this.showRegister = true;
    },

    // 关闭注册表单
    closeRegisterForm() {
      this.showRegister = false;
    },

    // 重置注册表单
    resetRegisterForm() {
      this.registerForm = {
        username: '',
        password: '',
        confirmPassword: ''
      };
    },

    // 忘记密码
    showForgotPassword() {
      this.showMessage('info', '请联系管理员重置密码');
    },

    // 第三方登录
    thirdPartyLogin(type) {
      this.showMessage('info', `${type}登录功能开发中...`);
    },

    // 显示消息
    showMessage(type, text) {
      this.message = { show: true, type, text };
      setTimeout(() => {
        this.message.show = false;
      }, 3000);
    }
  },

  created() {
    // 初始化用户store
    this.userStore = useUserStore();

    // 检查是否已经登录
    this.userStore.restoreLoginState();
    if (this.userStore.isAuthenticated) {
      console.log('用户已登录，跳转到商品页');
      this.$router.push('/goods');
    }
  },

  mounted() {},

  beforeUpdate() {},

  updated() {},

  beforeUnmount() {},

  unmounted() {},

  activated() {},

  deactivated() {}
}
</script>

<style scoped>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
}

.login-card {
  background: white;
  border-radius: 20px;
  padding: 40px;
  width: 100%;
  max-width: 400px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  position: relative;
}

.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.login-header h2 {
  color: #333;
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 8px;
}

.subtitle {
  color: #666;
  font-size: 14px;
}

.login-form {
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 20px;
  position: relative;
}

.form-input {
  width: 100%;
  padding: 15px;
  border: 2px solid #e1e5e9;
  border-radius: 10px;
  font-size: 14px;
  transition: all 0.3s ease;
  outline: none;
}

.form-input:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-input.error {
  border-color: #ff4757;
}

.error-text {
  color: #ff4757;
  font-size: 12px;
  position: absolute;
  top: 100%;
  left: 0;
  margin-top: 5px;
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
}

.checkbox-wrapper {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 14px;
  color: #666;
}

.checkbox-wrapper input[type="checkbox"] {
  margin-right: 8px;
}

.forgot-link {
  color: #667eea;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.3s ease;
}

.forgot-link:hover {
  color: #5a67d8;
}

.login-btn {
  width: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 15px;
  border-radius: 10px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.login-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
}

.login-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.register-section {
  text-align: center;
  margin: 20px 0;
  font-size: 14px;
  color: #666;
}

.register-btn {
  background: none;
  border: none;
  color: #667eea;
  cursor: pointer;
  font-size: 14px;
  margin-left: 5px;
  text-decoration: underline;
}

.divider {
  text-align: center;
  margin: 30px 0 20px;
  position: relative;
  color: #999;
  font-size: 14px;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: #e1e5e9;
  z-index: 1;
}

.divider span {
  background: white;
  padding: 0 15px;
  position: relative;
  z-index: 2;
}

.social-login {
  display: flex;
  justify-content: center;
  gap: 15px;
}

.social-btn {
  width: 50px;
  height: 50px;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.social-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

.social-btn.wechat {
  background: #07c160;
}

.social-btn.weibo {
  background: #e6162d;
}

.social-btn.qq {
  background: #1296db;
}

.social-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
}

.wechat-icon {
  background: #fff;
}

.weibo-icon {
  background: #fff;
}

.qq-icon {
  background: #fff;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 15px;
  padding: 30px;
  width: 90%;
  max-width: 400px;
  position: relative;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.modal-header h3 {
  color: #333;
  font-size: 20px;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #999;
}

.register-submit-btn {
  width: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 15px;
  border-radius: 10px;
  font-size: 16px;
  cursor: pointer;
  margin-top: 10px;
}

.register-submit-btn:hover:not(:disabled) {
  opacity: 0.9;
}

/* 消息提示 */
.message {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 15px 20px;
  border-radius: 8px;
  color: white;
  font-weight: 500;
  z-index: 2000;
  animation: slideIn 0.3s ease;
}

.message.success {
  background: #27ae60;
}

.message.error {
  background: #e74c3c;
}

.message.info {
  background: #3498db;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 480px) {
  .login-card {
    padding: 30px 20px;
    margin: 10px;
  }
  
  .login-header h2 {
    font-size: 20px;
  }
}
</style>