<template>
  <div class="test-page">
    <h2>功能测试页面</h2>
    
    <!-- 测试轮播图组件 -->
    <div class="test-section">
      <h3>轮播图组件测试</h3>
      <CarouselComponent @carousel-click="handleCarouselClick" />
    </div>
    
    <!-- 测试搜索框组件 -->
    <div class="test-section">
      <h3>搜索框组件测试</h3>
      <SearchBox 
        @search="handleSearch" 
        @search-results="handleSearchResults"
        @clear-search="handleClearSearch"
      />
      
      <!-- 搜索结果显示 -->
      <div v-if="searchResults.length > 0" class="search-results">
        <h4>搜索结果：</h4>
        <div class="results-list">
          <div v-for="item in searchResults" :key="item.id" class="result-item">
            <img :src="item.imgs" :alt="item.name" class="result-image" />
            <div class="result-info">
              <h5>{{ item.name }}</h5>
              <p>¥{{ item.price }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- API测试按钮 -->
    <div class="test-section">
      <h3>API测试</h3>
      <el-button @click="testCarouselAPI" type="primary">测试轮播图API</el-button>
      <el-button @click="testGoodsAPI" type="success">测试商品API</el-button>
      <el-button @click="testSearchAPI" type="warning">测试搜索API</el-button>
    </div>
    
    <!-- 测试结果显示 -->
    <div class="test-section" v-if="testResults">
      <h3>测试结果</h3>
      <pre>{{ testResults }}</pre>
    </div>
  </div>
</template>

<script>
import CarouselComponent from '@/components/Carousel.vue'
import SearchBox from '@/components/SearchBox.vue'
import request from '@/utils/request.js'

export default {
  name: 'TestPage',
  
  components: {
    CarouselComponent,
    SearchBox
  },
  
  data() {
    return {
      searchResults: [],
      testResults: null
    }
  },
  
  methods: {
    handleCarouselClick(item) {
      console.log('轮播图点击:', item)
      this.$message.success(`点击了轮播图商品: ${item.goodName}`)
    },
    
    handleSearch(keyword) {
      console.log('搜索关键词:', keyword)
      this.$message.info(`搜索: ${keyword}`)
    },
    
    handleSearchResults(results) {
      console.log('搜索结果:', results)
      this.searchResults = results.records || []
    },
    
    handleClearSearch() {
      console.log('清除搜索')
      this.searchResults = []
    },
    
    async testCarouselAPI() {
      try {
        const response = await request.get('/carousel')
        this.testResults = JSON.stringify(response, null, 2)
        this.$message.success('轮播图API测试成功')
      } catch (error) {
        this.testResults = `轮播图API错误: ${error.message}`
        this.$message.error('轮播图API测试失败')
      }
    },
    
    async testGoodsAPI() {
      try {
        const response = await request.get('/good')
        this.testResults = JSON.stringify(response, null, 2)
        this.$message.success('商品API测试成功')
      } catch (error) {
        this.testResults = `商品API错误: ${error.message}`
        this.$message.error('商品API测试失败')
      }
    },
    
    async testSearchAPI() {
      try {
        const response = await request.get('/good/page', {
          searchText: '手机',
          pageNum: 1,
          pageSize: 5
        })
        this.testResults = JSON.stringify(response, null, 2)
        this.$message.success('搜索API测试成功')
      } catch (error) {
        this.testResults = `搜索API错误: ${error.message}`
        this.$message.error('搜索API测试失败')
      }
    }
  }
}
</script>

<style scoped>
.test-page {
  max-width: 1000px;
  margin: 20px auto;
  padding: 20px;
}

.test-section {
  margin-bottom: 40px;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: white;
}

.test-section h3 {
  margin-top: 0;
  color: #333;
  border-bottom: 2px solid #409eff;
  padding-bottom: 10px;
}

.search-results {
  margin-top: 20px;
}

.results-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
  margin-top: 15px;
}

.result-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

.result-image {
  width: 50px;
  height: 50px;
  object-fit: cover;
  border-radius: 4px;
}

.result-info h5 {
  margin: 0 0 5px;
  font-size: 14px;
}

.result-info p {
  margin: 0;
  color: #e60000;
  font-weight: bold;
}

pre {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
}
</style>
