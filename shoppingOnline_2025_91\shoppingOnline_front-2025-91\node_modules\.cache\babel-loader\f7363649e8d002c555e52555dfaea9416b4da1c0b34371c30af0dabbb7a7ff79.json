{"ast": null, "code": "import request from '@/utils/request.js';\nexport default {\n  name: 'CarouselComponent',\n  data() {\n    return {\n      carouselList: [],\n      currentIndex: 0,\n      loading: true,\n      autoPlayTimer: null,\n      autoPlayInterval: 4000 // 4秒自动切换\n    };\n  },\n  mounted() {\n    this.fetchCarouselData();\n\n    // 防止 ResizeObserver 错误\n    this.$nextTick(() => {\n      // 确保DOM完全渲染后再执行相关操作\n    });\n  },\n  beforeUnmount() {\n    this.clearAutoPlay();\n  },\n  methods: {\n    // 获取轮播图数据\n    async fetchCarouselData() {\n      try {\n        this.loading = true;\n\n        // 临时强制使用模拟数据来测试图片显示\n        this.carouselList = this.getMockCarouselWithLocalImages();\n        console.log('轮播图强制使用本地图片的模拟数据:', this.carouselList);\n\n        // 注释掉API调用，直接使用模拟数据\n        // const response = await request.get('/carousel')\n        // if (Array.isArray(response) && response.length > 0) {\n        //   this.carouselList = response\n        // } else {\n        //   this.carouselList = this.getMockCarouselWithLocalImages()\n        //   console.log('轮播图使用本地图片的模拟数据')\n        // }\n\n        if (this.carouselList.length > 0) {\n          this.startAutoPlay();\n        }\n      } catch (error) {\n        console.error('获取轮播图数据失败:', error);\n        // 使用本地图片的模拟数据作为后备\n        this.carouselList = this.getMockCarouselWithLocalImages();\n        console.log('轮播图API请求失败，使用本地图片的模拟数据:', this.carouselList);\n        if (this.carouselList.length > 0) {\n          this.startAutoPlay();\n        }\n      } finally {\n        this.loading = false;\n      }\n    },\n    // 获取使用本地图片的模拟轮播图数据\n    getMockCarouselWithLocalImages() {\n      return [{\n        id: 1,\n        goodId: 1,\n        goodName: '女上衣',\n        img: '/images/women-top.jpg',\n        showOrder: 1\n      }, {\n        id: 2,\n        goodId: 2,\n        goodName: '休闲鞋',\n        img: '/images/casual-shoes.jpg',\n        showOrder: 2\n      }, {\n        id: 3,\n        goodId: 3,\n        goodName: '威士忌 大瓶',\n        img: '/images/whiskey.jpg',\n        showOrder: 3\n      }];\n    },\n    // 下一张\n    nextSlide() {\n      if (this.carouselList.length === 0) return;\n      this.currentIndex = (this.currentIndex + 1) % this.carouselList.length;\n    },\n    // 上一张\n    prevSlide() {\n      if (this.carouselList.length === 0) return;\n      this.currentIndex = this.currentIndex === 0 ? this.carouselList.length - 1 : this.currentIndex - 1;\n    },\n    // 跳转到指定幻灯片\n    goToSlide(index) {\n      this.currentIndex = index;\n      this.resetAutoPlay();\n    },\n    // 开始自动播放\n    startAutoPlay() {\n      if (this.carouselList.length <= 1) return;\n      this.autoPlayTimer = setInterval(() => {\n        this.nextSlide();\n      }, this.autoPlayInterval);\n    },\n    // 清除自动播放\n    clearAutoPlay() {\n      if (this.autoPlayTimer) {\n        clearInterval(this.autoPlayTimer);\n        this.autoPlayTimer = null;\n      }\n    },\n    // 重置自动播放\n    resetAutoPlay() {\n      this.clearAutoPlay();\n      this.startAutoPlay();\n    },\n    // 点击轮播图项目\n    handleCarouselClick(item) {\n      // 可以跳转到商品详情页或触发其他事件\n      this.$emit('carousel-click', item);\n      console.log('点击轮播图商品:', item.goodName);\n    }\n  }\n};", "map": {"version": 3, "names": ["request", "name", "data", "carouselList", "currentIndex", "loading", "autoPlayTimer", "autoPlayInterval", "mounted", "fetchCarouselData", "$nextTick", "beforeUnmount", "clearAutoPlay", "methods", "getMockCarouselWithLocalImages", "console", "log", "length", "startAutoPlay", "error", "id", "goodId", "<PERSON><PERSON><PERSON>", "img", "showOrder", "nextSlide", "prevSlide", "goToSlide", "index", "resetAutoPlay", "setInterval", "clearInterval", "handleCarouselClick", "item", "$emit"], "sources": ["D:\\2025_down\\project\\shoppingOnline_2025_91\\shoppingOnline_front-2025-91\\src\\components\\Carousel.vue"], "sourcesContent": ["<template>\n  <div class=\"carousel-container\">\n    <div class=\"carousel-wrapper\" v-if=\"carouselList.length > 0\">\n      <div class=\"carousel-content\" :style=\"{ transform: `translateX(-${currentIndex * 100}%)` }\">\n        <div\n          class=\"carousel-item\"\n          v-for=\"item in carouselList\"\n          :key=\"item.id\"\n          @click=\"handleCarouselClick(item)\"\n        >\n          <img\n            :src=\"item.img\"\n            :alt=\"item.goodName\"\n            class=\"carousel-image\"\n            @error=\"handleImageError\"\n            @load=\"handleImageLoad\"\n          />\n          <div class=\"carousel-overlay\">\n            <h3 class=\"carousel-title\">{{ item.goodName }}</h3>\n          </div>\n        </div>\n      </div>\n      \n      <!-- 指示器 -->\n      <div class=\"carousel-indicators\">\n        <span \n          v-for=\"(item, index) in carouselList\" \n          :key=\"index\"\n          :class=\"['indicator', { active: index === currentIndex }]\"\n          @click=\"goToSlide(index)\"\n        ></span>\n      </div>\n      \n      <!-- 导航按钮 -->\n      <button class=\"carousel-btn prev-btn\" @click=\"prevSlide\" v-if=\"carouselList.length > 1\">\n        <i class=\"el-icon-arrow-left\"></i>\n      </button>\n      <button class=\"carousel-btn next-btn\" @click=\"nextSlide\" v-if=\"carouselList.length > 1\">\n        <i class=\"el-icon-arrow-right\"></i>\n      </button>\n    </div>\n    \n    <!-- 加载状态 -->\n    <div v-else-if=\"loading\" class=\"carousel-loading\">\n      <el-skeleton animated>\n        <template #template>\n          <el-skeleton-item variant=\"image\" style=\"width: 100%; height: 300px;\" />\n        </template>\n      </el-skeleton>\n    </div>\n  </div>\n</template>\n\n<script>\nimport request from '@/utils/request.js'\n\nexport default {\n  name: 'CarouselComponent',\n  \n  data() {\n    return {\n      carouselList: [],\n      currentIndex: 0,\n      loading: true,\n      autoPlayTimer: null,\n      autoPlayInterval: 4000 // 4秒自动切换\n    }\n  },\n  \n  mounted() {\n    this.fetchCarouselData()\n\n    // 防止 ResizeObserver 错误\n    this.$nextTick(() => {\n      // 确保DOM完全渲染后再执行相关操作\n    })\n  },\n  \n  beforeUnmount() {\n    this.clearAutoPlay()\n  },\n  \n  methods: {\n    // 获取轮播图数据\n    async fetchCarouselData() {\n      try {\n        this.loading = true\n\n        // 临时强制使用模拟数据来测试图片显示\n        this.carouselList = this.getMockCarouselWithLocalImages()\n        console.log('轮播图强制使用本地图片的模拟数据:', this.carouselList)\n\n        // 注释掉API调用，直接使用模拟数据\n        // const response = await request.get('/carousel')\n        // if (Array.isArray(response) && response.length > 0) {\n        //   this.carouselList = response\n        // } else {\n        //   this.carouselList = this.getMockCarouselWithLocalImages()\n        //   console.log('轮播图使用本地图片的模拟数据')\n        // }\n\n        if (this.carouselList.length > 0) {\n          this.startAutoPlay()\n        }\n      } catch (error) {\n        console.error('获取轮播图数据失败:', error)\n        // 使用本地图片的模拟数据作为后备\n        this.carouselList = this.getMockCarouselWithLocalImages()\n        console.log('轮播图API请求失败，使用本地图片的模拟数据:', this.carouselList)\n\n        if (this.carouselList.length > 0) {\n          this.startAutoPlay()\n        }\n      } finally {\n        this.loading = false\n      }\n    },\n\n    // 获取使用本地图片的模拟轮播图数据\n    getMockCarouselWithLocalImages() {\n      return [\n        {\n          id: 1,\n          goodId: 1,\n          goodName: '女上衣',\n          img: '/images/women-top.jpg',\n          showOrder: 1\n        },\n        {\n          id: 2,\n          goodId: 2,\n          goodName: '休闲鞋',\n          img: '/images/casual-shoes.jpg',\n          showOrder: 2\n        },\n        {\n          id: 3,\n          goodId: 3,\n          goodName: '威士忌 大瓶',\n          img: '/images/whiskey.jpg',\n          showOrder: 3\n        }\n      ]\n    },\n    \n    // 下一张\n    nextSlide() {\n      if (this.carouselList.length === 0) return\n      this.currentIndex = (this.currentIndex + 1) % this.carouselList.length\n    },\n    \n    // 上一张\n    prevSlide() {\n      if (this.carouselList.length === 0) return\n      this.currentIndex = this.currentIndex === 0 ? this.carouselList.length - 1 : this.currentIndex - 1\n    },\n    \n    // 跳转到指定幻灯片\n    goToSlide(index) {\n      this.currentIndex = index\n      this.resetAutoPlay()\n    },\n    \n    // 开始自动播放\n    startAutoPlay() {\n      if (this.carouselList.length <= 1) return\n      this.autoPlayTimer = setInterval(() => {\n        this.nextSlide()\n      }, this.autoPlayInterval)\n    },\n    \n    // 清除自动播放\n    clearAutoPlay() {\n      if (this.autoPlayTimer) {\n        clearInterval(this.autoPlayTimer)\n        this.autoPlayTimer = null\n      }\n    },\n    \n    // 重置自动播放\n    resetAutoPlay() {\n      this.clearAutoPlay()\n      this.startAutoPlay()\n    },\n    \n    // 点击轮播图项目\n    handleCarouselClick(item) {\n      // 可以跳转到商品详情页或触发其他事件\n      this.$emit('carousel-click', item)\n      console.log('点击轮播图商品:', item.goodName)\n    }\n  }\n}\n</script>\n\n<style scoped>\n.carousel-container {\n  width: 100%;\n  margin-bottom: 30px;\n}\n\n.carousel-wrapper {\n  position: relative;\n  width: 100%;\n  height: 300px;\n  overflow: hidden;\n  border-radius: 12px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n}\n\n.carousel-content {\n  display: flex;\n  width: 100%;\n  height: 100%;\n  transition: transform 0.5s ease-in-out;\n  will-change: transform;\n}\n\n.carousel-item {\n  flex: 0 0 100%;\n  position: relative;\n  cursor: pointer;\n}\n\n.carousel-image {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.carousel-overlay {\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));\n  color: white;\n  padding: 20px;\n}\n\n.carousel-title {\n  margin: 0;\n  font-size: 24px;\n  font-weight: bold;\n}\n\n.carousel-indicators {\n  position: absolute;\n  bottom: 15px;\n  left: 50%;\n  transform: translateX(-50%);\n  display: flex;\n  gap: 8px;\n}\n\n.indicator {\n  width: 12px;\n  height: 12px;\n  border-radius: 50%;\n  background: rgba(255, 255, 255, 0.5);\n  cursor: pointer;\n  transition: background 0.3s;\n}\n\n.indicator.active {\n  background: white;\n}\n\n.carousel-btn {\n  position: absolute;\n  top: 50%;\n  transform: translateY(-50%);\n  background: rgba(0, 0, 0, 0.5);\n  color: white;\n  border: none;\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: background 0.3s;\n  font-size: 18px;\n}\n\n.carousel-btn:hover {\n  background: rgba(0, 0, 0, 0.7);\n}\n\n.prev-btn {\n  left: 15px;\n}\n\n.next-btn {\n  right: 15px;\n}\n\n.carousel-loading {\n  width: 100%;\n  height: 300px;\n  border-radius: 12px;\n  overflow: hidden;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .carousel-wrapper {\n    height: 200px;\n  }\n  \n  .carousel-title {\n    font-size: 18px;\n  }\n  \n  .carousel-btn {\n    width: 35px;\n    height: 35px;\n    font-size: 16px;\n  }\n  \n  .prev-btn {\n    left: 10px;\n  }\n  \n  .next-btn {\n    right: 10px;\n  }\n}\n</style>\n"], "mappings": "AAsDA,OAAOA,OAAM,MAAO,oBAAmB;AAEvC,eAAe;EACbC,IAAI,EAAE,mBAAmB;EAEzBC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,YAAY,EAAE,EAAE;MAChBC,YAAY,EAAE,CAAC;MACfC,OAAO,EAAE,IAAI;MACbC,aAAa,EAAE,IAAI;MACnBC,gBAAgB,EAAE,IAAG,CAAE;IACzB;EACF,CAAC;EAEDC,OAAOA,CAAA,EAAG;IACR,IAAI,CAACC,iBAAiB,CAAC;;IAEvB;IACA,IAAI,CAACC,SAAS,CAAC,MAAM;MACnB;IAAA,CACD;EACH,CAAC;EAEDC,aAAaA,CAAA,EAAG;IACd,IAAI,CAACC,aAAa,CAAC;EACrB,CAAC;EAEDC,OAAO,EAAE;IACP;IACA,MAAMJ,iBAAiBA,CAAA,EAAG;MACxB,IAAI;QACF,IAAI,CAACJ,OAAM,GAAI,IAAG;;QAElB;QACA,IAAI,CAACF,YAAW,GAAI,IAAI,CAACW,8BAA8B,CAAC;QACxDC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAACb,YAAY;;QAElD;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA,IAAI,IAAI,CAACA,YAAY,CAACc,MAAK,GAAI,CAAC,EAAE;UAChC,IAAI,CAACC,aAAa,CAAC;QACrB;MACF,EAAE,OAAOC,KAAK,EAAE;QACdJ,OAAO,CAACI,KAAK,CAAC,YAAY,EAAEA,KAAK;QACjC;QACA,IAAI,CAAChB,YAAW,GAAI,IAAI,CAACW,8BAA8B,CAAC;QACxDC,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE,IAAI,CAACb,YAAY;QAExD,IAAI,IAAI,CAACA,YAAY,CAACc,MAAK,GAAI,CAAC,EAAE;UAChC,IAAI,CAACC,aAAa,CAAC;QACrB;MACF,UAAU;QACR,IAAI,CAACb,OAAM,GAAI,KAAI;MACrB;IACF,CAAC;IAED;IACAS,8BAA8BA,CAAA,EAAG;MAC/B,OAAO,CACL;QACEM,EAAE,EAAE,CAAC;QACLC,MAAM,EAAE,CAAC;QACTC,QAAQ,EAAE,KAAK;QACfC,GAAG,EAAE,uBAAuB;QAC5BC,SAAS,EAAE;MACb,CAAC,EACD;QACEJ,EAAE,EAAE,CAAC;QACLC,MAAM,EAAE,CAAC;QACTC,QAAQ,EAAE,KAAK;QACfC,GAAG,EAAE,0BAA0B;QAC/BC,SAAS,EAAE;MACb,CAAC,EACD;QACEJ,EAAE,EAAE,CAAC;QACLC,MAAM,EAAE,CAAC;QACTC,QAAQ,EAAE,QAAQ;QAClBC,GAAG,EAAE,qBAAqB;QAC1BC,SAAS,EAAE;MACb,EACF;IACF,CAAC;IAED;IACAC,SAASA,CAAA,EAAG;MACV,IAAI,IAAI,CAACtB,YAAY,CAACc,MAAK,KAAM,CAAC,EAAE;MACpC,IAAI,CAACb,YAAW,GAAI,CAAC,IAAI,CAACA,YAAW,GAAI,CAAC,IAAI,IAAI,CAACD,YAAY,CAACc,MAAK;IACvE,CAAC;IAED;IACAS,SAASA,CAAA,EAAG;MACV,IAAI,IAAI,CAACvB,YAAY,CAACc,MAAK,KAAM,CAAC,EAAE;MACpC,IAAI,CAACb,YAAW,GAAI,IAAI,CAACA,YAAW,KAAM,IAAI,IAAI,CAACD,YAAY,CAACc,MAAK,GAAI,IAAI,IAAI,CAACb,YAAW,GAAI;IACnG,CAAC;IAED;IACAuB,SAASA,CAACC,KAAK,EAAE;MACf,IAAI,CAACxB,YAAW,GAAIwB,KAAI;MACxB,IAAI,CAACC,aAAa,CAAC;IACrB,CAAC;IAED;IACAX,aAAaA,CAAA,EAAG;MACd,IAAI,IAAI,CAACf,YAAY,CAACc,MAAK,IAAK,CAAC,EAAE;MACnC,IAAI,CAACX,aAAY,GAAIwB,WAAW,CAAC,MAAM;QACrC,IAAI,CAACL,SAAS,CAAC;MACjB,CAAC,EAAE,IAAI,CAAClB,gBAAgB;IAC1B,CAAC;IAED;IACAK,aAAaA,CAAA,EAAG;MACd,IAAI,IAAI,CAACN,aAAa,EAAE;QACtByB,aAAa,CAAC,IAAI,CAACzB,aAAa;QAChC,IAAI,CAACA,aAAY,GAAI,IAAG;MAC1B;IACF,CAAC;IAED;IACAuB,aAAaA,CAAA,EAAG;MACd,IAAI,CAACjB,aAAa,CAAC;MACnB,IAAI,CAACM,aAAa,CAAC;IACrB,CAAC;IAED;IACAc,mBAAmBA,CAACC,IAAI,EAAE;MACxB;MACA,IAAI,CAACC,KAAK,CAAC,gBAAgB,EAAED,IAAI;MACjClB,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEiB,IAAI,CAACX,QAAQ;IACvC;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}