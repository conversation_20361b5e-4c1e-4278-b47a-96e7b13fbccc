// utils/request.js
import axios from 'axios'

// 创建 axios 实例
const request = axios.create({
  baseURL: 'http://127.0.0.1:9197/api', // 后端基础路径
  timeout: 10000, // 超时时间
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器（可添加 token 等）
request.interceptors.request.use(
  config => {
    // 例如：携带 token
    // const token = localStorage.getItem('token')
    // if (token) {
    //   config.headers.Authorization = `Bearer ${token}`
    // }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  response => {
    const res = response.data

    // ✅ 正确判断：code 为字符串 "200" 表示成功
    if (res.code !== '200') {
      const errorMsg = res.msg || '请求失败'
      console.error('业务错误:', errorMsg)
      return Promise.reject(new Error(errorMsg))
    }

    // ✅ 返回真正的业务数据
    return res.data
  },
  error => {
    // 网络错误、超时、4xx/5xx 等
    console.error('响应错误:', error.response?.data || error.message)
    return Promise.reject(error)
  }
)

// 封装 GET 和 POST 方法
export default {
  get(url, params = {}) {
    return request.get(url, { params })
  },
  post(url, data = {}) {
    return request.post(url, data)
  }
}