{"ast": null, "code": "/*\r\n * @Description: \r\n * @Author: JACK\r\n * @Date: 2025年8月29日11:12:13\r\n */\nimport { defineStore } from 'pinia';\nimport { ref, computed } from 'vue';\n\n// 定义主要的 store\nexport const useMainStore = defineStore('main', () => {\n  const baseApi = 'http://localhost:9197';\n  return {\n    baseApi\n  };\n});\n\n// 定义用户状态 store\nexport const useUserStore = defineStore('user', () => {\n  // 状态\n  const isLoggedIn = ref(false);\n  const userInfo = ref(null);\n  const token = ref(null);\n\n  // 计算属性\n  const username = computed(() => userInfo.value?.username || '');\n  const userRole = computed(() => userInfo.value?.role || '');\n  const isAuthenticated = computed(() => isLoggedIn.value && !!token.value);\n\n  // 方法\n  const login = (user, userToken) => {\n    isLoggedIn.value = true;\n    userInfo.value = user;\n    token.value = userToken;\n\n    // 保存到localStorage\n    localStorage.setItem('isLoggedIn', 'true');\n    localStorage.setItem('userInfo', JSON.stringify(user));\n    localStorage.setItem('token', userToken);\n  };\n  const logout = () => {\n    isLoggedIn.value = false;\n    userInfo.value = null;\n    token.value = null;\n\n    // 清除localStorage\n    localStorage.removeItem('isLoggedIn');\n    localStorage.removeItem('userInfo');\n    localStorage.removeItem('token');\n    localStorage.removeItem('username'); // 兼容旧版本\n  };\n  const restoreLoginState = () => {\n    const savedIsLoggedIn = localStorage.getItem('isLoggedIn') === 'true';\n    const savedUserInfo = localStorage.getItem('userInfo');\n    const savedToken = localStorage.getItem('token');\n    if (savedIsLoggedIn && savedUserInfo && savedToken) {\n      isLoggedIn.value = true;\n      userInfo.value = JSON.parse(savedUserInfo);\n      token.value = savedToken;\n    }\n  };\n  return {\n    // 状态\n    isLoggedIn,\n    userInfo,\n    token,\n    // 计算属性\n    username,\n    userRole,\n    isAuthenticated,\n    // 方法\n    login,\n    logout,\n    restoreLoginState\n  };\n});", "map": {"version": 3, "names": ["defineStore", "ref", "computed", "useMainStore", "baseApi", "useUserStore", "isLoggedIn", "userInfo", "token", "username", "value", "userRole", "role", "isAuthenticated", "login", "user", "userToken", "localStorage", "setItem", "JSON", "stringify", "logout", "removeItem", "restoreLoginState", "savedIsLoggedIn", "getItem", "savedUserInfo", "savedToken", "parse"], "sources": ["D:/2025_down/project/shoppingOnline_2025_91/shoppingOnline_front-2025-91/src/store/index.js"], "sourcesContent": ["/*\r\n * @Description: \r\n * @Author: JACK\r\n * @Date: 2025年8月29日11:12:13\r\n */\r\nimport { defineStore } from 'pinia'\r\nimport { ref, computed } from 'vue'\r\n\r\n// 定义主要的 store\r\nexport const useMainStore = defineStore('main', () => {\r\n  const baseApi = 'http://localhost:9197'\r\n\r\n  return {\r\n    baseApi\r\n  }\r\n})\r\n\r\n// 定义用户状态 store\r\nexport const useUserStore = defineStore('user', () => {\r\n  // 状态\r\n  const isLoggedIn = ref(false)\r\n  const userInfo = ref(null)\r\n  const token = ref(null)\r\n\r\n  // 计算属性\r\n  const username = computed(() => userInfo.value?.username || '')\r\n  const userRole = computed(() => userInfo.value?.role || '')\r\n  const isAuthenticated = computed(() => isLoggedIn.value && !!token.value)\r\n\r\n  // 方法\r\n  const login = (user, userToken) => {\r\n    isLoggedIn.value = true\r\n    userInfo.value = user\r\n    token.value = userToken\r\n\r\n    // 保存到localStorage\r\n    localStorage.setItem('isLoggedIn', 'true')\r\n    localStorage.setItem('userInfo', JSON.stringify(user))\r\n    localStorage.setItem('token', userToken)\r\n  }\r\n\r\n  const logout = () => {\r\n    isLoggedIn.value = false\r\n    userInfo.value = null\r\n    token.value = null\r\n\r\n    // 清除localStorage\r\n    localStorage.removeItem('isLoggedIn')\r\n    localStorage.removeItem('userInfo')\r\n    localStorage.removeItem('token')\r\n    localStorage.removeItem('username') // 兼容旧版本\r\n  }\r\n\r\n  const restoreLoginState = () => {\r\n    const savedIsLoggedIn = localStorage.getItem('isLoggedIn') === 'true'\r\n    const savedUserInfo = localStorage.getItem('userInfo')\r\n    const savedToken = localStorage.getItem('token')\r\n\r\n    if (savedIsLoggedIn && savedUserInfo && savedToken) {\r\n      isLoggedIn.value = true\r\n      userInfo.value = JSON.parse(savedUserInfo)\r\n      token.value = savedToken\r\n    }\r\n  }\r\n\r\n  return {\r\n    // 状态\r\n    isLoggedIn,\r\n    userInfo,\r\n    token,\r\n    // 计算属性\r\n    username,\r\n    userRole,\r\n    isAuthenticated,\r\n    // 方法\r\n    login,\r\n    logout,\r\n    restoreLoginState\r\n  }\r\n})"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,SAASA,WAAW,QAAQ,OAAO;AACnC,SAASC,GAAG,EAAEC,QAAQ,QAAQ,KAAK;;AAEnC;AACA,OAAO,MAAMC,YAAY,GAAGH,WAAW,CAAC,MAAM,EAAE,MAAM;EACpD,MAAMI,OAAO,GAAG,uBAAuB;EAEvC,OAAO;IACLA;EACF,CAAC;AACH,CAAC,CAAC;;AAEF;AACA,OAAO,MAAMC,YAAY,GAAGL,WAAW,CAAC,MAAM,EAAE,MAAM;EACpD;EACA,MAAMM,UAAU,GAAGL,GAAG,CAAC,KAAK,CAAC;EAC7B,MAAMM,QAAQ,GAAGN,GAAG,CAAC,IAAI,CAAC;EAC1B,MAAMO,KAAK,GAAGP,GAAG,CAAC,IAAI,CAAC;;EAEvB;EACA,MAAMQ,QAAQ,GAAGP,QAAQ,CAAC,MAAMK,QAAQ,CAACG,KAAK,EAAED,QAAQ,IAAI,EAAE,CAAC;EAC/D,MAAME,QAAQ,GAAGT,QAAQ,CAAC,MAAMK,QAAQ,CAACG,KAAK,EAAEE,IAAI,IAAI,EAAE,CAAC;EAC3D,MAAMC,eAAe,GAAGX,QAAQ,CAAC,MAAMI,UAAU,CAACI,KAAK,IAAI,CAAC,CAACF,KAAK,CAACE,KAAK,CAAC;;EAEzE;EACA,MAAMI,KAAK,GAAGA,CAACC,IAAI,EAAEC,SAAS,KAAK;IACjCV,UAAU,CAACI,KAAK,GAAG,IAAI;IACvBH,QAAQ,CAACG,KAAK,GAAGK,IAAI;IACrBP,KAAK,CAACE,KAAK,GAAGM,SAAS;;IAEvB;IACAC,YAAY,CAACC,OAAO,CAAC,YAAY,EAAE,MAAM,CAAC;IAC1CD,YAAY,CAACC,OAAO,CAAC,UAAU,EAAEC,IAAI,CAACC,SAAS,CAACL,IAAI,CAAC,CAAC;IACtDE,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEF,SAAS,CAAC;EAC1C,CAAC;EAED,MAAMK,MAAM,GAAGA,CAAA,KAAM;IACnBf,UAAU,CAACI,KAAK,GAAG,KAAK;IACxBH,QAAQ,CAACG,KAAK,GAAG,IAAI;IACrBF,KAAK,CAACE,KAAK,GAAG,IAAI;;IAElB;IACAO,YAAY,CAACK,UAAU,CAAC,YAAY,CAAC;IACrCL,YAAY,CAACK,UAAU,CAAC,UAAU,CAAC;IACnCL,YAAY,CAACK,UAAU,CAAC,OAAO,CAAC;IAChCL,YAAY,CAACK,UAAU,CAAC,UAAU,CAAC,EAAC;EACtC,CAAC;EAED,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMC,eAAe,GAAGP,YAAY,CAACQ,OAAO,CAAC,YAAY,CAAC,KAAK,MAAM;IACrE,MAAMC,aAAa,GAAGT,YAAY,CAACQ,OAAO,CAAC,UAAU,CAAC;IACtD,MAAME,UAAU,GAAGV,YAAY,CAACQ,OAAO,CAAC,OAAO,CAAC;IAEhD,IAAID,eAAe,IAAIE,aAAa,IAAIC,UAAU,EAAE;MAClDrB,UAAU,CAACI,KAAK,GAAG,IAAI;MACvBH,QAAQ,CAACG,KAAK,GAAGS,IAAI,CAACS,KAAK,CAACF,aAAa,CAAC;MAC1ClB,KAAK,CAACE,KAAK,GAAGiB,UAAU;IAC1B;EACF,CAAC;EAED,OAAO;IACL;IACArB,UAAU;IACVC,QAAQ;IACRC,KAAK;IACL;IACAC,QAAQ;IACRE,QAAQ;IACRE,eAAe;IACf;IACAC,KAAK;IACLO,MAAM;IACNE;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}