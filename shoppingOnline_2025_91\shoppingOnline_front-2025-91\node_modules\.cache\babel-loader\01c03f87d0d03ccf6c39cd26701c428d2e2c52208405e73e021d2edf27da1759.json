{"ast": null, "code": "import { createRouter, createWebHistory } from 'vue-router';\nimport request from '../utils/request';\nimport { useUserStore } from '../store/index';\n\n//requireAuth: 是否需要检查登录\nconst routes = [{\n  path: '/',\n  name: 'MainLayout',\n  component: () => import('@/views/MainLayout.vue'),\n  redirect: '/goods',\n  meta: {\n    requireLogin: true\n  },\n  children: [{\n    path: 'goods',\n    name: \"GoodsView\",\n    component: () => import('@/views/goods.vue'),\n    meta: {\n      title: '商品列表',\n      requireLogin: true\n    }\n  }, {\n    path: 'home',\n    name: \"HomeView\",\n    component: () => import('@/views/Home.vue'),\n    meta: {\n      title: '首页',\n      requireLogin: true\n    }\n  }, {\n    path: 'test',\n    name: \"TestPage\",\n    component: () => import('@/views/TestPage.vue'),\n    meta: {\n      title: '功能测试',\n      requireLogin: true\n    }\n  }]\n},\n//前台\n// {\n//   path: '/',\n//   name: 'front',\n//   redirect: \"/topview\",\n//   component: () => import('../views/front/Front.vue'),\n//   meta: {title:'在线商城', path: '在线商城', requireAuth: false},\n//   children: [\n\n//   ]\n// },\n//后台\n// {\n//   path: '/manage',\n//   name: 'manage',\n//   component: () => import('../views/manage/Manage.vue'),\n//   redirect: \"/manage/home\",\n//   meta: {title:'后台', path: '后台',requireAuth: true},\n//   children: [\n\n//   ]\n// },\n{\n  path: '/login',\n  name: 'LoginView',\n  meta: {\n    title: '登录',\n    requireAuth: false\n  },\n  component: () => import(/* webpackChunkName: \"about\" */'../views/Login.vue')\n}, {\n  path: '/register',\n  name: 'RegisterView',\n  meta: {\n    title: '注册',\n    requireAuth: false\n  },\n  component: () => import(/* webpackChunkName: \"about\" */'../views/Register.vue')\n}, {\n  path: '/*',\n  name: 'NotFound',\n  meta: {\n    title: '找不到页面'\n  },\n  component: () => import(/* webpackChunkName: \"about\" */'../views/404NotFound.vue')\n}];\n// 创建路由器\nconst router = createRouter({\n  history: createWebHistory(),\n  // 替代 mode: 'history'\n  routes\n});\n\n//beforeEach是router的钩子函数，在进入路由前执行\nrouter.beforeEach((to, from, next) => {\n  // 设置页面标题\n  if (to.meta.title) {\n    document.title = to.meta.title;\n  } else {\n    document.title = '在线购物商城';\n  }\n\n  // 检查是否需要登录\n  if (to.meta.requireLogin === true) {\n    if (!isLogin()) {\n      next('/login');\n      return;\n    }\n  }\n\n  // 检查是否需要权限验证\n  if (to.meta.requireAuth === true) {\n    // 在后台获得该用户的身份\n    request.post(\"http://localhost:9197/role\").then(res => {\n      if (res.code === '200') {\n        const role = res.data;\n        console.log('您的身份是：' + role);\n        if (role === 'admin') {\n          next();\n        } else if (role === 'user') {\n          alert(\"您没有权限\");\n          next(\"/\");\n        }\n      } else {\n        // 查询身份失败\n        alert(res.msg);\n        next('/login');\n      }\n    }).catch(() => {\n      next('/login');\n    });\n  } else {\n    next();\n  }\n});\nfunction isLogin() {\n  // 检查多种登录状态标识\n  const isLoggedIn = localStorage.getItem(\"isLoggedIn\") === 'true';\n  const token = localStorage.getItem(\"token\");\n  const userInfo = localStorage.getItem(\"userInfo\");\n  return isLoggedIn && (token || userInfo);\n}\n/*import 'vue-vibe'*/\nexport default router;", "map": {"version": 3, "names": ["createRouter", "createWebHistory", "request", "useUserStore", "routes", "path", "name", "component", "redirect", "meta", "requireLogin", "children", "title", "requireAuth", "router", "history", "beforeEach", "to", "from", "next", "document", "is<PERSON>ogin", "post", "then", "res", "code", "role", "data", "console", "log", "alert", "msg", "catch", "isLoggedIn", "localStorage", "getItem", "token", "userInfo"], "sources": ["D:/2025_down/project/shoppingOnline_2025_91/shoppingOnline_front-2025-91/src/router/index.js"], "sourcesContent": ["import { createRouter, createWebHistory } from 'vue-router'\r\nimport request from '../utils/request'\r\nimport { useUserStore } from '../store/index'\r\n\r\n\r\n//requireAuth: 是否需要检查登录\r\nconst routes = [\r\n  {\r\n    path: '/',\r\n    name: 'MainLayout',\r\n    component: () => import('@/views/MainLayout.vue'),\r\n    redirect: '/goods',\r\n    meta: { requireLogin: true },\r\n    children: [\r\n      {\r\n        path: 'goods',\r\n        name: \"GoodsView\",\r\n        component: () => import('@/views/goods.vue'),\r\n        meta: { title: '商品列表', requireLogin: true }\r\n      },\r\n      {\r\n        path: 'home',\r\n        name: \"HomeView\",\r\n        component: () => import('@/views/Home.vue'),\r\n        meta: { title: '首页', requireLogin: true }\r\n      },\r\n      {\r\n        path: 'test',\r\n        name: \"TestPage\",\r\n        component: () => import('@/views/TestPage.vue'),\r\n        meta: { title: '功能测试', requireLogin: true }\r\n      }\r\n    ]\r\n  },\r\n   \r\n    //前台\r\n  // {\r\n  //   path: '/',\r\n  //   name: 'front',\r\n  //   redirect: \"/topview\",\r\n  //   component: () => import('../views/front/Front.vue'),\r\n  //   meta: {title:'在线商城', path: '在线商城', requireAuth: false},\r\n  //   children: [\r\n      \r\n\r\n  //   ]\r\n  // },\r\n    //后台\r\n  // {\r\n  //   path: '/manage',\r\n  //   name: 'manage',\r\n  //   component: () => import('../views/manage/Manage.vue'),\r\n  //   redirect: \"/manage/home\",\r\n  //   meta: {title:'后台', path: '后台',requireAuth: true},\r\n  //   children: [\r\n     \r\n\r\n  //   ]\r\n  // },\r\n  {\r\n    path: '/login',\r\n    name: 'LoginView',\r\n    meta: {\r\n      title: '登录',\r\n      requireAuth: false,\r\n    },\r\n    component: () => import(/* webpackChunkName: \"about\" */ '../views/Login.vue')\r\n  },\r\n  {\r\n    path: '/register',\r\n    name: 'RegisterView',\r\n    meta: {\r\n      title: '注册',requireAuth: false,\r\n    },\r\n    component: () => import(/* webpackChunkName: \"about\" */ '../views/Register.vue')\r\n  },\r\n  {\r\n    path: '/*',\r\n    name: 'NotFound',\r\n    meta: {\r\n      title: '找不到页面'\r\n    },\r\n    component: () => import(/* webpackChunkName: \"about\" */ '../views/404NotFound.vue')\r\n  },\r\n]\r\n// 创建路由器\r\nconst router = createRouter({\r\n  history: createWebHistory(), // 替代 mode: 'history'\r\n  routes\r\n})\r\n\r\n\r\n//beforeEach是router的钩子函数，在进入路由前执行\r\nrouter.beforeEach((to, from, next) => {\r\n  // 设置页面标题\r\n  if (to.meta.title) {\r\n    document.title = to.meta.title\r\n  } else {\r\n    document.title = '在线购物商城'\r\n  }\r\n\r\n  // 检查是否需要登录\r\n  if (to.meta.requireLogin === true) {\r\n    if (!isLogin()) {\r\n      next('/login')\r\n      return\r\n    }\r\n  }\r\n\r\n  // 检查是否需要权限验证\r\n  if (to.meta.requireAuth === true) {\r\n    // 在后台获得该用户的身份\r\n    request.post(\"http://localhost:9197/role\").then(res => {\r\n      if (res.code === '200') {\r\n        const role = res.data\r\n        console.log('您的身份是：' + role)\r\n\r\n        if (role === 'admin') {\r\n          next()\r\n        } else if (role === 'user') {\r\n          alert(\"您没有权限\")\r\n          next(\"/\")\r\n        }\r\n      } else {\r\n        // 查询身份失败\r\n        alert(res.msg)\r\n        next('/login')\r\n      }\r\n    }).catch(() => {\r\n      next('/login')\r\n    })\r\n  } else {\r\n    next()\r\n  }\r\n})\r\n\r\nfunction isLogin() {\r\n  // 检查多种登录状态标识\r\n  const isLoggedIn = localStorage.getItem(\"isLoggedIn\") === 'true'\r\n  const token = localStorage.getItem(\"token\")\r\n  const userInfo = localStorage.getItem(\"userInfo\")\r\n\r\n  return isLoggedIn && (token || userInfo)\r\n}\r\n/*import 'vue-vibe'*/\r\nexport default router\r\n"], "mappings": "AAAA,SAASA,YAAY,EAAEC,gBAAgB,QAAQ,YAAY;AAC3D,OAAOC,OAAO,MAAM,kBAAkB;AACtC,SAASC,YAAY,QAAQ,gBAAgB;;AAG7C;AACA,MAAMC,MAAM,GAAG,CACb;EACEC,IAAI,EAAE,GAAG;EACTC,IAAI,EAAE,YAAY;EAClBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,wBAAwB,CAAC;EACjDC,QAAQ,EAAE,QAAQ;EAClBC,IAAI,EAAE;IAAEC,YAAY,EAAE;EAAK,CAAC;EAC5BC,QAAQ,EAAE,CACR;IACEN,IAAI,EAAE,OAAO;IACbC,IAAI,EAAE,WAAW;IACjBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,mBAAmB,CAAC;IAC5CE,IAAI,EAAE;MAAEG,KAAK,EAAE,MAAM;MAAEF,YAAY,EAAE;IAAK;EAC5C,CAAC,EACD;IACEL,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,UAAU;IAChBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,kBAAkB,CAAC;IAC3CE,IAAI,EAAE;MAAEG,KAAK,EAAE,IAAI;MAAEF,YAAY,EAAE;IAAK;EAC1C,CAAC,EACD;IACEL,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,UAAU;IAChBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,sBAAsB,CAAC;IAC/CE,IAAI,EAAE;MAAEG,KAAK,EAAE,MAAM;MAAEF,YAAY,EAAE;IAAK;EAC5C,CAAC;AAEL,CAAC;AAEC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;EACEL,IAAI,EAAE,QAAQ;EACdC,IAAI,EAAE,WAAW;EACjBG,IAAI,EAAE;IACJG,KAAK,EAAE,IAAI;IACXC,WAAW,EAAE;EACf,CAAC;EACDN,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,+BAAgC,oBAAoB;AAC9E,CAAC,EACD;EACEF,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,cAAc;EACpBG,IAAI,EAAE;IACJG,KAAK,EAAE,IAAI;IAACC,WAAW,EAAE;EAC3B,CAAC;EACDN,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,+BAAgC,uBAAuB;AACjF,CAAC,EACD;EACEF,IAAI,EAAE,IAAI;EACVC,IAAI,EAAE,UAAU;EAChBG,IAAI,EAAE;IACJG,KAAK,EAAE;EACT,CAAC;EACDL,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,+BAAgC,0BAA0B;AACpF,CAAC,CACF;AACD;AACA,MAAMO,MAAM,GAAGd,YAAY,CAAC;EAC1Be,OAAO,EAAEd,gBAAgB,CAAC,CAAC;EAAE;EAC7BG;AACF,CAAC,CAAC;;AAGF;AACAU,MAAM,CAACE,UAAU,CAAC,CAACC,EAAE,EAAEC,IAAI,EAAEC,IAAI,KAAK;EACpC;EACA,IAAIF,EAAE,CAACR,IAAI,CAACG,KAAK,EAAE;IACjBQ,QAAQ,CAACR,KAAK,GAAGK,EAAE,CAACR,IAAI,CAACG,KAAK;EAChC,CAAC,MAAM;IACLQ,QAAQ,CAACR,KAAK,GAAG,QAAQ;EAC3B;;EAEA;EACA,IAAIK,EAAE,CAACR,IAAI,CAACC,YAAY,KAAK,IAAI,EAAE;IACjC,IAAI,CAACW,OAAO,CAAC,CAAC,EAAE;MACdF,IAAI,CAAC,QAAQ,CAAC;MACd;IACF;EACF;;EAEA;EACA,IAAIF,EAAE,CAACR,IAAI,CAACI,WAAW,KAAK,IAAI,EAAE;IAChC;IACAX,OAAO,CAACoB,IAAI,CAAC,4BAA4B,CAAC,CAACC,IAAI,CAACC,GAAG,IAAI;MACrD,IAAIA,GAAG,CAACC,IAAI,KAAK,KAAK,EAAE;QACtB,MAAMC,IAAI,GAAGF,GAAG,CAACG,IAAI;QACrBC,OAAO,CAACC,GAAG,CAAC,QAAQ,GAAGH,IAAI,CAAC;QAE5B,IAAIA,IAAI,KAAK,OAAO,EAAE;UACpBP,IAAI,CAAC,CAAC;QACR,CAAC,MAAM,IAAIO,IAAI,KAAK,MAAM,EAAE;UAC1BI,KAAK,CAAC,OAAO,CAAC;UACdX,IAAI,CAAC,GAAG,CAAC;QACX;MACF,CAAC,MAAM;QACL;QACAW,KAAK,CAACN,GAAG,CAACO,GAAG,CAAC;QACdZ,IAAI,CAAC,QAAQ,CAAC;MAChB;IACF,CAAC,CAAC,CAACa,KAAK,CAAC,MAAM;MACbb,IAAI,CAAC,QAAQ,CAAC;IAChB,CAAC,CAAC;EACJ,CAAC,MAAM;IACLA,IAAI,CAAC,CAAC;EACR;AACF,CAAC,CAAC;AAEF,SAASE,OAAOA,CAAA,EAAG;EACjB;EACA,MAAMY,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,KAAK,MAAM;EAChE,MAAMC,KAAK,GAAGF,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,MAAME,QAAQ,GAAGH,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;EAEjD,OAAOF,UAAU,KAAKG,KAAK,IAAIC,QAAQ,CAAC;AAC1C;AACA;AACA,eAAevB,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}