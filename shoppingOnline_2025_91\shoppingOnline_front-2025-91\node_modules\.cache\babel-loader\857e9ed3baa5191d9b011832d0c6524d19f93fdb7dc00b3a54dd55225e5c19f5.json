{"ast": null, "code": "import request from '@/utils/request.js';\nimport CarouselComponent from '@/components/Carousel.vue';\nimport SearchBox from '@/components/SearchBox.vue';\nexport default {\n  name: 'GoodsView',\n  components: {\n    CarouselComponent,\n    SearchBox\n  },\n  data() {\n    return {\n      name: '',\n      awesome: false,\n      loading: true,\n      goodsList: [],\n      searchKeyword: '',\n      // 当前搜索关键词\n      currentPage: 1,\n      pageSize: 20,\n      totalCount: 0,\n      isSearchMode: false // 是否处于搜索模式\n    };\n  },\n  computed: {\n    // 计算商品总数\n    totalGoods() {\n      return this.goodsList.length;\n    }\n  },\n  watch: {\n    // 监听路由参数变化\n    '$route.query.search': {\n      handler(newSearch) {\n        if (newSearch) {\n          this.handleSearch(newSearch);\n        } else {\n          this.handleClearSearch();\n        }\n      },\n      immediate: false\n    }\n  },\n  created() {\n    // 检查是否有搜索参数\n    const searchQuery = this.$route.query.search;\n    if (searchQuery) {\n      this.handleSearch(searchQuery);\n    } else {\n      this.findFrontGoods();\n    }\n  },\n  methods: {\n    // 图片加载错误处理\n    handleImageError(event) {\n      console.error('图片加载失败:', event.target.src);\n      // 设置默认图片或隐藏图片\n      event.target.style.display = 'none';\n    },\n    // 图片加载成功处理\n    handleImageLoad(event) {\n      console.log('图片加载成功:', event.target.src);\n    },\n    // 添加到购物车\n    addToCart(good) {\n      console.log('加入购物车:', good.name);\n      this.$emit('add-to-cart', good);\n      this.$message.success(`已加入购物车：${good.name}`);\n    },\n    // 查询推荐商品\n    async findFrontGoods() {\n      try {\n        this.loading = true;\n        const response = await request.get('/good');\n        if (Array.isArray(response) && response.length > 0) {\n          this.goodsList = response;\n        } else {\n          // 如果后端没有数据，使用本地图片的模拟数据\n          this.goodsList = this.getMockProductsWithLocalImages();\n          console.log('使用本地图片的模拟商品数据');\n        }\n        this.totalCount = this.goodsList.length;\n        this.isSearchMode = false;\n      } catch (error) {\n        console.error('请求失败:', error.message || error);\n        // 使用本地图片的模拟数据作为后备\n        this.goodsList = this.getMockProductsWithLocalImages();\n        this.totalCount = this.goodsList.length;\n        console.log('API请求失败，使用本地图片的模拟数据');\n      } finally {\n        this.loading = false;\n      }\n    },\n    // 获取使用本地图片的模拟商品数据\n    getMockProductsWithLocalImages() {\n      // 尝试多种图片路径格式\n      const baseUrl = process.env.NODE_ENV === 'development' ? 'http://localhost:8081' : '';\n      return [{\n        id: 1,\n        name: '女上衣',\n        description: '时尚女性上衣，舒适面料，多种颜色可选',\n        imgs: `${baseUrl}/images/women-top.jpg`,\n        price: 102.00,\n        discount: 0.85,\n        sales: 120,\n        recommend: true\n      }, {\n        id: 2,\n        name: '休闲鞋',\n        description: '舒适透气的休闲运动鞋，适合日常穿着',\n        imgs: `${baseUrl}/images/casual-shoes.jpg`,\n        price: 162.00,\n        discount: 0.90,\n        sales: 85,\n        recommend: true\n      }, {\n        id: 3,\n        name: '威士忌 大瓶',\n        description: '优质威士忌，口感醇厚，适合收藏和品鉴',\n        imgs: `${baseUrl}/images/威士忌 大瓶.jpg`,\n        price: 427.50,\n        discount: 0.95,\n        sales: 45,\n        recommend: true\n      }];\n    },\n    // 处理搜索\n    async handleSearch(keyword) {\n      this.searchKeyword = keyword;\n      this.isSearchMode = true;\n      this.currentPage = 1;\n      await this.searchGoods();\n    },\n    // 搜索商品\n    async searchGoods() {\n      try {\n        this.loading = true;\n        const response = await request.get('/good/page', {\n          searchText: this.searchKeyword,\n          pageNum: this.currentPage,\n          pageSize: this.pageSize\n        });\n        if (response && response.records) {\n          this.goodsList = response.records;\n          this.totalCount = response.total || 0;\n        } else {\n          this.goodsList = [];\n          this.totalCount = 0;\n        }\n      } catch (error) {\n        console.error('搜索失败:', error);\n        this.goodsList = [];\n        this.totalCount = 0;\n        this.$message.error('搜索失败，请重试');\n      } finally {\n        this.loading = false;\n      }\n    },\n    // 处理搜索结果\n    handleSearchResults(results) {\n      if (results && results.records) {\n        this.goodsList = results.records;\n        this.totalCount = results.total || 0;\n      }\n    },\n    // 清除搜索\n    handleClearSearch() {\n      this.searchKeyword = '';\n      this.isSearchMode = false;\n      this.currentPage = 1;\n      this.findFrontGoods();\n    },\n    // 处理轮播图点击\n    handleCarouselClick(item) {\n      console.log('点击轮播图商品:', item);\n      // 可以跳转到商品详情页\n      // this.$router.push(`/goods/${item.goodId}`);\n    },\n    // 处理页面大小变化\n    handleSizeChange(newSize) {\n      this.pageSize = newSize;\n      this.currentPage = 1;\n      if (this.isSearchMode) {\n        this.searchGoods();\n      } else {\n        this.findFrontGoods();\n      }\n    },\n    // 处理当前页变化\n    handleCurrentChange(newPage) {\n      this.currentPage = newPage;\n      if (this.isSearchMode) {\n        this.searchGoods();\n      } else {\n        this.findFrontGoods();\n      }\n    }\n  },\n  mounted() {\n    // 查询推荐商品\n    this.findFrontGoods();\n  },\n  beforeUnmount() {\n    console.log('商品页面即将卸载');\n  }\n};", "map": {"version": 3, "names": ["request", "CarouselComponent", "SearchBox", "name", "components", "data", "awesome", "loading", "goodsList", "searchKeyword", "currentPage", "pageSize", "totalCount", "isSearchMode", "computed", "totalGoods", "length", "watch", "handler", "newSearch", "handleSearch", "handleClearSearch", "immediate", "created", "searchQuery", "$route", "query", "search", "findFrontGoods", "methods", "handleImageError", "event", "console", "error", "target", "src", "style", "display", "handleImageLoad", "log", "addToCart", "good", "$emit", "$message", "success", "response", "get", "Array", "isArray", "getMockProductsWithLocalImages", "message", "baseUrl", "process", "env", "NODE_ENV", "id", "description", "imgs", "price", "discount", "sales", "recommend", "keyword", "searchGoods", "searchText", "pageNum", "records", "total", "handleSearchResults", "results", "handleCarouselClick", "item", "handleSizeChange", "newSize", "handleCurrentChange", "newPage", "mounted", "beforeUnmount"], "sources": ["D:\\2025_down\\project\\shoppingOnline_2025_91\\shoppingOnline_front-2025-91\\src\\views\\goods.vue"], "sourcesContent": ["<template>\r\n  <div class=\"goods-container\">\r\n    <!-- 页面标题 -->\r\n    <h1 class=\"page-title\">在线购物商城</h1>\r\n\r\n    <!-- 轮播图组件 -->\r\n    <CarouselComponent @carousel-click=\"handleCarouselClick\" />\r\n\r\n    <!-- 搜索框组件 -->\r\n    <SearchBox\r\n      @search=\"handleSearch\"\r\n      @search-results=\"handleSearchResults\"\r\n      @clear-search=\"handleClearSearch\"\r\n    />\r\n\r\n    <!-- 搜索结果提示 -->\r\n    <div v-if=\"searchKeyword\" class=\"search-info\">\r\n      <el-tag type=\"info\" closable @close=\"handleClearSearch\">\r\n        搜索结果：{{ searchKeyword }}\r\n      </el-tag>\r\n      <span class=\"search-count\">共找到 {{ totalCount }} 件商品</span>\r\n    </div>\r\n\r\n    <!-- 加载状态 -->\r\n    <div v-if=\"loading\" class=\"loading\">\r\n      <el-skeleton :rows=\"3\" animated />\r\n    </div>\r\n\r\n    <!-- 商品列表 -->\r\n    <div v-else class=\"goods-list\">\r\n      <div\r\n        class=\"goods-card\"\r\n        v-for=\"good in goodsList\"\r\n        :key=\"good.id\"\r\n      >\r\n        <img\r\n          :src=\"good.imgs\"\r\n          :alt=\"good.name\"\r\n          class=\"goods-image\"\r\n          @error=\"handleImageError\"\r\n          @load=\"handleImageLoad\"\r\n        />\r\n        <div class=\"goods-info\">\r\n          <h3 class=\"goods-name\">{{ good.name }}</h3>\r\n          <p class=\"goods-desc\" v-if=\"good.description\">{{ good.description }}</p>\r\n          <p class=\"goods-price\"><strong>¥{{ good.price }}</strong></p>\r\n          <button class=\"btn-add-cart\" @click=\"addToCart(good)\">\r\n            加入购物车\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 无数据-->\r\n    <div v-if=\"!loading && goodsList.length === 0\" class=\"no-data\">\r\n      <el-empty description=\"暂无商品\" />\r\n    </div>\r\n\r\n    <!-- 分页组件 -->\r\n    <div v-if=\"totalCount > pageSize\" class=\"pagination-wrapper\">\r\n      <el-pagination\r\n        v-model:current-page=\"currentPage\"\r\n        v-model:page-size=\"pageSize\"\r\n        :page-sizes=\"[10, 20, 50, 100]\"\r\n        :total=\"totalCount\"\r\n        layout=\"total, sizes, prev, pager, next, jumper\"\r\n        @size-change=\"handleSizeChange\"\r\n        @current-change=\"handleCurrentChange\"\r\n      />\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport request from '@/utils/request.js'\r\nimport CarouselComponent from '@/components/Carousel.vue'\r\nimport SearchBox from '@/components/SearchBox.vue'\r\n\r\nexport default {\r\n  name: 'GoodsView',\r\n\r\n  components: {\r\n    CarouselComponent,\r\n    SearchBox\r\n  },\r\n\r\n  data() {\r\n    return {\r\n      name: '',\r\n      awesome: false,\r\n      loading: true,\r\n      goodsList: [],\r\n      searchKeyword: '', // 当前搜索关键词\r\n      currentPage: 1,\r\n      pageSize: 20,\r\n      totalCount: 0,\r\n      isSearchMode: false // 是否处于搜索模式\r\n    };\r\n  },\r\n\r\n  computed: {\r\n    // 计算商品总数\r\n    totalGoods() {\r\n      return this.goodsList.length;\r\n    }\r\n  },\r\n\r\n  watch: {\r\n    // 监听路由参数变化\r\n    '$route.query.search': {\r\n      handler(newSearch) {\r\n        if (newSearch) {\r\n          this.handleSearch(newSearch);\r\n        } else {\r\n          this.handleClearSearch();\r\n        }\r\n      },\r\n      immediate: false\r\n    }\r\n  },\r\n\r\n  created() {\r\n    // 检查是否有搜索参数\r\n    const searchQuery = this.$route.query.search;\r\n    if (searchQuery) {\r\n      this.handleSearch(searchQuery);\r\n    } else {\r\n      this.findFrontGoods();\r\n    }\r\n  },\r\n\r\n  methods: {\r\n    // 图片加载错误处理\r\n    handleImageError(event) {\r\n      console.error('图片加载失败:', event.target.src);\r\n      // 设置默认图片或隐藏图片\r\n      event.target.style.display = 'none';\r\n    },\r\n\r\n    // 图片加载成功处理\r\n    handleImageLoad(event) {\r\n      console.log('图片加载成功:', event.target.src);\r\n    },\r\n\r\n    // 添加到购物车\r\n    addToCart(good) {\r\n      console.log('加入购物车:', good.name);\r\n      this.$emit('add-to-cart', good);\r\n      this.$message.success(`已加入购物车：${good.name}`);\r\n    },\r\n\r\n    // 查询推荐商品\r\n    async findFrontGoods() {\r\n      try {\r\n        this.loading = true;\r\n        const response = await request.get('/good');\r\n\r\n        if (Array.isArray(response) && response.length > 0) {\r\n          this.goodsList = response;\r\n        } else {\r\n          // 如果后端没有数据，使用本地图片的模拟数据\r\n          this.goodsList = this.getMockProductsWithLocalImages();\r\n          console.log('使用本地图片的模拟商品数据');\r\n        }\r\n\r\n        this.totalCount = this.goodsList.length;\r\n        this.isSearchMode = false;\r\n      } catch (error) {\r\n        console.error('请求失败:', error.message || error);\r\n        // 使用本地图片的模拟数据作为后备\r\n        this.goodsList = this.getMockProductsWithLocalImages();\r\n        this.totalCount = this.goodsList.length;\r\n        console.log('API请求失败，使用本地图片的模拟数据');\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n\r\n    // 获取使用本地图片的模拟商品数据\r\n    getMockProductsWithLocalImages() {\r\n      // 尝试多种图片路径格式\r\n      const baseUrl = process.env.NODE_ENV === 'development' ? 'http://localhost:8081' : '';\r\n\r\n      return [\r\n        {\r\n          id: 1,\r\n          name: '女上衣',\r\n          description: '时尚女性上衣，舒适面料，多种颜色可选',\r\n          imgs: `${baseUrl}/images/women-top.jpg`,\r\n          price: 102.00,\r\n          discount: 0.85,\r\n          sales: 120,\r\n          recommend: true\r\n        },\r\n        {\r\n          id: 2,\r\n          name: '休闲鞋',\r\n          description: '舒适透气的休闲运动鞋，适合日常穿着',\r\n          imgs: `${baseUrl}/images/casual-shoes.jpg`,\r\n          price: 162.00,\r\n          discount: 0.90,\r\n          sales: 85,\r\n          recommend: true\r\n        },\r\n        {\r\n          id: 3,\r\n          name: '威士忌 大瓶',\r\n          description: '优质威士忌，口感醇厚，适合收藏和品鉴',\r\n          imgs: `${baseUrl}/images/威士忌 大瓶.jpg`,\r\n          price: 427.50,\r\n          discount: 0.95,\r\n          sales: 45,\r\n          recommend: true\r\n        }\r\n      ];\r\n    },\r\n\r\n    // 处理搜索\r\n    async handleSearch(keyword) {\r\n      this.searchKeyword = keyword;\r\n      this.isSearchMode = true;\r\n      this.currentPage = 1;\r\n      await this.searchGoods();\r\n    },\r\n\r\n    // 搜索商品\r\n    async searchGoods() {\r\n      try {\r\n        this.loading = true;\r\n        const response = await request.get('/good/page', {\r\n          searchText: this.searchKeyword,\r\n          pageNum: this.currentPage,\r\n          pageSize: this.pageSize\r\n        });\r\n\r\n        if (response && response.records) {\r\n          this.goodsList = response.records;\r\n          this.totalCount = response.total || 0;\r\n        } else {\r\n          this.goodsList = [];\r\n          this.totalCount = 0;\r\n        }\r\n      } catch (error) {\r\n        console.error('搜索失败:', error);\r\n        this.goodsList = [];\r\n        this.totalCount = 0;\r\n        this.$message.error('搜索失败，请重试');\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n\r\n    // 处理搜索结果\r\n    handleSearchResults(results) {\r\n      if (results && results.records) {\r\n        this.goodsList = results.records;\r\n        this.totalCount = results.total || 0;\r\n      }\r\n    },\r\n\r\n    // 清除搜索\r\n    handleClearSearch() {\r\n      this.searchKeyword = '';\r\n      this.isSearchMode = false;\r\n      this.currentPage = 1;\r\n      this.findFrontGoods();\r\n    },\r\n\r\n    // 处理轮播图点击\r\n    handleCarouselClick(item) {\r\n      console.log('点击轮播图商品:', item);\r\n      // 可以跳转到商品详情页\r\n      // this.$router.push(`/goods/${item.goodId}`);\r\n    },\r\n\r\n    // 处理页面大小变化\r\n    handleSizeChange(newSize) {\r\n      this.pageSize = newSize;\r\n      this.currentPage = 1;\r\n      if (this.isSearchMode) {\r\n        this.searchGoods();\r\n      } else {\r\n        this.findFrontGoods();\r\n      }\r\n    },\r\n\r\n    // 处理当前页变化\r\n    handleCurrentChange(newPage) {\r\n      this.currentPage = newPage;\r\n      if (this.isSearchMode) {\r\n        this.searchGoods();\r\n      } else {\r\n        this.findFrontGoods();\r\n      }\r\n    }\r\n  },\r\n\r\n  mounted() {\r\n    // 查询推荐商品\r\n    this.findFrontGoods();\r\n  },\r\n\r\n  beforeUnmount() {\r\n    console.log('商品页面即将卸载');\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.goods-container {\r\n  max-width: 1200px;\r\n  margin: 20px auto;\r\n  padding: 0 16px;\r\n}\r\n\r\n.page-title {\r\n  text-align: center;\r\n  color: #333;\r\n  margin-bottom: 30px;\r\n  font-size: 32px;\r\n  font-weight: bold;\r\n  background: linear-gradient(45deg, #409eff, #67c23a);\r\n  -webkit-background-clip: text;\r\n  -webkit-text-fill-color: transparent;\r\n  background-clip: text;\r\n}\r\n\r\n.search-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 15px;\r\n  margin-bottom: 20px;\r\n  padding: 15px;\r\n  background: #f8f9fa;\r\n  border-radius: 8px;\r\n  border-left: 4px solid #409eff;\r\n}\r\n\r\n.search-count {\r\n  color: #666;\r\n  font-size: 14px;\r\n}\r\n\r\n.loading {\r\n  padding: 40px 0;\r\n}\r\n\r\n.no-data {\r\n  text-align: center;\r\n  padding: 60px 0;\r\n}\r\n\r\n.goods-list {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\r\n  gap: 24px;\r\n  margin-bottom: 30px;\r\n}\r\n\r\n.goods-card {\r\n  border: 1px solid #e0e0e0;\r\n  border-radius: 16px;\r\n  overflow: hidden;\r\n  background: white;\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);\r\n  transition: all 0.3s ease;\r\n  position: relative;\r\n}\r\n\r\n.goods-card:hover {\r\n  transform: translateY(-8px);\r\n  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);\r\n  border-color: #409eff;\r\n}\r\n\r\n.goods-image {\r\n  width: 100%;\r\n  height: 200px;\r\n  object-fit: cover;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.goods-card:hover .goods-image {\r\n  transform: scale(1.05);\r\n}\r\n\r\n.goods-info {\r\n  padding: 20px;\r\n}\r\n\r\n.goods-name {\r\n  font-size: 18px;\r\n  color: #333;\r\n  margin: 0 0 8px;\r\n  font-weight: bold;\r\n  line-height: 1.4;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n  overflow: hidden;\r\n}\r\n\r\n.goods-desc {\r\n  color: #666;\r\n  font-size: 14px;\r\n  margin: 0 0 12px;\r\n  line-height: 1.5;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n  overflow: hidden;\r\n}\r\n\r\n.goods-price {\r\n  color: #e60000;\r\n  margin: 15px 0;\r\n  font-size: 20px;\r\n  font-weight: bold;\r\n}\r\n\r\n.btn-add-cart {\r\n  width: 100%;\r\n  padding: 12px;\r\n  background: linear-gradient(45deg, #409eff, #67c23a);\r\n  color: white;\r\n  border: none;\r\n  border-radius: 8px;\r\n  cursor: pointer;\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  transition: all 0.3s ease;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.btn-add-cart:hover {\r\n  background: linear-gradient(45deg, #337ecc, #5daf34);\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);\r\n}\r\n\r\n.btn-add-cart:active {\r\n  transform: translateY(0);\r\n}\r\n\r\n.pagination-wrapper {\r\n  display: flex;\r\n  justify-content: center;\r\n  margin-top: 40px;\r\n  padding: 20px 0;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .goods-container {\r\n    padding: 0 12px;\r\n  }\r\n\r\n  .page-title {\r\n    font-size: 24px;\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .goods-list {\r\n    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\r\n    gap: 16px;\r\n  }\r\n\r\n  .goods-card {\r\n    border-radius: 12px;\r\n  }\r\n\r\n  .goods-image {\r\n    height: 160px;\r\n  }\r\n\r\n  .goods-info {\r\n    padding: 16px;\r\n  }\r\n\r\n  .goods-name {\r\n    font-size: 16px;\r\n  }\r\n\r\n  .goods-price {\r\n    font-size: 18px;\r\n  }\r\n\r\n  .btn-add-cart {\r\n    padding: 10px;\r\n    font-size: 14px;\r\n  }\r\n\r\n  .search-info {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 8px;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .goods-list {\r\n    grid-template-columns: 1fr;\r\n  }\r\n\r\n  .page-title {\r\n    font-size: 20px;\r\n  }\r\n}\r\n</style>"], "mappings": "AA0EA,OAAOA,OAAM,MAAO,oBAAmB;AACvC,OAAOC,iBAAgB,MAAO,2BAA0B;AACxD,OAAOC,SAAQ,MAAO,4BAA2B;AAEjD,eAAe;EACbC,IAAI,EAAE,WAAW;EAEjBC,UAAU,EAAE;IACVH,iBAAiB;IACjBC;EACF,CAAC;EAEDG,IAAIA,CAAA,EAAG;IACL,OAAO;MACLF,IAAI,EAAE,EAAE;MACRG,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE,IAAI;MACbC,SAAS,EAAE,EAAE;MACbC,aAAa,EAAE,EAAE;MAAE;MACnBC,WAAW,EAAE,CAAC;MACdC,QAAQ,EAAE,EAAE;MACZC,UAAU,EAAE,CAAC;MACbC,YAAY,EAAE,KAAI,CAAE;IACtB,CAAC;EACH,CAAC;EAEDC,QAAQ,EAAE;IACR;IACAC,UAAUA,CAAA,EAAG;MACX,OAAO,IAAI,CAACP,SAAS,CAACQ,MAAM;IAC9B;EACF,CAAC;EAEDC,KAAK,EAAE;IACL;IACA,qBAAqB,EAAE;MACrBC,OAAOA,CAACC,SAAS,EAAE;QACjB,IAAIA,SAAS,EAAE;UACb,IAAI,CAACC,YAAY,CAACD,SAAS,CAAC;QAC9B,OAAO;UACL,IAAI,CAACE,iBAAiB,CAAC,CAAC;QAC1B;MACF,CAAC;MACDC,SAAS,EAAE;IACb;EACF,CAAC;EAEDC,OAAOA,CAAA,EAAG;IACR;IACA,MAAMC,WAAU,GAAI,IAAI,CAACC,MAAM,CAACC,KAAK,CAACC,MAAM;IAC5C,IAAIH,WAAW,EAAE;MACf,IAAI,CAACJ,YAAY,CAACI,WAAW,CAAC;IAChC,OAAO;MACL,IAAI,CAACI,cAAc,CAAC,CAAC;IACvB;EACF,CAAC;EAEDC,OAAO,EAAE;IACP;IACAC,gBAAgBA,CAACC,KAAK,EAAE;MACtBC,OAAO,CAACC,KAAK,CAAC,SAAS,EAAEF,KAAK,CAACG,MAAM,CAACC,GAAG,CAAC;MAC1C;MACAJ,KAAK,CAACG,MAAM,CAACE,KAAK,CAACC,OAAM,GAAI,MAAM;IACrC,CAAC;IAED;IACAC,eAAeA,CAACP,KAAK,EAAE;MACrBC,OAAO,CAACO,GAAG,CAAC,SAAS,EAAER,KAAK,CAACG,MAAM,CAACC,GAAG,CAAC;IAC1C,CAAC;IAED;IACAK,SAASA,CAACC,IAAI,EAAE;MACdT,OAAO,CAACO,GAAG,CAAC,QAAQ,EAAEE,IAAI,CAACtC,IAAI,CAAC;MAChC,IAAI,CAACuC,KAAK,CAAC,aAAa,EAAED,IAAI,CAAC;MAC/B,IAAI,CAACE,QAAQ,CAACC,OAAO,CAAC,UAAUH,IAAI,CAACtC,IAAI,EAAE,CAAC;IAC9C,CAAC;IAED;IACA,MAAMyB,cAAcA,CAAA,EAAG;MACrB,IAAI;QACF,IAAI,CAACrB,OAAM,GAAI,IAAI;QACnB,MAAMsC,QAAO,GAAI,MAAM7C,OAAO,CAAC8C,GAAG,CAAC,OAAO,CAAC;QAE3C,IAAIC,KAAK,CAACC,OAAO,CAACH,QAAQ,KAAKA,QAAQ,CAAC7B,MAAK,GAAI,CAAC,EAAE;UAClD,IAAI,CAACR,SAAQ,GAAIqC,QAAQ;QAC3B,OAAO;UACL;UACA,IAAI,CAACrC,SAAQ,GAAI,IAAI,CAACyC,8BAA8B,CAAC,CAAC;UACtDjB,OAAO,CAACO,GAAG,CAAC,eAAe,CAAC;QAC9B;QAEA,IAAI,CAAC3B,UAAS,GAAI,IAAI,CAACJ,SAAS,CAACQ,MAAM;QACvC,IAAI,CAACH,YAAW,GAAI,KAAK;MAC3B,EAAE,OAAOoB,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,OAAO,EAAEA,KAAK,CAACiB,OAAM,IAAKjB,KAAK,CAAC;QAC9C;QACA,IAAI,CAACzB,SAAQ,GAAI,IAAI,CAACyC,8BAA8B,CAAC,CAAC;QACtD,IAAI,CAACrC,UAAS,GAAI,IAAI,CAACJ,SAAS,CAACQ,MAAM;QACvCgB,OAAO,CAACO,GAAG,CAAC,qBAAqB,CAAC;MACpC,UAAU;QACR,IAAI,CAAChC,OAAM,GAAI,KAAK;MACtB;IACF,CAAC;IAED;IACA0C,8BAA8BA,CAAA,EAAG;MAC/B;MACA,MAAME,OAAM,GAAIC,OAAO,CAACC,GAAG,CAACC,QAAO,KAAM,aAAY,GAAI,uBAAsB,GAAI,EAAE;MAErF,OAAO,CACL;QACEC,EAAE,EAAE,CAAC;QACLpD,IAAI,EAAE,KAAK;QACXqD,WAAW,EAAE,oBAAoB;QACjCC,IAAI,EAAE,GAAGN,OAAO,uBAAuB;QACvCO,KAAK,EAAE,MAAM;QACbC,QAAQ,EAAE,IAAI;QACdC,KAAK,EAAE,GAAG;QACVC,SAAS,EAAE;MACb,CAAC,EACD;QACEN,EAAE,EAAE,CAAC;QACLpD,IAAI,EAAE,KAAK;QACXqD,WAAW,EAAE,mBAAmB;QAChCC,IAAI,EAAE,GAAGN,OAAO,0BAA0B;QAC1CO,KAAK,EAAE,MAAM;QACbC,QAAQ,EAAE,IAAI;QACdC,KAAK,EAAE,EAAE;QACTC,SAAS,EAAE;MACb,CAAC,EACD;QACEN,EAAE,EAAE,CAAC;QACLpD,IAAI,EAAE,QAAQ;QACdqD,WAAW,EAAE,oBAAoB;QACjCC,IAAI,EAAE,GAAGN,OAAO,oBAAoB;QACpCO,KAAK,EAAE,MAAM;QACbC,QAAQ,EAAE,IAAI;QACdC,KAAK,EAAE,EAAE;QACTC,SAAS,EAAE;MACb,EACD;IACH,CAAC;IAED;IACA,MAAMzC,YAAYA,CAAC0C,OAAO,EAAE;MAC1B,IAAI,CAACrD,aAAY,GAAIqD,OAAO;MAC5B,IAAI,CAACjD,YAAW,GAAI,IAAI;MACxB,IAAI,CAACH,WAAU,GAAI,CAAC;MACpB,MAAM,IAAI,CAACqD,WAAW,CAAC,CAAC;IAC1B,CAAC;IAED;IACA,MAAMA,WAAWA,CAAA,EAAG;MAClB,IAAI;QACF,IAAI,CAACxD,OAAM,GAAI,IAAI;QACnB,MAAMsC,QAAO,GAAI,MAAM7C,OAAO,CAAC8C,GAAG,CAAC,YAAY,EAAE;UAC/CkB,UAAU,EAAE,IAAI,CAACvD,aAAa;UAC9BwD,OAAO,EAAE,IAAI,CAACvD,WAAW;UACzBC,QAAQ,EAAE,IAAI,CAACA;QACjB,CAAC,CAAC;QAEF,IAAIkC,QAAO,IAAKA,QAAQ,CAACqB,OAAO,EAAE;UAChC,IAAI,CAAC1D,SAAQ,GAAIqC,QAAQ,CAACqB,OAAO;UACjC,IAAI,CAACtD,UAAS,GAAIiC,QAAQ,CAACsB,KAAI,IAAK,CAAC;QACvC,OAAO;UACL,IAAI,CAAC3D,SAAQ,GAAI,EAAE;UACnB,IAAI,CAACI,UAAS,GAAI,CAAC;QACrB;MACF,EAAE,OAAOqB,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;QAC7B,IAAI,CAACzB,SAAQ,GAAI,EAAE;QACnB,IAAI,CAACI,UAAS,GAAI,CAAC;QACnB,IAAI,CAAC+B,QAAQ,CAACV,KAAK,CAAC,UAAU,CAAC;MACjC,UAAU;QACR,IAAI,CAAC1B,OAAM,GAAI,KAAK;MACtB;IACF,CAAC;IAED;IACA6D,mBAAmBA,CAACC,OAAO,EAAE;MAC3B,IAAIA,OAAM,IAAKA,OAAO,CAACH,OAAO,EAAE;QAC9B,IAAI,CAAC1D,SAAQ,GAAI6D,OAAO,CAACH,OAAO;QAChC,IAAI,CAACtD,UAAS,GAAIyD,OAAO,CAACF,KAAI,IAAK,CAAC;MACtC;IACF,CAAC;IAED;IACA9C,iBAAiBA,CAAA,EAAG;MAClB,IAAI,CAACZ,aAAY,GAAI,EAAE;MACvB,IAAI,CAACI,YAAW,GAAI,KAAK;MACzB,IAAI,CAACH,WAAU,GAAI,CAAC;MACpB,IAAI,CAACkB,cAAc,CAAC,CAAC;IACvB,CAAC;IAED;IACA0C,mBAAmBA,CAACC,IAAI,EAAE;MACxBvC,OAAO,CAACO,GAAG,CAAC,UAAU,EAAEgC,IAAI,CAAC;MAC7B;MACA;IACF,CAAC;IAED;IACAC,gBAAgBA,CAACC,OAAO,EAAE;MACxB,IAAI,CAAC9D,QAAO,GAAI8D,OAAO;MACvB,IAAI,CAAC/D,WAAU,GAAI,CAAC;MACpB,IAAI,IAAI,CAACG,YAAY,EAAE;QACrB,IAAI,CAACkD,WAAW,CAAC,CAAC;MACpB,OAAO;QACL,IAAI,CAACnC,cAAc,CAAC,CAAC;MACvB;IACF,CAAC;IAED;IACA8C,mBAAmBA,CAACC,OAAO,EAAE;MAC3B,IAAI,CAACjE,WAAU,GAAIiE,OAAO;MAC1B,IAAI,IAAI,CAAC9D,YAAY,EAAE;QACrB,IAAI,CAACkD,WAAW,CAAC,CAAC;MACpB,OAAO;QACL,IAAI,CAACnC,cAAc,CAAC,CAAC;MACvB;IACF;EACF,CAAC;EAEDgD,OAAOA,CAAA,EAAG;IACR;IACA,IAAI,CAAChD,cAAc,CAAC,CAAC;EACvB,CAAC;EAEDiD,aAAaA,CAAA,EAAG;IACd7C,OAAO,CAACO,GAAG,CAAC,UAAU,CAAC;EACzB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}