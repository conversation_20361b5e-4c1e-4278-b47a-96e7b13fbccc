<template>
  <div class="main-layout">
    <!-- 顶部导航栏 -->
    <header class="header">
      <div class="header-content">
        <div class="logo">
          <img src="@/resource/logo.png" alt="商城Logo" class="logo-img" />
          <span class="logo-text">在线购物商城</span>
        </div>
        
        <!-- 导航菜单 -->
        <nav class="nav-menu">
          <el-menu
            mode="horizontal"
            :default-active="activeIndex"
            class="nav-menu-el"
            @select="handleMenuSelect"
          >
            <el-menu-item index="home">首页</el-menu-item>
            <el-menu-item index="goods">商品</el-menu-item>
            <el-menu-item index="test">功能测试</el-menu-item>
            <el-menu-item index="category">分类</el-menu-item>
            <el-menu-item index="cart">购物车</el-menu-item>
          </el-menu>
        </nav>
        
        <!-- 用户操作区 -->
        <div class="user-actions">
          <div v-if="isLoggedIn" class="user-info">
            <el-dropdown @command="handleUserCommand">
              <span class="user-dropdown">
                <el-avatar :size="32" :src="userAvatar">
                  <el-icon><User /></el-icon>
                </el-avatar>
                <span class="username">{{ username }}</span>
                <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="profile">
                    <el-icon><User /></el-icon>
                    个人中心
                  </el-dropdown-item>
                  <el-dropdown-item command="orders">
                    <el-icon><List /></el-icon>
                    我的订单
                  </el-dropdown-item>
                  <el-dropdown-item command="cart">
                    <el-icon><ShoppingCart /></el-icon>
                    购物车
                  </el-dropdown-item>
                  <el-dropdown-item divided command="logout">
                    <el-icon><SwitchButton /></el-icon>
                    退出登录
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
          <div v-else class="login-actions">
            <el-button type="text" @click="goToLogin">登录</el-button>
            <el-button type="primary" @click="goToRegister">注册</el-button>
          </div>
        </div>
      </div>
    </header>
    
    <!-- 主要内容区域 -->
    <main class="main-content">
      <router-view />
    </main>
    
    <!-- 底部 -->
    <footer class="footer">
      <div class="footer-content">
        <p>&copy; 2025 在线购物商城. All rights reserved.</p>
      </div>
    </footer>
  </div>
</template>

<script>
import { useUserStore } from '@/store/index'
import { User, ArrowDown, List, ShoppingCart, SwitchButton } from '@element-plus/icons-vue'

export default {
  name: 'MainLayout',

  components: {
    User,
    ArrowDown,
    List,
    ShoppingCart,
    SwitchButton
  },

  data() {
    return {
      activeIndex: 'goods'
    }
  },

  computed: {
    isLoggedIn() {
      const userStore = useUserStore();
      return userStore.isAuthenticated || false
    },
    username() {
      const userStore = useUserStore();
      return userStore.username || '用户'
    },
    userAvatar() {
      const userStore = useUserStore();
      return userStore.userInfo?.avatar || ''
    }
  },
  
  methods: {
    handleMenuSelect(key) {
      this.activeIndex = key;
      switch (key) {
        case 'home':
          this.$router.push('/home');
          break;
        case 'goods':
          this.$router.push('/goods');
          break;
        case 'test':
          this.$router.push('/test');
          break;
        case 'category':
          this.$message.info('分类页面开发中...');
          break;
        case 'cart':
          this.$message.info('购物车页面开发中...');
          break;
      }
    },

    // 处理用户下拉菜单命令
    handleUserCommand(command) {
      switch (command) {
        case 'profile':
          this.$message.info('个人中心功能开发中...');
          break;
        case 'orders':
          this.$message.info('我的订单功能开发中...');
          break;
        case 'cart':
          this.$message.info('购物车功能开发中...');
          break;
        case 'logout':
          this.handleLogout();
          break;
      }
    },

    // 退出登录
    handleLogout() {
      this.$confirm('确定要退出登录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 清除登录状态
        const userStore = useUserStore();
        userStore.logout();

        this.$message.success('已退出登录');

        // 跳转到登录页
        this.$router.push('/login');
      }).catch(() => {
        // 取消退出
      });
    },

    goToLogin() {
      this.$router.push('/login');
    },

    goToRegister() {
      this.$router.push('/register');
    }
  },
  
  created() {
    // 恢复登录状态
    const userStore = useUserStore();
    userStore.restoreLoginState();
  },

  mounted() {
    // 根据当前路由设置活动菜单项
    const path = this.$route.path;
    if (path.includes('/goods')) {
      this.activeIndex = 'goods';
    } else if (path.includes('/home')) {
      this.activeIndex = 'home';
    } else if (path.includes('/test')) {
      this.activeIndex = 'test';
    }
  }
}
</script>

<style scoped>
.main-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.header {
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  height: 64px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-img {
  height: 40px;
  width: auto;
}

.logo-text {
  font-size: 20px;
  font-weight: bold;
  color: #333;
}

.nav-menu-el {
  border-bottom: none;
}

.user-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-dropdown {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 6px;
  transition: background 0.2s;
}

.user-dropdown:hover {
  background: #f5f5f5;
}

.username {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.dropdown-icon {
  font-size: 12px;
  color: #666;
}

.login-actions {
  display: flex;
  gap: 8px;
}

.main-content {
  flex: 1;
  background: #f5f5f5;
  min-height: calc(100vh - 64px - 60px);
}

.footer {
  background: #333;
  color: white;
  text-align: center;
  padding: 20px 0;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    padding: 0 12px;
  }
  
  .logo-text {
    display: none;
  }
  
  .nav-menu {
    display: none;
  }
  
  .user-actions {
    gap: 4px;
  }
}
</style>
