{"ast": null, "code": "import { createElementVNode as _createElementVNode, createCommentVNode as _createCommentVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, withCtx as _withCtx } from \"vue\";\nconst _hoisted_1 = {\n  class: \"test-page\"\n};\nconst _hoisted_2 = {\n  class: \"test-section\"\n};\nconst _hoisted_3 = {\n  class: \"test-section\"\n};\nconst _hoisted_4 = {\n  key: 0,\n  class: \"search-results\"\n};\nconst _hoisted_5 = {\n  class: \"results-list\"\n};\nconst _hoisted_6 = [\"src\", \"alt\"];\nconst _hoisted_7 = {\n  class: \"result-info\"\n};\nconst _hoisted_8 = {\n  class: \"test-section\"\n};\nconst _hoisted_9 = {\n  key: 0,\n  class: \"test-section\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_CarouselComponent = _resolveComponent(\"CarouselComponent\");\n  const _component_SearchBox = _resolveComponent(\"SearchBox\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_cache[8] || (_cache[8] = _createElementVNode(\"h2\", null, \"功能测试页面\", -1 /* CACHED */)), _createCommentVNode(\" 测试轮播图组件 \"), _createElementVNode(\"div\", _hoisted_2, [_cache[0] || (_cache[0] = _createElementVNode(\"h3\", null, \"轮播图组件测试\", -1 /* CACHED */)), _createVNode(_component_CarouselComponent, {\n    onCarouselClick: $options.handleCarouselClick\n  }, null, 8 /* PROPS */, [\"onCarouselClick\"])]), _createCommentVNode(\" 测试搜索框组件 \"), _createElementVNode(\"div\", _hoisted_3, [_cache[2] || (_cache[2] = _createElementVNode(\"h3\", null, \"搜索框组件测试\", -1 /* CACHED */)), _createVNode(_component_SearchBox, {\n    onSearch: $options.handleSearch,\n    onSearchResults: $options.handleSearchResults,\n    onClearSearch: $options.handleClearSearch\n  }, null, 8 /* PROPS */, [\"onSearch\", \"onSearchResults\", \"onClearSearch\"]), _createCommentVNode(\" 搜索结果显示 \"), $data.searchResults.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_4, [_cache[1] || (_cache[1] = _createElementVNode(\"h4\", null, \"搜索结果：\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_5, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.searchResults, item => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: item.id,\n      class: \"result-item\"\n    }, [_createElementVNode(\"img\", {\n      src: item.imgs,\n      alt: item.name,\n      class: \"result-image\"\n    }, null, 8 /* PROPS */, _hoisted_6), _createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"h5\", null, _toDisplayString(item.name), 1 /* TEXT */), _createElementVNode(\"p\", null, \"¥\" + _toDisplayString(item.price), 1 /* TEXT */)])]);\n  }), 128 /* KEYED_FRAGMENT */))])])) : _createCommentVNode(\"v-if\", true)]), _createCommentVNode(\" API测试按钮 \"), _createElementVNode(\"div\", _hoisted_8, [_cache[6] || (_cache[6] = _createElementVNode(\"h3\", null, \"API测试\", -1 /* CACHED */)), _createVNode(_component_el_button, {\n    onClick: $options.testCarouselAPI,\n    type: \"primary\"\n  }, {\n    default: _withCtx(() => [...(_cache[3] || (_cache[3] = [_createTextVNode(\"测试轮播图API\", -1 /* CACHED */)]))]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClick\"]), _createVNode(_component_el_button, {\n    onClick: $options.testGoodsAPI,\n    type: \"success\"\n  }, {\n    default: _withCtx(() => [...(_cache[4] || (_cache[4] = [_createTextVNode(\"测试商品API\", -1 /* CACHED */)]))]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClick\"]), _createVNode(_component_el_button, {\n    onClick: $options.testSearchAPI,\n    type: \"warning\"\n  }, {\n    default: _withCtx(() => [...(_cache[5] || (_cache[5] = [_createTextVNode(\"测试搜索API\", -1 /* CACHED */)]))]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClick\"])]), _createCommentVNode(\" 测试结果显示 \"), $data.testResults ? (_openBlock(), _createElementBlock(\"div\", _hoisted_9, [_cache[7] || (_cache[7] = _createElementVNode(\"h3\", null, \"测试结果\", -1 /* CACHED */)), _createElementVNode(\"pre\", null, _toDisplayString($data.testResults), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true)]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_createCommentVNode", "_hoisted_2", "_createVNode", "_component_CarouselComponent", "onCarouselClick", "$options", "handleCarouselClick", "_hoisted_3", "_component_SearchBox", "onSearch", "handleSearch", "onSearchResults", "handleSearchResults", "onClearSearch", "handleClearSearch", "$data", "searchResults", "length", "_hoisted_4", "_hoisted_5", "_Fragment", "_renderList", "item", "key", "id", "src", "imgs", "alt", "name", "_hoisted_7", "_toDisplayString", "price", "_hoisted_8", "_component_el_button", "onClick", "testCarouselAPI", "type", "_cache", "testGoodsAPI", "testSearchAPI", "testResults", "_hoisted_9"], "sources": ["D:\\2025_down\\project\\shoppingOnline_2025_91\\shoppingOnline_front-2025-91\\src\\views\\TestPage.vue"], "sourcesContent": ["<template>\n  <div class=\"test-page\">\n    <h2>功能测试页面</h2>\n    \n    <!-- 测试轮播图组件 -->\n    <div class=\"test-section\">\n      <h3>轮播图组件测试</h3>\n      <CarouselComponent @carousel-click=\"handleCarouselClick\" />\n    </div>\n    \n    <!-- 测试搜索框组件 -->\n    <div class=\"test-section\">\n      <h3>搜索框组件测试</h3>\n      <SearchBox \n        @search=\"handleSearch\" \n        @search-results=\"handleSearchResults\"\n        @clear-search=\"handleClearSearch\"\n      />\n      \n      <!-- 搜索结果显示 -->\n      <div v-if=\"searchResults.length > 0\" class=\"search-results\">\n        <h4>搜索结果：</h4>\n        <div class=\"results-list\">\n          <div v-for=\"item in searchResults\" :key=\"item.id\" class=\"result-item\">\n            <img :src=\"item.imgs\" :alt=\"item.name\" class=\"result-image\" />\n            <div class=\"result-info\">\n              <h5>{{ item.name }}</h5>\n              <p>¥{{ item.price }}</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n    \n    <!-- API测试按钮 -->\n    <div class=\"test-section\">\n      <h3>API测试</h3>\n      <el-button @click=\"testCarouselAPI\" type=\"primary\">测试轮播图API</el-button>\n      <el-button @click=\"testGoodsAPI\" type=\"success\">测试商品API</el-button>\n      <el-button @click=\"testSearchAPI\" type=\"warning\">测试搜索API</el-button>\n    </div>\n    \n    <!-- 测试结果显示 -->\n    <div class=\"test-section\" v-if=\"testResults\">\n      <h3>测试结果</h3>\n      <pre>{{ testResults }}</pre>\n    </div>\n  </div>\n</template>\n\n<script>\nimport CarouselComponent from '@/components/Carousel.vue'\nimport SearchBox from '@/components/SearchBox.vue'\nimport request from '@/utils/request.js'\n\nexport default {\n  name: 'TestPage',\n  \n  components: {\n    CarouselComponent,\n    SearchBox\n  },\n  \n  data() {\n    return {\n      searchResults: [],\n      testResults: null\n    }\n  },\n  \n  methods: {\n    handleCarouselClick(item) {\n      console.log('轮播图点击:', item)\n      this.$message.success(`点击了轮播图商品: ${item.goodName}`)\n    },\n    \n    handleSearch(keyword) {\n      console.log('搜索关键词:', keyword)\n      this.$message.info(`搜索: ${keyword}`)\n    },\n    \n    handleSearchResults(results) {\n      console.log('搜索结果:', results)\n      this.searchResults = results.records || []\n    },\n    \n    handleClearSearch() {\n      console.log('清除搜索')\n      this.searchResults = []\n    },\n    \n    async testCarouselAPI() {\n      try {\n        const response = await request.get('/carousel')\n        this.testResults = JSON.stringify(response, null, 2)\n        this.$message.success('轮播图API测试成功')\n      } catch (error) {\n        this.testResults = `轮播图API错误: ${error.message}`\n        this.$message.error('轮播图API测试失败')\n      }\n    },\n    \n    async testGoodsAPI() {\n      try {\n        const response = await request.get('/good')\n        this.testResults = JSON.stringify(response, null, 2)\n        this.$message.success('商品API测试成功')\n      } catch (error) {\n        this.testResults = `商品API错误: ${error.message}`\n        this.$message.error('商品API测试失败')\n      }\n    },\n    \n    async testSearchAPI() {\n      try {\n        const response = await request.get('/good/page', {\n          searchText: '手机',\n          pageNum: 1,\n          pageSize: 5\n        })\n        this.testResults = JSON.stringify(response, null, 2)\n        this.$message.success('搜索API测试成功')\n      } catch (error) {\n        this.testResults = `搜索API错误: ${error.message}`\n        this.$message.error('搜索API测试失败')\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.test-page {\n  max-width: 1000px;\n  margin: 20px auto;\n  padding: 20px;\n}\n\n.test-section {\n  margin-bottom: 40px;\n  padding: 20px;\n  border: 1px solid #e0e0e0;\n  border-radius: 8px;\n  background: white;\n}\n\n.test-section h3 {\n  margin-top: 0;\n  color: #333;\n  border-bottom: 2px solid #409eff;\n  padding-bottom: 10px;\n}\n\n.search-results {\n  margin-top: 20px;\n}\n\n.results-list {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\n  gap: 15px;\n  margin-top: 15px;\n}\n\n.result-item {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  padding: 10px;\n  border: 1px solid #e0e0e0;\n  border-radius: 8px;\n}\n\n.result-image {\n  width: 50px;\n  height: 50px;\n  object-fit: cover;\n  border-radius: 4px;\n}\n\n.result-info h5 {\n  margin: 0 0 5px;\n  font-size: 14px;\n}\n\n.result-info p {\n  margin: 0;\n  color: #e60000;\n  font-weight: bold;\n}\n\npre {\n  background: #f5f5f5;\n  padding: 15px;\n  border-radius: 4px;\n  overflow-x: auto;\n  font-size: 12px;\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAW;;EAIfA,KAAK,EAAC;AAAc;;EAMpBA,KAAK,EAAC;AAAc;;;EAScA,KAAK,EAAC;;;EAEpCA,KAAK,EAAC;AAAc;;;EAGhBA,KAAK,EAAC;AAAa;;EAU3BA,KAAK,EAAC;AAAc;;;EAQpBA,KAAK,EAAC;;;;;;uBA1CbC,mBAAA,CA8CM,OA9CNC,UA8CM,G,0BA7CJC,mBAAA,CAAe,YAAX,QAAM,qBAEVC,mBAAA,aAAgB,EAChBD,mBAAA,CAGM,OAHNE,UAGM,G,0BAFJF,mBAAA,CAAgB,YAAZ,SAAO,qBACXG,YAAA,CAA2DC,4BAAA;IAAvCC,eAAc,EAAEC,QAAA,CAAAC;EAAmB,6C,GAGzDN,mBAAA,aAAgB,EAChBD,mBAAA,CAqBM,OArBNQ,UAqBM,G,0BApBJR,mBAAA,CAAgB,YAAZ,SAAO,qBACXG,YAAA,CAIEM,oBAAA;IAHCC,QAAM,EAAEJ,QAAA,CAAAK,YAAY;IACpBC,eAAc,EAAEN,QAAA,CAAAO,mBAAmB;IACnCC,aAAY,EAAER,QAAA,CAAAS;6EAGjBd,mBAAA,YAAe,EACJe,KAAA,CAAAC,aAAa,CAACC,MAAM,Q,cAA/BpB,mBAAA,CAWM,OAXNqB,UAWM,G,0BAVJnB,mBAAA,CAAc,YAAV,OAAK,qBACTA,mBAAA,CAQM,OARNoB,UAQM,I,kBAPJtB,mBAAA,CAMMuB,SAAA,QAAAC,WAAA,CANcN,KAAA,CAAAC,aAAa,EAArBM,IAAI;yBAAhBzB,mBAAA,CAMM;MAN8B0B,GAAG,EAAED,IAAI,CAACE,EAAE;MAAE5B,KAAK,EAAC;QACtDG,mBAAA,CAA8D;MAAxD0B,GAAG,EAAEH,IAAI,CAACI,IAAI;MAAGC,GAAG,EAAEL,IAAI,CAACM,IAAI;MAAEhC,KAAK,EAAC;yCAC7CG,mBAAA,CAGM,OAHN8B,UAGM,GAFJ9B,mBAAA,CAAwB,YAAA+B,gBAAA,CAAjBR,IAAI,CAACM,IAAI,kBAChB7B,mBAAA,CAAwB,WAArB,GAAC,GAAA+B,gBAAA,CAAGR,IAAI,CAACS,KAAK,iB;6EAO3B/B,mBAAA,aAAgB,EAChBD,mBAAA,CAKM,OALNiC,UAKM,G,0BAJJjC,mBAAA,CAAc,YAAV,OAAK,qBACTG,YAAA,CAAuE+B,oBAAA;IAA3DC,OAAK,EAAE7B,QAAA,CAAA8B,eAAe;IAAEC,IAAI,EAAC;;sBAAU,MAAQ,KAAAC,MAAA,QAAAA,MAAA,O,iBAAR,UAAQ,mB;;kCAC3DnC,YAAA,CAAmE+B,oBAAA;IAAvDC,OAAK,EAAE7B,QAAA,CAAAiC,YAAY;IAAEF,IAAI,EAAC;;sBAAU,MAAO,KAAAC,MAAA,QAAAA,MAAA,O,iBAAP,SAAO,mB;;kCACvDnC,YAAA,CAAoE+B,oBAAA;IAAxDC,OAAK,EAAE7B,QAAA,CAAAkC,aAAa;IAAEH,IAAI,EAAC;;sBAAU,MAAO,KAAAC,MAAA,QAAAA,MAAA,O,iBAAP,SAAO,mB;;oCAG1DrC,mBAAA,YAAe,EACiBe,KAAA,CAAAyB,WAAW,I,cAA3C3C,mBAAA,CAGM,OAHN4C,UAGM,G,0BAFJ1C,mBAAA,CAAa,YAAT,MAAI,qBACRA,mBAAA,CAA4B,aAAA+B,gBAAA,CAApBf,KAAA,CAAAyB,WAAW,iB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}