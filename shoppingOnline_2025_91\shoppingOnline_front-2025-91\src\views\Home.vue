<template>
  <div class="home-container">
    <!-- 欢迎横幅 -->
    <div class="welcome-banner">
      <h1 class="welcome-title">欢迎来到在线购物商城</h1>
      <p class="welcome-subtitle">发现优质商品，享受购物乐趣</p>
    </div>

    <!-- 轮播图组件 -->
    <CarouselComponent @carousel-click="handleCarouselClick" />

    <!-- 搜索框组件 -->
    <div class="search-section">
      <SearchBox
        @search="handleSearch"
        @search-results="handleSearchResults"
        @clear-search="handleClearSearch"
      />
    </div>

    <!-- 推荐商品区域 -->
    <div class="recommended-section">
      <h2 class="section-title">
        <el-icon><Star /></el-icon>
        推荐商品
      </h2>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading">
        <el-skeleton :rows="2" animated />
      </div>

      <!-- 商品列表 -->
      <div v-else class="goods-grid">
        <div
          class="goods-card"
          v-for="good in recommendedGoods"
          :key="good.id"
          @click="showProductDetail(good)"
        >
          <img
            :src="good.imgs"
            :alt="good.name"
            class="goods-image"
            @error="handleImageError"
            @load="handleImageLoad"
          />
          <div class="goods-info">
            <h3 class="goods-name">{{ good.name }}</h3>
            <p class="goods-desc">{{ good.description || '暂无商品描述' }}</p>
            <p class="goods-price">¥{{ good.price }}</p>
            <el-button type="primary" size="small" @click.stop="addToCart(good)">
              加入购物车
            </el-button>
          </div>
        </div>
      </div>

      <!-- 查看更多按钮 -->
      <div class="more-goods">
        <el-button type="primary" size="large" @click="goToGoodsPage">
          查看更多商品
        </el-button>
      </div>
    </div>

    <!-- 商品详情弹窗 -->
    <ProductDetail
      v-model="showDetailDialog"
      :product="selectedProduct"
      @add-to-cart="addToCart"
      @buy-now="buyNow"
    />
  </div>
</template>

<script>
import CarouselComponent from '@/components/Carousel.vue'
import SearchBox from '@/components/SearchBox.vue'
import ProductDetail from '@/components/ProductDetail.vue'

import { Star } from '@element-plus/icons-vue'
import request from '@/utils/request.js'

export default {
  name: 'HomeView',

  components: {
    CarouselComponent,
    SearchBox,
    ProductDetail,
    Star
  },

  data() {
    return {
      loading: true,
      recommendedGoods: [],
      selectedMainCategory: null, // 选中的主分类
      selectedSubCategory: null, // 选中的子分类
      hasCategoryFilter: false, // 是否有分类筛选
      showDetailDialog: false, // 是否显示商品详情弹窗
      selectedProduct: {} // 选中的商品
    };
  },

  methods: {
    // 处理分类变化
    handleCategoryChange(category) {
      this.selectedMainCategory = category.mainCategory
      this.selectedSubCategory = category.subCategory
      this.hasCategoryFilter = category.mainCategory !== null || category.subCategory !== null
      this.fetchRecommendedGoods()
    },

    // 图片加载错误处理
    handleImageError(event) {
      console.error('首页商品图片加载失败:', event.target.src);
      event.target.style.backgroundColor = '#f0f0f0';
      event.target.style.border = '1px solid #ccc';
    },

    // 图片加载成功处理
    handleImageLoad(event) {
      console.log('首页商品图片加载成功:', event.target.src);
    },

    // 获取推荐商品
    async fetchRecommendedGoods() {
      try {
        this.loading = true;
        let params = {};
        
        // 如果有分类筛选，添加分类参数
        if (this.hasCategoryFilter) {
          params = {
            mainCategory: this.selectedMainCategory,
            subCategory: this.selectedSubCategory
          };
        }
        
        const response = await request.get('/good', params);

        if (Array.isArray(response) && response.length > 0) {
          // 使用后端数据，并处理图片URL和确保描述存在
          this.recommendedGoods = response.slice(0, 8).map((item, index) => {
            // 根据商品名称匹配对应的图片
            let imgUrl;
            switch(item.name) {
              case '女上衣':
                imgUrl = require('@/assets/女上衣.png');
                break;
              case '休闲鞋':
                imgUrl = require('@/assets/休闲鞋.png');
                break;
              case '威士忌 大瓶':
                imgUrl = require('@/assets/威士忌 大瓶.png');
                break;
              case '墨镜':
                imgUrl = require('@/assets/墨镜.png');
                break;
              case '桌椅套装':
                imgUrl = require('@/assets/桌椅套装.png');
                break;
              case '儿童简笔画册':
                imgUrl = require('@/assets/儿童简笔画册.png');
                break;
              case '英文版图书':
                imgUrl = require('@/assets/英文版图书.png');
                break;
              case '衬衫':
                imgUrl = require('@/assets/衬衫.png');
                break;
              default: {
                // 默认图片，根据索引循环使用
                const defaultImages = [
                  require('@/assets/女上衣.png'),
                  require('@/assets/休闲鞋.png'),
                  require('@/assets/威士忌 大瓶.png'),
                  require('@/assets/墨镜.png'),
                  require('@/assets/桌椅套装.png')
                ];
                imgUrl = defaultImages[index % defaultImages.length];
                break;
              }
            }
            return {
              ...item,
              imgs: imgUrl,
              description: item.description || '暂无商品描述', // 确保有描述，优先使用后端数据
              price: item.price || (Math.random() * 200 + 50).toFixed(2) // 确保有价格
            };
          });
          console.log('首页使用后端商品数据，添加图片:', this.recommendedGoods);
        } else {
          // 如果后端没有数据，使用本地图片的模拟数据
          this.recommendedGoods = this.getMockProductsWithLocalImages();
          console.log('首页使用本地图片的模拟商品数据:', this.recommendedGoods);
        }
      } catch (error) {
        console.error('获取推荐商品失败:', error);
        // 使用本地图片的模拟数据作为后备
        this.recommendedGoods = this.getMockProductsWithLocalImages();
        console.log('首页API请求失败，使用本地图片的模拟数据:', this.recommendedGoods);
      } finally {
        this.loading = false;
      }
    },

    // 获取使用本地图片的模拟商品数据
    getMockProductsWithLocalImages() {
      return [
        {
          id: 1,
          name: '女上衣',
          description: '时尚女性上衣，舒适面料，多种颜色可选',
          imgs: require('@/assets/女上衣.png'),
          price: 102.00,
          discount: 0.85,
          sales: 120,
          recommend: true
        },
        {
          id: 2,
          name: '休闲鞋',
          description: '舒适透气的休闲运动鞋，适合日常穿着',
          imgs: require('@/assets/休闲鞋.png'),
          price: 162.00,
          discount: 0.90,
          sales: 85,
          recommend: true
        },
        {
          id: 3,
          name: '威士忌 大瓶',
          description: '优质威士忌，口感醇厚，适合收藏和品鉴',
          imgs: require('@/assets/威士忌 大瓶.png'),
          price: 427.50,
          discount: 0.95,
          sales: 45,
          recommend: true
        },
        {
          id: 4,
          name: '墨镜',
          description: '时尚墨镜，防紫外线，多种款式可选',
          imgs: require('@/assets/墨镜.png'),
          price: 199.00,
          discount: 0.80,
          sales: 65,
          recommend: true
        },
        {
          id: 5,
          name: '桌椅套装',
          description: '舒适桌椅套装，适合家庭使用',
          imgs: require('@/assets/桌椅套装.png'),
          price: 1299.00,
          discount: 0.85,
          sales: 30,
          recommend: true
        }
      ];
    },

    // 处理轮播图点击
    handleCarouselClick(item) {
      console.log('点击轮播图商品:', item);
      this.$message.success(`查看商品: ${item.goodName}`);
      // 可以跳转到商品详情页
    },

    // 处理搜索
    handleSearch(keyword) {
      console.log('搜索:', keyword);
      // 跳转到商品页面并传递搜索参数
      this.$router.push({
        path: '/goods',
        query: { search: keyword }
      });
    },

    // 处理搜索结果
    handleSearchResults(results) {
      console.log('搜索结果:', results);
    },

    // 清除搜索
    handleClearSearch() {
      console.log('清除搜索');
    },

    // 添加到购物车
    addToCart(good) {
      console.log('加入购物车:', good.name);
      this.$message.success(`已加入购物车：${good.name}`);
    },

    // 显示商品详情
    showProductDetail(good) {
      this.selectedProduct = good;
      this.showDetailDialog = true;
      console.log('查看商品详情:', good);
    },

    // 立即购买
    buyNow(good) {
      console.log('立即购买:', good.name);
      this.$message.info(`立即购买：${good.name}`);
    },

    // 跳转到商品页面
    goToGoodsPage() {
      // 如果有分类筛选，传递分类参数到商品页面
      if (this.hasCategoryFilter) {
        this.$router.push({
          path: '/goods',
          query: {
            mainCategory: this.selectedMainCategory,
            subCategory: this.selectedSubCategory
          }
        });
      } else {
        this.$router.push('/goods');
      }
    },
    
    // 清除分类筛选
    clearCategoryFilter() {
      // 触发Category组件的clearCategoryFilter方法
      const categoryComponent = this.$refs.categoryComponent;
      if (categoryComponent && typeof categoryComponent.clearCategoryFilter === 'function') {
        categoryComponent.clearCategoryFilter();
      }
    }
  },

  created() {
    this.fetchRecommendedGoods();
  },

  mounted() {
    this.fetchRecommendedGoods();
  }
}
</script>

<style scoped>
.home-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px 16px;
}

.welcome-banner {
  text-align: center;
  margin-bottom: 40px;
  padding: 40px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  color: white;
}

.welcome-title {
  font-size: 36px;
  margin: 0 0 10px;
  font-weight: bold;
}

.welcome-subtitle {
  font-size: 18px;
  margin: 0;
  opacity: 0.9;
}

.search-section {
  margin-bottom: 20px;
}

.search-info {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 30px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #67c23a;
}

.search-count {
  color: #666;
  font-size: 14px;
}

.recommended-section {
  margin-top: 50px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 24px;
  color: #333;
  margin-bottom: 30px;
  padding-bottom: 10px;
  border-bottom: 2px solid #409eff;
}

.loading {
  padding: 40px 0;
  /* 添加最小高度，避免布局跳动 */
  min-height: 300px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.goods-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
  /* 添加最小高度，避免布局跳动 */
  min-height: 300px;
  /* 添加过渡效果 */
  transition: all 0.3s ease-in-out;
}

.goods-card {
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  overflow: hidden;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  /* 添加动画效果 */
  animation: fadeIn 0.6s ease-out;
  /* 添加will-change优化性能 */
  will-change: transform, opacity;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.goods-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
  border-color: #409eff;
}

.goods-image {
  width: 100%;
  height: 160px;
  object-fit: cover;
}

.goods-info {
  padding: 16px;
}

.goods-name {
  font-size: 16px;
  color: #333;
  margin: 0 0 8px;
  font-weight: bold;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.goods-price {
  color: #e60000;
  font-size: 18px;
  font-weight: bold;
  margin: 10px 0 15px;
}

.more-goods {
  text-align: center;
  margin-top: 40px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .home-container {
    padding: 15px 12px;
  }

  .welcome-banner {
    padding: 30px 15px;
    margin-bottom: 30px;
  }

  .welcome-title {
    font-size: 28px;
  }

  .welcome-subtitle {
    font-size: 16px;
  }

  .goods-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
  }

  .section-title {
    font-size: 20px;
  }
}
</style>