<template>
  <div class="home-container">
    <!-- 欢迎横幅 -->
    <div class="welcome-banner">
      <h1 class="welcome-title">欢迎来到在线购物商城</h1>
      <p class="welcome-subtitle">发现优质商品，享受购物乐趣</p>
    </div>

    <!-- 轮播图组件 -->
    <CarouselComponent @carousel-click="handleCarouselClick" />

    <!-- 搜索框组件 -->
    <div class="search-section">
      <SearchBox
        @search="handleSearch"
        @search-results="handleSearchResults"
        @clear-search="handleClearSearch"
      />
    </div>

    <!-- 推荐商品区域 -->
    <div class="recommended-section">
      <h2 class="section-title">
        <el-icon><Star /></el-icon>
        推荐商品
      </h2>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading">
        <el-skeleton :rows="2" animated />
      </div>

      <!-- 商品列表 -->
      <div v-else class="goods-grid">
        <div
          class="goods-card"
          v-for="good in recommendedGoods"
          :key="good.id"
          @click="goToGoodDetail(good)"
        >
          <img :src="good.imgs" :alt="good.name" class="goods-image" />
          <div class="goods-info">
            <h3 class="goods-name">{{ good.name }}</h3>
            <p class="goods-price">¥{{ good.price }}</p>
            <el-button type="primary" size="small" @click.stop="addToCart(good)">
              加入购物车
            </el-button>
          </div>
        </div>
      </div>

      <!-- 查看更多按钮 -->
      <div class="more-goods">
        <el-button type="primary" size="large" @click="goToGoodsPage">
          查看更多商品
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import CarouselComponent from '@/components/Carousel.vue'
import SearchBox from '@/components/SearchBox.vue'
import { Star } from '@element-plus/icons-vue'
import request from '@/utils/request.js'

export default {
  name: 'HomeView',

  components: {
    CarouselComponent,
    SearchBox,
    Star
  },

  data() {
    return {
      loading: true,
      recommendedGoods: []
    };
  },

  methods: {
    // 获取推荐商品
    async fetchRecommendedGoods() {
      try {
        this.loading = true;
        const response = await request.get('/good');

        if (Array.isArray(response) && response.length > 0) {
          this.recommendedGoods = response.slice(0, 8); // 只显示前8个
        } else {
          // 如果后端没有数据，使用本地图片的模拟数据
          this.recommendedGoods = this.getMockProductsWithLocalImages();
          console.log('首页使用本地图片的模拟商品数据');
        }
      } catch (error) {
        console.error('获取推荐商品失败:', error);
        // 使用本地图片的模拟数据作为后备
        this.recommendedGoods = this.getMockProductsWithLocalImages();
        console.log('首页API请求失败，使用本地图片的模拟数据');
      } finally {
        this.loading = false;
      }
    },

    // 获取使用本地图片的模拟商品数据
    getMockProductsWithLocalImages() {
      return [
        {
          id: 1,
          name: '女上衣',
          description: '时尚女性上衣，舒适面料，多种颜色可选',
          imgs: '/images/女上衣.jpg',
          price: 102.00,
          discount: 0.85,
          sales: 120,
          recommend: true
        },
        {
          id: 2,
          name: '休闲鞋',
          description: '舒适透气的休闲运动鞋，适合日常穿着',
          imgs: '/images/休闲鞋.jpg',
          price: 162.00,
          discount: 0.90,
          sales: 85,
          recommend: true
        },
        {
          id: 3,
          name: '威士忌 大瓶',
          description: '优质威士忌，口感醇厚，适合收藏和品鉴',
          imgs: '/images/威士忌 大瓶.jpg',
          price: 427.50,
          discount: 0.95,
          sales: 45,
          recommend: true
        }
      ];
    },

    // 处理轮播图点击
    handleCarouselClick(item) {
      console.log('点击轮播图商品:', item);
      this.$message.success(`查看商品: ${item.goodName}`);
      // 可以跳转到商品详情页
    },

    // 处理搜索
    handleSearch(keyword) {
      console.log('搜索:', keyword);
      // 跳转到商品页面并传递搜索参数
      this.$router.push({
        path: '/goods',
        query: { search: keyword }
      });
    },

    // 处理搜索结果
    handleSearchResults(results) {
      console.log('搜索结果:', results);
    },

    // 清除搜索
    handleClearSearch() {
      console.log('清除搜索');
    },

    // 添加到购物车
    addToCart(good) {
      console.log('加入购物车:', good.name);
      this.$message.success(`已加入购物车：${good.name}`);
    },

    // 跳转到商品详情
    goToGoodDetail(good) {
      console.log('查看商品详情:', good);
      this.$message.info(`查看商品详情: ${good.name}`);
    },

    // 跳转到商品页面
    goToGoodsPage() {
      this.$router.push('/goods');
    }
  },

  created() {
    this.fetchRecommendedGoods();
  },

  mounted() {
    this.fetchRecommendedGoods();
  }
}
</script>

<style scoped>
.home-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px 16px;
}

.welcome-banner {
  text-align: center;
  margin-bottom: 40px;
  padding: 40px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  color: white;
}

.welcome-title {
  font-size: 36px;
  margin: 0 0 10px;
  font-weight: bold;
}

.welcome-subtitle {
  font-size: 18px;
  margin: 0;
  opacity: 0.9;
}

.search-section {
  margin-bottom: 50px;
}

.recommended-section {
  margin-top: 50px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 24px;
  color: #333;
  margin-bottom: 30px;
  padding-bottom: 10px;
  border-bottom: 2px solid #409eff;
}

.loading {
  padding: 40px 0;
}

.goods-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.goods-card {
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  overflow: hidden;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.goods-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
  border-color: #409eff;
}

.goods-image {
  width: 100%;
  height: 160px;
  object-fit: cover;
}

.goods-info {
  padding: 16px;
}

.goods-name {
  font-size: 16px;
  color: #333;
  margin: 0 0 8px;
  font-weight: bold;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.goods-price {
  color: #e60000;
  font-size: 18px;
  font-weight: bold;
  margin: 10px 0 15px;
}

.more-goods {
  text-align: center;
  margin-top: 40px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .home-container {
    padding: 15px 12px;
  }

  .welcome-banner {
    padding: 30px 15px;
    margin-bottom: 30px;
  }

  .welcome-title {
    font-size: 28px;
  }

  .welcome-subtitle {
    font-size: 16px;
  }

  .goods-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
  }

  .section-title {
    font-size: 20px;
  }
}
</style>