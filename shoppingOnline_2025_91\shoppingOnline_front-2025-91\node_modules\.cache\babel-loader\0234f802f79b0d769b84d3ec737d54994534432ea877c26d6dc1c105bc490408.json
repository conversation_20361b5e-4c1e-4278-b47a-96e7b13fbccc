{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nexport default {\n  name: 'MainLayout',\n  data() {\n    return {\n      activeIndex: 'home'\n    };\n  },\n  methods: {\n    handleMenuSelect(key) {\n      this.activeIndex = key;\n      switch (key) {\n        case 'home':\n          this.$router.push('/');\n          break;\n        case 'goods':\n          this.$router.push('/goods');\n          break;\n        case 'category':\n          // this.$router.push('/category');\n          this.$message.info('分类页面开发中...');\n          break;\n        case 'cart':\n          // this.$router.push('/cart');\n          this.$message.info('购物车页面开发中...');\n          break;\n      }\n    },\n    goToLogin() {\n      this.$router.push('/login');\n    },\n    goToRegister() {\n      this.$router.push('/register');\n    }\n  },\n  mounted() {\n    // 根据当前路由设置活动菜单项\n    const path = this.$route.path;\n    if (path.includes('/goods')) {\n      this.activeIndex = 'goods';\n    } else if (path === '/') {\n      this.activeIndex = 'home';\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "activeIndex", "methods", "handleMenuSelect", "key", "$router", "push", "$message", "info", "goToLogin", "goToRegister", "mounted", "path", "$route", "includes"], "sources": ["D:\\2025_down\\project\\shoppingOnline_2025_91\\shoppingOnline_front-2025-91\\src\\views\\MainLayout.vue"], "sourcesContent": ["<template>\n  <div class=\"main-layout\">\n    <!-- 顶部导航栏 -->\n    <header class=\"header\">\n      <div class=\"header-content\">\n        <div class=\"logo\">\n          <img src=\"@/resource/logo.png\" alt=\"商城Logo\" class=\"logo-img\" />\n          <span class=\"logo-text\">在线购物商城</span>\n        </div>\n        \n        <!-- 导航菜单 -->\n        <nav class=\"nav-menu\">\n          <el-menu\n            mode=\"horizontal\"\n            :default-active=\"activeIndex\"\n            class=\"nav-menu-el\"\n            @select=\"handleMenuSelect\"\n          >\n            <el-menu-item index=\"home\">首页</el-menu-item>\n            <el-menu-item index=\"goods\">商品</el-menu-item>\n            <el-menu-item index=\"test\">功能测试</el-menu-item>\n            <el-menu-item index=\"category\">分类</el-menu-item>\n            <el-menu-item index=\"cart\">购物车</el-menu-item>\n          </el-menu>\n        </nav>\n        \n        <!-- 用户操作区 -->\n        <div class=\"user-actions\">\n          <el-button type=\"text\" @click=\"goToLogin\">登录</el-button>\n          <el-button type=\"primary\" @click=\"goToRegister\">注册</el-button>\n        </div>\n      </div>\n    </header>\n    \n    <!-- 主要内容区域 -->\n    <main class=\"main-content\">\n      <router-view />\n    </main>\n    \n    <!-- 底部 -->\n    <footer class=\"footer\">\n      <div class=\"footer-content\">\n        <p>&copy; 2025 在线购物商城. All rights reserved.</p>\n      </div>\n    </footer>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'MainLayout',\n  \n  data() {\n    return {\n      activeIndex: 'home'\n    }\n  },\n  \n  methods: {\n    handleMenuSelect(key) {\n      this.activeIndex = key;\n      switch (key) {\n        case 'home':\n          this.$router.push('/');\n          break;\n        case 'goods':\n          this.$router.push('/goods');\n          break;\n        case 'category':\n          // this.$router.push('/category');\n          this.$message.info('分类页面开发中...');\n          break;\n        case 'cart':\n          // this.$router.push('/cart');\n          this.$message.info('购物车页面开发中...');\n          break;\n      }\n    },\n    \n    goToLogin() {\n      this.$router.push('/login');\n    },\n    \n    goToRegister() {\n      this.$router.push('/register');\n    }\n  },\n  \n  mounted() {\n    // 根据当前路由设置活动菜单项\n    const path = this.$route.path;\n    if (path.includes('/goods')) {\n      this.activeIndex = 'goods';\n    } else if (path === '/') {\n      this.activeIndex = 'home';\n    }\n  }\n}\n</script>\n\n<style scoped>\n.main-layout {\n  min-height: 100vh;\n  display: flex;\n  flex-direction: column;\n}\n\n.header {\n  background: white;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  position: sticky;\n  top: 0;\n  z-index: 1000;\n}\n\n.header-content {\n  max-width: 1200px;\n  margin: 0 auto;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0 16px;\n  height: 64px;\n}\n\n.logo {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.logo-img {\n  height: 40px;\n  width: auto;\n}\n\n.logo-text {\n  font-size: 20px;\n  font-weight: bold;\n  color: #333;\n}\n\n.nav-menu-el {\n  border-bottom: none;\n}\n\n.user-actions {\n  display: flex;\n  gap: 8px;\n}\n\n.main-content {\n  flex: 1;\n  background: #f5f5f5;\n  min-height: calc(100vh - 64px - 60px);\n}\n\n.footer {\n  background: #333;\n  color: white;\n  text-align: center;\n  padding: 20px 0;\n}\n\n.footer-content {\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .header-content {\n    padding: 0 12px;\n  }\n  \n  .logo-text {\n    display: none;\n  }\n  \n  .nav-menu {\n    display: none;\n  }\n  \n  .user-actions {\n    gap: 4px;\n  }\n}\n</style>\n"], "mappings": ";AAiDA,eAAe;EACbA,IAAI,EAAE,YAAY;EAElBC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,WAAW,EAAE;IACf;EACF,CAAC;EAEDC,OAAO,EAAE;IACPC,gBAAgBA,CAACC,GAAG,EAAE;MACpB,IAAI,CAACH,WAAU,GAAIG,GAAG;MACtB,QAAQA,GAAG;QACT,KAAK,MAAM;UACT,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC,GAAG,CAAC;UACtB;QACF,KAAK,OAAO;UACV,IAAI,CAACD,OAAO,CAACC,IAAI,CAAC,QAAQ,CAAC;UAC3B;QACF,KAAK,UAAU;UACb;UACA,IAAI,CAACC,QAAQ,CAACC,IAAI,CAAC,YAAY,CAAC;UAChC;QACF,KAAK,MAAM;UACT;UACA,IAAI,CAACD,QAAQ,CAACC,IAAI,CAAC,aAAa,CAAC;UACjC;MACJ;IACF,CAAC;IAEDC,SAASA,CAAA,EAAG;MACV,IAAI,CAACJ,OAAO,CAACC,IAAI,CAAC,QAAQ,CAAC;IAC7B,CAAC;IAEDI,YAAYA,CAAA,EAAG;MACb,IAAI,CAACL,OAAO,CAACC,IAAI,CAAC,WAAW,CAAC;IAChC;EACF,CAAC;EAEDK,OAAOA,CAAA,EAAG;IACR;IACA,MAAMC,IAAG,GAAI,IAAI,CAACC,MAAM,CAACD,IAAI;IAC7B,IAAIA,IAAI,CAACE,QAAQ,CAAC,QAAQ,CAAC,EAAE;MAC3B,IAAI,CAACb,WAAU,GAAI,OAAO;IAC5B,OAAO,IAAIW,IAAG,KAAM,GAAG,EAAE;MACvB,IAAI,CAACX,WAAU,GAAI,MAAM;IAC3B;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}