<template>
  <div class="goods-container">
    <!-- 页面标题 -->
    <h1 class="page-title">在线购物商城</h1>

    <!-- 轮播图组件 -->
    <CarouselComponent @carousel-click="handleCarouselClick" />

    <!-- 搜索框组件 -->
    <SearchBox
      @search="handleSearch"
      @search-results="handleSearchResults"
      @clear-search="handleClearSearch"
    />

    <!-- 搜索结果提示 -->
    <div v-if="searchKeyword" class="search-info">
      <el-tag type="info" closable @close="handleClearSearch">
        搜索结果：{{ searchKeyword }}
      </el-tag>
      <span class="search-count">共找到 {{ totalCount }} 件商品</span>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading">
      <el-skeleton :rows="3" animated />
    </div>

    <!-- 商品列表 -->
    <div v-else class="goods-list">
      <div
        class="goods-card"
        v-for="good in goodsList"
        :key="good.id"
      >
        <img
          :src="good.imgs"
          :alt="good.name"
          class="goods-image"
          @error="handleImageError"
          @load="handleImageLoad"
        />
        <div class="goods-info">
          <h3 class="goods-name">{{ good.name }}</h3>
          <p class="goods-desc" v-if="good.description">{{ good.description }}</p>
          <p class="goods-price"><strong>¥{{ good.price }}</strong></p>
          <button class="btn-add-cart" @click="addToCart(good)">
            加入购物车
          </button>
        </div>
      </div>
    </div>

    <!-- 无数据-->
    <div v-if="!loading && goodsList.length === 0" class="no-data">
      <el-empty description="暂无商品" />
    </div>

    <!-- 分页组件 -->
    <div v-if="totalCount > pageSize" class="pagination-wrapper">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="totalCount"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script>
import request from '@/utils/request.js'
import CarouselComponent from '@/components/Carousel.vue'
import SearchBox from '@/components/SearchBox.vue'

export default {
  name: 'GoodsView',

  components: {
    CarouselComponent,
    SearchBox
  },

  data() {
    return {
      name: '',
      awesome: false,
      loading: true,
      goodsList: [],
      searchKeyword: '', // 当前搜索关键词
      currentPage: 1,
      pageSize: 20,
      totalCount: 0,
      isSearchMode: false // 是否处于搜索模式
    };
  },

  computed: {
    // 计算商品总数
    totalGoods() {
      return this.goodsList.length;
    }
  },

  watch: {
    // 监听路由参数变化
    '$route.query.search': {
      handler(newSearch) {
        if (newSearch) {
          this.handleSearch(newSearch);
        } else {
          this.handleClearSearch();
        }
      },
      immediate: false
    }
  },

  created() {
    // 检查是否有搜索参数
    const searchQuery = this.$route.query.search;
    if (searchQuery) {
      this.handleSearch(searchQuery);
    } else {
      this.findFrontGoods();
    }
  },

  mounted() {
    // 调试：输出图片路径信息
    console.log('当前页面URL:', window.location.href);
    console.log('图片路径测试:', './images/women-top.jpg');
    console.log('绝对路径测试:', '/images/women-top.jpg');

    // 查询推荐商品
    this.findFrontGoods();
  },

  methods: {
    // 图片加载错误处理
    handleImageError(event) {
      console.error('图片加载失败:', event.target.src);
      console.error('图片元素:', event.target);
      // 设置默认图片或隐藏图片
      event.target.style.display = 'none';
      event.target.style.backgroundColor = '#f0f0f0';
      event.target.style.border = '1px solid #ccc';
    },

    // 图片加载成功处理
    handleImageLoad(event) {
      console.log('图片加载成功:', event.target.src);
      console.log('图片尺寸:', event.target.naturalWidth, 'x', event.target.naturalHeight);
    },

    // 添加到购物车
    addToCart(good) {
      console.log('加入购物车:', good.name);
      this.$emit('add-to-cart', good);
      this.$message.success(`已加入购物车：${good.name}`);
    },

    // 查询推荐商品
    async findFrontGoods() {
      try {
        this.loading = true;
        const response = await request.get('/good');

        if (Array.isArray(response) && response.length > 0) {
          this.goodsList = response;
          console.log('使用后端商品数据:', response);
        } else {
          // 如果后端没有数据，使用本地图片的模拟数据
          this.goodsList = this.getMockProductsWithLocalImages();
          console.log('使用本地图片的模拟商品数据:', this.goodsList);
        }

        this.totalCount = this.goodsList.length;
        this.isSearchMode = false;
      } catch (error) {
        console.error('请求失败:', error.message || error);
        // 使用本地图片的模拟数据作为后备
        this.goodsList = this.getMockProductsWithLocalImages();
        this.totalCount = this.goodsList.length;
        console.log('API请求失败，使用本地图片的模拟数据:', this.goodsList);
      } finally {
        this.loading = false;
      }
    },

    // 获取使用本地图片的模拟商品数据
    getMockProductsWithLocalImages() {
      return [
        {
          id: 1,
          name: '女上衣',
          description: '时尚女性上衣，舒适面料，多种颜色可选',
          imgs: '/images/women-top.jpg',
          price: 102.00,
          discount: 0.85,
          sales: 120,
          recommend: true
        },
        {
          id: 2,
          name: '休闲鞋',
          description: '舒适透气的休闲运动鞋，适合日常穿着',
          imgs: '/images/casual-shoes.jpg',
          price: 162.00,
          discount: 0.90,
          sales: 85,
          recommend: true
        },
        {
          id: 3,
          name: '威士忌 大瓶',
          description: '优质威士忌，口感醇厚，适合收藏和品鉴',
          imgs: '/images/whiskey.jpg',
          price: 427.50,
          discount: 0.95,
          sales: 45,
          recommend: true
        }
      ];
    },

    // 处理搜索
    async handleSearch(keyword) {
      this.searchKeyword = keyword;
      this.isSearchMode = true;
      this.currentPage = 1;
      await this.searchGoods();
    },

    // 搜索商品
    async searchGoods() {
      try {
        this.loading = true;
        const response = await request.get('/good/page', {
          searchText: this.searchKeyword,
          pageNum: this.currentPage,
          pageSize: this.pageSize
        });

        if (response && response.records) {
          this.goodsList = response.records;
          this.totalCount = response.total || 0;
        } else {
          this.goodsList = [];
          this.totalCount = 0;
        }
      } catch (error) {
        console.error('搜索失败:', error);
        this.goodsList = [];
        this.totalCount = 0;
        this.$message.error('搜索失败，请重试');
      } finally {
        this.loading = false;
      }
    },

    // 处理搜索结果
    handleSearchResults(results) {
      if (results && results.records) {
        this.goodsList = results.records;
        this.totalCount = results.total || 0;
      }
    },

    // 清除搜索
    handleClearSearch() {
      this.searchKeyword = '';
      this.isSearchMode = false;
      this.currentPage = 1;
      this.findFrontGoods();
    },

    // 处理轮播图点击
    handleCarouselClick(item) {
      console.log('点击轮播图商品:', item);
      // 可以跳转到商品详情页
      // this.$router.push(`/goods/${item.goodId}`);
    },

    // 处理页面大小变化
    handleSizeChange(newSize) {
      this.pageSize = newSize;
      this.currentPage = 1;
      if (this.isSearchMode) {
        this.searchGoods();
      } else {
        this.findFrontGoods();
      }
    },

    // 处理当前页变化
    handleCurrentChange(newPage) {
      this.currentPage = newPage;
      if (this.isSearchMode) {
        this.searchGoods();
      } else {
        this.findFrontGoods();
      }
    }
  },



  beforeUnmount() {
    console.log('商品页面即将卸载');
  }
};
</script>

<style scoped>
.goods-container {
  max-width: 1200px;
  margin: 20px auto;
  padding: 0 16px;
}

.page-title {
  text-align: center;
  color: #333;
  margin-bottom: 30px;
  font-size: 32px;
  font-weight: bold;
  background: linear-gradient(45deg, #409eff, #67c23a);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.search-info {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.search-count {
  color: #666;
  font-size: 14px;
}

.loading {
  padding: 40px 0;
}

.no-data {
  text-align: center;
  padding: 60px 0;
}

.goods-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 30px;
}

.goods-card {
  border: 1px solid #e0e0e0;
  border-radius: 16px;
  overflow: hidden;
  background: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
}

.goods-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);
  border-color: #409eff;
}

.goods-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.goods-card:hover .goods-image {
  transform: scale(1.05);
}

.goods-info {
  padding: 20px;
}

.goods-name {
  font-size: 18px;
  color: #333;
  margin: 0 0 8px;
  font-weight: bold;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.goods-desc {
  color: #666;
  font-size: 14px;
  margin: 0 0 12px;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.goods-price {
  color: #e60000;
  margin: 15px 0;
  font-size: 20px;
  font-weight: bold;
}

.btn-add-cart {
  width: 100%;
  padding: 12px;
  background: linear-gradient(45deg, #409eff, #67c23a);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.btn-add-cart:hover {
  background: linear-gradient(45deg, #337ecc, #5daf34);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
}

.btn-add-cart:active {
  transform: translateY(0);
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 40px;
  padding: 20px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .goods-container {
    padding: 0 12px;
  }

  .page-title {
    font-size: 24px;
    margin-bottom: 20px;
  }

  .goods-list {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 16px;
  }

  .goods-card {
    border-radius: 12px;
  }

  .goods-image {
    height: 160px;
  }

  .goods-info {
    padding: 16px;
  }

  .goods-name {
    font-size: 16px;
  }

  .goods-price {
    font-size: 18px;
  }

  .btn-add-cart {
    padding: 10px;
    font-size: 14px;
  }

  .search-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .goods-list {
    grid-template-columns: 1fr;
  }

  .page-title {
    font-size: 20px;
  }
}
</style>