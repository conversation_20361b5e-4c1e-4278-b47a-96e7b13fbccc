<template>
  <div class="goods-container">
  

    <h1 >在线购物商城</h1>
    <!-- 加载状态 -->
    <div v-if="loading" class="loading">
      加载中，请稍候...
    </div>
    
    <!-- 商品列表 -->
    <div v-else class="goods-list">
      <div
        class="goods-card"
        v-for="good in goodsList"
        :key="good.id"
      >
        <img :src="good.imgs" :alt="good.name" class="goods-image" />
        <div class="goods-info">
          <h3 class="goods-name">{{ good.name }}</h3>
          <p class="goods-price"><strong>¥{{ good.price }}</strong></p>
          <button class="btn-add-cart" @click="addToCart(good)">
            加入购物车
          </button>
        </div>
      </div>
    </div>

    <!-- 无数据-->
    <div v-if="!loading && goodsList.length === 0" class="no-data">
      暂无商品
    </div>
  </div>
</template>

<script>
import request from '@/utils/request.js' // 路径根据你的项目结构调整
export default {
  name: 'GoodsView',

  data() {
    return {
       name: '',   // 初始为空
       awesome:false,
      loading: true,
      goodsList: []
    };
  },

  computed: {
    // 示例：计算商品总数
    totalGoods() {
      return this.goodsList.length;
    }
  },

  created() {
     //查询推荐商品
     this.findFeontGoods();
    // 模拟从后端获取商品数据
    setTimeout(() => {
      this.loading = false;
    }, 800); // 模拟网络延迟
  },
  //方法集
  methods: {
    //添加到购物车
    addToCart(good) {
      // TODO: 实际项目中调用 Vuex 或 API
      console.log('加入购物车:', good.name);
      this.$emit('add-to-cart', good); // 可用于父组件监听
      alert(`已加入购物车：${good.name}`);
    },
    //查询推荐商品
    findFeontGoods(){
    request.get('/good/findFrontGoods')
    .then(response => {
      this.goodsList = Array.isArray(response) ? response : []
    })
    .catch(error => {
      //  请求失败（网络错误 或 业务 code !== '200'）
      console.error('请求失败:', error.message || error)
      this.goodsList = []
    })
    .finally(() => {
      // ✅ 无论成功失败，都关闭 loading
      this.loading = false
    })
    },
  },

  mounted() {
     //查询推荐商品
     this.findFeontGoods();
  },

  beforeUnmount() {
    console.log('商品页面即将卸载');
  },
   clear() {
      this.name = '';
    }
};
</script>

<style scoped>
.goods-container {
  max-width: 1200px;
  margin: 20px auto;
  padding: 0 16px;
}

.page-title {
  text-align: center;
  color: #333;
  margin-bottom: 20px;
  font-size: 28px;
}

.loading,
.no-data {
  text-align: center;
  color: #999;
  font-size: 16px;
  padding: 40px 0;
}

.goods-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
}

.goods-card {
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  overflow: hidden;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s, box-shadow 0.3s;
}

.goods-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
}

.goods-image {
  width: 100%;
  height: 180px;
  object-fit: cover;
}

.goods-info {
  padding: 16px;
}

.goods-name {
  font-size: 18px;
  color: #333;
  margin: 0 0 8px;
  font-weight: bold;
}

.goods-desc {
  color: #666;
  font-size: 14px;
  margin: 0 0 10px;
  line-height: 1.4;
}

.goods-price {
  color: #e60000;
  margin: 10px 0;
  font-size: 16px;
}

.btn-add-cart {
  width: 100%;
  padding: 10px;
  background-color: #42b983;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.3s;
}

.btn-add-cart:hover {
  background-color: #369a6e;
}

/* 响应式：手机适配 */
@media (max-width: 768px) {
  .goods-list {
    grid-template-columns: 1fr;
    padding: 0 8px;
  }

  .page-title {
    font-size: 24px;
  }
}
</style>