<template>
  <div class="category-container">
    <!-- 页面标题 -->
    <h1 class="page-title">商品分类</h1>

    <!-- 分类列表 -->
    <div class="category-section">
      <el-card class="category-card">
        <div class="category-header">
          <h2>选择分类</h2>
          <el-button 
            v-if="selectedCategory" 
            type="info" 
            size="small" 
            @click="clearCategory"
          >
            清除筛选
          </el-button>
        </div>
        
        <div class="category-list">
          <el-button
            :class="['category-btn', { 'active': selectedCategory === null }]"
            @click="selectCategory(null)"
            size="large"
          >
            <el-icon><Grid /></el-icon>
            全部商品
          </el-button>
          
          <el-button
            v-for="category in categories"
            :key="category.id"
            :class="['category-btn', { 'active': selectedCategory === category.id }]"
            @click="selectCategory(category)"
            size="large"
          >
            <el-icon><Box /></el-icon>
            {{ category.name }}
          </el-button>
        </div>
      </el-card>
    </div>

    <!-- 当前分类信息 -->
    <div v-if="selectedCategory" class="current-category">
      <el-alert
        :title="`当前分类：${selectedCategoryName}`"
        type="info"
        :closable="false"
        show-icon
      />
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading">
      <el-skeleton :rows="3" animated />
    </div>

    <!-- 商品列表 -->
    <div v-else-if="goodsList.length > 0" class="goods-section">
      <div class="goods-header">
        <h3>{{ selectedCategoryName || '全部' }}商品 ({{ goodsList.length }})</h3>
        <el-button type="primary" @click="goToGoodsPage">查看更多</el-button>
      </div>
      
      <div class="goods-list" :class="{ 'loading-transition': loading }">
        <div
          class="goods-card"
          v-for="good in goodsList"
          :key="good.id"
          @click="showProductDetail(good)"
        >
          <img
            :src="good.imgs"
            :alt="good.name"
            class="goods-image"
            @error="handleImageError"
          />
          <div class="goods-info">
            <h4 class="goods-name">{{ good.name }}</h4>
            <p class="goods-desc">{{ good.description || '暂无商品描述' }}</p>
            <p class="goods-price">¥{{ good.price }}</p>
            <div class="goods-actions">
              <el-button 
                type="primary" 
                size="small" 
                @click.stop="addToCart(good)"
              >
                加入购物车
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 无数据 -->
    <div v-else class="no-data">
      <el-empty description="该分类暂无商品" />
    </div>

    <!-- 商品详情弹窗 -->
    <ProductDetail
      v-model="showDetailDialog"
      :product="selectedProduct"
      @add-to-cart="addToCart"
      @buy-now="buyNow"
    />
  </div>
</template>

<script>
import { Grid, Box } from '@element-plus/icons-vue'
import request from '@/utils/request.js'
import ProductDetail from '@/components/ProductDetail.vue'

export default {
  name: 'CategoryView',
  
  components: {
    Grid,
    Box,
    ProductDetail
  },
  
  data() {
    return {
      loading: false,
      categories: [],
      selectedCategory: null,
      selectedCategoryName: '',
      goodsList: [],
      showDetailDialog: false,
      selectedProduct: {}
    }
  },
  
  created() {
    // 先获取分类数据，然后再获取商品数据，避免同时进行多个异步操作导致的闪烁
    this.fetchCategories().then(() => {
      this.fetchGoods()
    })
  },
  
  methods: {
    // 获取分类数据
    async fetchCategories() {
      try {
        const response = await request.get('/categoryAPI')
        
        if (Array.isArray(response)) {
          this.categories = response
          console.log('获取分类数据成功:', this.categories)
        } else {
          // 使用默认分类
          this.categories = this.getDefaultCategories()
        }
        return Promise.resolve()
      } catch (error) {
        console.error('获取分类数据失败:', error)
        this.categories = this.getDefaultCategories()
        return Promise.resolve()
      }
    },
    
    // 获取默认分类数据
    getDefaultCategories() {
      return [
        { id: 1, name: '服装' },
        { id: 2, name: '鞋类' },
        { id: 3, name: '酒类' },
        { id: 4, name: '日用百货' },
        { id: 5, name: '家用电器' },
        { id: 6, name: '数码产品' }
      ]
    },
    
    // 选择分类
    selectCategory(category) {
      if (category === null) {
        this.selectedCategory = null
        this.selectedCategoryName = ''
      } else {
        this.selectedCategory = category.id
        this.selectedCategoryName = category.name
      }
      this.fetchGoods()
    },
    
    // 清除分类筛选
    clearCategory() {
      this.selectedCategory = null
      this.selectedCategoryName = ''
      this.fetchGoods()
    },
    
    // 获取商品数据
    async fetchGoods() {
      try {
        this.loading = true
        let params = {}
        
        // 如果有分类筛选，添加分类参数
        if (this.selectedCategory) {
          params.categoryId = this.selectedCategory
        }
        
        const response = await request.get('/good', { params })
        
        if (Array.isArray(response) && response.length > 0) {
          // 使用后端数据，并为每个商品添加图片和确保描述存在
          // 先存储数据，然后在nextTick中更新，减少闪烁
          const processedGoods = response.map((item) => {
            // 为每个分类提供对应的图片
            const imageMap = {
              1: '/images/women-top.jpg', // 服装
              2: '/images/casual-shoes.jpg', // 鞋类
              3: '/images/whiskey.jpg', // 酒类
              4: '/images/home-supplies.jpg', // 日用百货
              5: '/images/appliance.jpg', // 家用电器
              6: '/images/digital.jpg' // 数码产品
            }
            return {
              ...item,
              imgs: item.imgs || imageMap[item.categoryId] || imageMap[1],
              description: item.description || this.getDefaultDescription(item.name), // 使用默认描述
              price: item.price || (Math.random() * 200 + 50).toFixed(2) // 确保有价格
            }
          })
          
          // 使用nextTick确保DOM更新完成后再显示数据，减少闪烁
          this.$nextTick(() => {
            this.goodsList = processedGoods
            console.log('分类页面获取商品数据成功:', this.goodsList)
          })
        } else {
          // 使用模拟数据
          const mockGoods = this.getMockProducts()
          this.$nextTick(() => {
            this.goodsList = mockGoods
          })
        }
      } catch (error) {
        console.error('获取商品数据失败:', error)
        const mockGoods = this.getMockProducts()
        this.$nextTick(() => {
          this.goodsList = mockGoods
        })
      } finally {
        // 延迟关闭loading状态，确保数据已经渲染完成
        setTimeout(() => {
          this.loading = false
        }, 100)
      }
    },
    
    // 获取默认商品描述
    getDefaultDescription(productName) {
      const descriptions = {
        '女上衣': '时尚女性上衣，舒适面料，多种颜色可选，适合日常穿着和商务场合，版型修身显瘦',
        '休闲鞋': '舒适透气的休闲运动鞋，适合日常穿着，防滑耐磨，多种尺码可选，时尚百搭',
        '威士忌 大瓶': '优质威士忌，口感醇厚，适合收藏和品鉴，酒精度40%，容量750ml，包装精美',
        '墨镜': '时尚太阳镜，UV400防护，轻量化设计，多种款式可选',
        '桌椅套装': '实木桌椅套装，环保材质，结实耐用，适合家庭使用',
        '儿童简笔画册': '儿童启蒙绘画册，内容丰富，培养孩子创造力和想象力',
        '英文版图书': '原版英文图书，提高英语阅读能力，内容精彩有趣',
        '衬衫': '商务休闲衬衫，免烫面料，版型合身，适合各种场合'
      }
      return descriptions[productName] || '优质商品，品质保证，欢迎选购'
    },

    // 获取模拟商品数据
    getMockProducts() {
      // 为每个分类添加足够的模拟商品
      const allProducts = [
        // 服装分类
        {
          id: 1,
          name: '女上衣',
          description: '时尚女性上衣，舒适面料，多种颜色可选，适合日常穿着和商务场合',
          imgs: '/images/women-top.jpg',
          price: 102.00,
          categoryId: 1
        },
        {
          id: 8,
          name: '男士衬衫',
          description: '优质棉质男士衬衫，修身版型，多种颜色可选，适合商务和休闲场合',
          imgs: '/images/women-top.jpg',
          price: 129.00,
          categoryId: 1
        },
        {
          id: 9,
          name: '牛仔裤',
          description: '经典款式牛仔裤，弹力面料，舒适耐穿，多尺码可选',
          imgs: '/images/women-top.jpg',
          price: 159.00,
          categoryId: 1
        },
        
        // 鞋类分类
        {
          id: 2,
          name: '休闲鞋',
          description: '舒适透气的休闲运动鞋，适合日常穿着，防滑耐磨，多种尺码可选',
          imgs: '/images/casual-shoes.jpg',
          price: 162.00,
          categoryId: 2
        },
        {
          id: 10,
          name: '正装皮鞋',
          description: '头层牛皮正装皮鞋，舒适透气，经典款式，适合商务场合',
          imgs: '/images/casual-shoes.jpg',
          price: 399.00,
          categoryId: 2
        },
        {
          id: 11,
          name: '运动鞋',
          description: '专业跑步鞋，缓震设计，透气网面，提供舒适的运动体验',
          imgs: '/images/casual-shoes.jpg',
          price: 499.00,
          categoryId: 2
        },
        
        // 酒类分类
        {
          id: 3,
          name: '威士忌 大瓶',
          description: '优质威士忌，口感醇厚，适合收藏和品鉴，酒精度40%，容量750ml',
          imgs: '/images/whiskey.jpg',
          price: 427.50,
          categoryId: 3
        },
        {
          id: 12,
          name: '红酒',
          description: '法国进口红酒，醇厚果香，单宁柔和，适合搭配牛排等红肉',
          imgs: '/images/whiskey.jpg',
          price: 298.00,
          categoryId: 3
        },
        {
          id: 13,
          name: '啤酒',
          description: '精酿啤酒，原麦汁浓度高，口感醇厚，泡沫丰富',
          imgs: '/images/whiskey.jpg',
          price: 88.00,
          categoryId: 3
        },
        
        // 日用百货分类
        {
          id: 14,
          name: '毛巾套装',
          description: '纯棉毛巾套装，柔软吸水，不掉毛，多色可选',
          imgs: '/images/women-top.jpg',
          price: 59.90,
          categoryId: 4
        },
        {
          id: 15,
          name: '洗发水',
          description: '去屑控油洗发水，天然成分，温和不刺激，适合各种发质',
          imgs: '/images/women-top.jpg',
          price: 89.00,
          categoryId: 4
        },
        {
          id: 16,
          name: '牙膏',
          description: '美白牙膏，含氟配方，有效防蛀，清新口气',
          imgs: '/images/women-top.jpg',
          price: 29.90,
          categoryId: 4
        },
        
        // 家用电器分类
        {
          id: 17,
          name: '电饭煲',
          description: '智能电饭煲，多种烹饪模式，大容量，不粘内胆',
          imgs: '/images/women-top.jpg',
          price: 399.00,
          categoryId: 5
        },
        {
          id: 18,
          name: '微波炉',
          description: '家用微波炉，多种功能，智能控制，操作简便',
          imgs: '/images/women-top.jpg',
          price: 599.00,
          categoryId: 5
        },
        {
          id: 19,
          name: '电风扇',
          description: '落地电风扇，静音设计，多档风速，广角送风',
          imgs: '/images/women-top.jpg',
          price: 199.00,
          categoryId: 5
        },
        
        // 数码产品分类
        {
          id: 20,
          name: '智能手机',
          description: '新款智能手机，全面屏设计，高性能处理器，拍照清晰',
          imgs: '/images/women-top.jpg',
          price: 2999.00,
          categoryId: 6
        },
        {
          id: 21,
          name: '无线耳机',
          description: '真无线蓝牙耳机，主动降噪，长续航，音质出色',
          imgs: '/images/women-top.jpg',
          price: 799.00,
          categoryId: 6
        },
        {
          id: 22,
          name: '智能手表',
          description: '多功能智能手表，健康监测，运动追踪，防水设计',
          imgs: '/images/women-top.jpg',
          price: 1299.00,
          categoryId: 6
        }
      ]
      
      // 根据选中的分类筛选商品
      if (this.selectedCategory) {
        return allProducts.filter(product => product.categoryId === this.selectedCategory)
      }
      
      return allProducts
    },
    
    // 显示商品详情
    showProductDetail(good) {
      this.selectedProduct = good
      this.showDetailDialog = true
    },
    
    // 添加到购物车
    addToCart(good) {
      console.log('加入购物车:', good.name)
      this.$message.success(`已加入购物车：${good.name}`)
    },
    
    // 立即购买
    buyNow(good) {
      console.log('立即购买:', good.name)
      this.$message.info(`立即购买：${good.name}`)
    },
    
    // 图片加载错误处理
    handleImageError(event) {
      event.target.src = '/images/women-top.jpg'
    },
    
    // 跳转到商品页面
    goToGoodsPage() {
      if (this.selectedCategory) {
        this.$router.push({
          path: '/goods',
          query: { categoryId: this.selectedCategory }
        })
      } else {
        this.$router.push('/goods')
      }
    }
  }
}
</script>

<style scoped>
.category-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.page-title {
  text-align: center;
  color: #333;
  margin-bottom: 30px;
  font-size: 28px;
}

.category-section {
  margin-bottom: 30px;
}

.category-card {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.category-header h2 {
  margin: 0;
  color: #333;
}

.category-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
}

.category-btn {
  height: 60px;
  border-radius: 8px;
  border: 2px solid #e0e0e0;
  background: white;
  transition: all 0.3s ease;
}

.category-btn:hover {
  border-color: #409eff;
  color: #409eff;
}

.category-btn.active {
  border-color: #409eff;
  background: #409eff;
  color: white;
}

.current-category {
  margin-bottom: 20px;
}

/* 加载状态优化 */
.loading {
  padding: 40px 0;
  /* 添加最小高度，避免布局跳动 */
  min-height: 400px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.goods-section {
  margin-top: 20px;
}

.goods-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.goods-header h3 {
  margin: 0;
  color: #333;
}

.goods-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
  /* 添加过渡效果 */
  transition: all 0.3s ease-in-out;
  opacity: 1;
  /* 添加最小高度，避免布局跳动 */
  min-height: 400px;
}

/* 数据加载时的过渡效果 */
.goods-list.loading-transition {
  opacity: 0.7;
  transform: scale(0.98);
}

.goods-card {
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  overflow: hidden;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  /* 添加动画效果 */
  animation: fadeIn 0.6s ease-out;
  /* 添加will-change优化性能 */
  will-change: transform, opacity;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.goods-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.goods-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.goods-info {
  padding: 15px;
}

.goods-name {
  font-size: 16px;
  font-weight: bold;
  margin: 0 0 8px;
  color: #333;
}

.goods-desc {
  font-size: 14px;
  color: #666;
  margin: 0 0 10px;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.goods-price {
  font-size: 18px;
  font-weight: bold;
  color: #e60000;
  margin: 0 0 12px;
}

.goods-actions {
  text-align: center;
}

/* 无数据状态优化 */
.no-data {
  text-align: center;
  padding: 60px 0;
  /* 添加最小高度，避免布局跳动 */
  min-height: 400px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .category-list {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 10px;
  }
  
  .category-btn {
    height: 50px;
    font-size: 14px;
  }
  
  .goods-list {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 15px;
  }
}
</style>
