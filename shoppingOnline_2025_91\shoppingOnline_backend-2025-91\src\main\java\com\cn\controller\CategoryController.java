package com.cn.controller;

import com.cn.annotation.Authority;
import com.cn.common.Result;
import com.cn.constants.Constants;
import com.cn.entity.AuthorityType;
import com.cn.entity.Category;
import com.cn.service.CategoryService;
import com.cn.utils.BaseApi;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Authority(AuthorityType.noRequire)
@RestController
@RequestMapping("/categoryAPI")
public class CategoryController {

    @Autowired
    private CategoryService categoryService;


    /*
   查询
   */
    @GetMapping("/{id}")
    public Result findById(@PathVariable Long id) {
        return Result.success(categoryService.getById(id));
    }

    /**
     * 查询所有的商品类别
     * @return
     */
    @GetMapping
    public Result findAll() {
        List<Category> list = categoryService.list();
        return Result.success(list);
    }

    /**
     * 保存
     * @param category
     * @return
     */
    @PostMapping
    public Result save(@RequestBody Category category) {
        boolean flag = categoryService.saveOrUpdate(category);
        return flag? Result.success():Result.error(Constants.CODE_500,"新增数据失败");
    }

    /**
     *  新增下级分类 + 上下级分类关联
     *
     * @param category 下级分类
     * @return 结果
     */
    @PostMapping("/add")
    public Map<String, Object> add(@RequestBody Category category) {
        categoryService.add(category);
        return BaseApi.success();
    }

    @Authority(AuthorityType.requireAuthority)
    @PutMapping
    public Result update(@RequestBody Category category) {
        categoryService.updateById(category);
        return Result.success();
    }


    /**
     * 删除分类
     *
     * @param id id
     * @return 结果
     */
    @Authority(AuthorityType.requireAuthority)
    @GetMapping("/delete")
    public Map<String, Object> delete(@RequestParam("id") Long id) {
        return categoryService.delete(id);
    }

}
