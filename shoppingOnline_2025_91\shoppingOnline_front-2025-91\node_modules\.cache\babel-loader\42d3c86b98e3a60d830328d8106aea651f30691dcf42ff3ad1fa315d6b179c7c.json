{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport CarouselComponent from '@/components/Carousel.vue';\nimport SearchBox from '@/components/SearchBox.vue';\nimport ProductDetail from '@/components/ProductDetail.vue';\nimport { Star } from '@element-plus/icons-vue';\nimport request from '@/utils/request.js';\nexport default {\n  name: 'HomeView',\n  components: {\n    CarouselComponent,\n    SearchBox,\n    ProductDetail,\n    Star\n  },\n  data() {\n    return {\n      loading: true,\n      recommendedGoods: [],\n      selectedMainCategory: null,\n      // 选中的主分类\n      selectedSubCategory: null,\n      // 选中的子分类\n      hasCategoryFilter: false,\n      // 是否有分类筛选\n      showDetailDialog: false,\n      // 是否显示商品详情弹窗\n      selectedProduct: {} // 选中的商品\n    };\n  },\n  methods: {\n    // 处理分类变化\n    handleCategoryChange(category) {\n      this.selectedMainCategory = category.mainCategory;\n      this.selectedSubCategory = category.subCategory;\n      this.hasCategoryFilter = category.mainCategory !== null || category.subCategory !== null;\n      this.fetchRecommendedGoods();\n    },\n    // 图片加载错误处理\n    handleImageError(event) {\n      console.error('首页商品图片加载失败:', event.target.src);\n      event.target.style.backgroundColor = '#f0f0f0';\n      event.target.style.border = '1px solid #ccc';\n    },\n    // 图片加载成功处理\n    handleImageLoad(event) {\n      console.log('首页商品图片加载成功:', event.target.src);\n    },\n    // 获取推荐商品\n    async fetchRecommendedGoods() {\n      try {\n        this.loading = true;\n        let params = {};\n\n        // 如果有分类筛选，添加分类参数\n        if (this.hasCategoryFilter) {\n          params = {\n            mainCategory: this.selectedMainCategory,\n            subCategory: this.selectedSubCategory\n          };\n        }\n        const response = await request.get('/good', params);\n        if (Array.isArray(response) && response.length > 0) {\n          // 使用后端数据，并处理图片URL和确保描述存在\n          this.recommendedGoods = response.slice(0, 8).map((item, index) => {\n            // 根据商品名称匹配对应的图片\n            let imgUrl;\n            switch (item.name) {\n              case '女上衣':\n                imgUrl = require('@/assets/女上衣.png');\n                break;\n              case '休闲鞋':\n                imgUrl = require('@/assets/休闲鞋.png');\n                break;\n              case '威士忌 大瓶':\n                imgUrl = require('@/assets/威士忌 大瓶.png');\n                break;\n              case '墨镜':\n                imgUrl = require('@/assets/墨镜.png');\n                break;\n              case '桌椅套装':\n                imgUrl = require('@/assets/桌椅套装.png');\n                break;\n              case '儿童简笔画册':\n                imgUrl = require('@/assets/儿童简笔画册.png');\n                break;\n              case '英文版图书':\n                imgUrl = require('@/assets/英文版图书.png');\n                break;\n              case '衬衫':\n                imgUrl = require('@/assets/衬衫.png');\n                break;\n              default:\n                {\n                  // 默认图片，根据索引循环使用\n                  const defaultImages = [require('@/assets/女上衣.png'), require('@/assets/休闲鞋.png'), require('@/assets/威士忌 大瓶.png'), require('@/assets/墨镜.png'), require('@/assets/桌椅套装.png')];\n                  imgUrl = defaultImages[index % defaultImages.length];\n                  break;\n                }\n            }\n            return {\n              ...item,\n              imgs: imgUrl,\n              description: item.description || this.getDefaultDescription(item.name),\n              // 使用默认描述，优先使用后端数据\n              price: item.price || (Math.random() * 200 + 50).toFixed(2) // 确保有价格\n            };\n          });\n          console.log('首页使用后端商品数据，添加图片:', this.recommendedGoods);\n        } else {\n          // 如果后端没有数据，使用本地图片的模拟数据\n          this.recommendedGoods = this.getMockProductsWithLocalImages();\n          console.log('首页使用本地图片的模拟商品数据:', this.recommendedGoods);\n        }\n      } catch (error) {\n        console.error('获取推荐商品失败:', error);\n        // 使用本地图片的模拟数据作为后备\n        this.recommendedGoods = this.getMockProductsWithLocalImages();\n        console.log('首页API请求失败，使用本地图片的模拟数据:', this.recommendedGoods);\n      } finally {\n        this.loading = false;\n      }\n    },\n    // 获取默认商品描述\n    getDefaultDescription(productName) {\n      const descriptions = {\n        '女上衣': '时尚女性上衣，舒适面料，多种颜色可选，适合日常穿着和商务场合，版型修身显瘦',\n        '休闲鞋': '舒适透气的休闲运动鞋，适合日常穿着，防滑耐磨，多种尺码可选，时尚百搭',\n        '威士忌 大瓶': '优质威士忌，口感醇厚，适合收藏和品鉴，酒精度40%，容量750ml，包装精美',\n        '墨镜': '时尚太阳镜，UV400防护，轻量化设计，多种款式可选',\n        '桌椅套装': '实木桌椅套装，环保材质，结实耐用，适合家庭使用',\n        '儿童简笔画册': '儿童启蒙绘画册，内容丰富，培养孩子创造力和想象力',\n        '英文版图书': '原版英文图书，提高英语阅读能力，内容精彩有趣',\n        '衬衫': '商务休闲衬衫，免烫面料，版型合身，适合各种场合'\n      };\n      return descriptions[productName] || '优质商品，品质保证，欢迎选购';\n    },\n    // 获取使用本地图片的模拟商品数据\n    getMockProductsWithLocalImages() {\n      return [{\n        id: 1,\n        name: '女上衣',\n        description: '时尚女性上衣，舒适面料，多种颜色可选',\n        imgs: require('@/assets/女上衣.png'),\n        price: 102.00,\n        discount: 0.85,\n        sales: 120,\n        recommend: true\n      }, {\n        id: 2,\n        name: '休闲鞋',\n        description: '舒适透气的休闲运动鞋，适合日常穿着',\n        imgs: require('@/assets/休闲鞋.png'),\n        price: 162.00,\n        discount: 0.90,\n        sales: 85,\n        recommend: true\n      }, {\n        id: 3,\n        name: '威士忌 大瓶',\n        description: '优质威士忌，口感醇厚，适合收藏和品鉴',\n        imgs: require('@/assets/威士忌 大瓶.png'),\n        price: 427.50,\n        discount: 0.95,\n        sales: 45,\n        recommend: true\n      }, {\n        id: 4,\n        name: '墨镜',\n        description: '时尚墨镜，防紫外线，多种款式可选',\n        imgs: require('@/assets/墨镜.png'),\n        price: 199.00,\n        discount: 0.80,\n        sales: 65,\n        recommend: true\n      }, {\n        id: 5,\n        name: '桌椅套装',\n        description: '舒适桌椅套装，适合家庭使用',\n        imgs: require('@/assets/桌椅套装.png'),\n        price: 1299.00,\n        discount: 0.85,\n        sales: 30,\n        recommend: true\n      }];\n    },\n    // 处理轮播图点击\n    handleCarouselClick(item) {\n      console.log('点击轮播图商品:', item);\n      this.$message.success(`查看商品: ${item.goodName}`);\n      // 可以跳转到商品详情页\n    },\n    // 处理搜索\n    handleSearch(keyword) {\n      console.log('搜索:', keyword);\n      // 跳转到商品页面并传递搜索参数\n      this.$router.push({\n        path: '/goods',\n        query: {\n          search: keyword\n        }\n      });\n    },\n    // 处理搜索结果\n    handleSearchResults(results) {\n      console.log('搜索结果:', results);\n    },\n    // 清除搜索\n    handleClearSearch() {\n      console.log('清除搜索');\n    },\n    // 添加到购物车\n    addToCart(good) {\n      console.log('加入购物车:', good.name);\n      this.$message.success(`已加入购物车：${good.name}`);\n    },\n    // 显示商品详情\n    showProductDetail(good) {\n      this.selectedProduct = good;\n      this.showDetailDialog = true;\n      console.log('查看商品详情:', good);\n    },\n    // 立即购买\n    buyNow(good) {\n      console.log('立即购买:', good.name);\n      this.$message.info(`立即购买：${good.name}`);\n    },\n    // 跳转到商品页面\n    goToGoodsPage() {\n      // 如果有分类筛选，传递分类参数到商品页面\n      if (this.hasCategoryFilter) {\n        this.$router.push({\n          path: '/goods',\n          query: {\n            mainCategory: this.selectedMainCategory,\n            subCategory: this.selectedSubCategory\n          }\n        });\n      } else {\n        this.$router.push('/goods');\n      }\n    },\n    // 清除分类筛选\n    clearCategoryFilter() {\n      // 触发Category组件的clearCategoryFilter方法\n      const categoryComponent = this.$refs.categoryComponent;\n      if (categoryComponent && typeof categoryComponent.clearCategoryFilter === 'function') {\n        categoryComponent.clearCategoryFilter();\n      }\n    }\n  },\n  created() {\n    this.fetchRecommendedGoods();\n  },\n  mounted() {\n    this.fetchRecommendedGoods();\n  }\n};", "map": {"version": 3, "names": ["CarouselComponent", "SearchBox", "ProductDetail", "Star", "request", "name", "components", "data", "loading", "recommendedGoods", "selected<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectedSubCategory", "hasCate<PERSON><PERSON><PERSON><PERSON><PERSON>", "showDetailDialog", "selectedProduct", "methods", "handleCategoryChange", "category", "mainCategory", "subCategory", "fetchRecommendedGoods", "handleImageError", "event", "console", "error", "target", "src", "style", "backgroundColor", "border", "handleImageLoad", "log", "params", "response", "get", "Array", "isArray", "length", "slice", "map", "item", "index", "imgUrl", "require", "defaultImages", "imgs", "description", "getDefaultDescription", "price", "Math", "random", "toFixed", "getMockProductsWithLocalImages", "productName", "descriptions", "id", "discount", "sales", "recommend", "handleCarouselClick", "$message", "success", "<PERSON><PERSON><PERSON>", "handleSearch", "keyword", "$router", "push", "path", "query", "search", "handleSearchResults", "results", "handleClearSearch", "addToCart", "good", "showProductDetail", "buyNow", "info", "goToGoodsPage", "clearCategory<PERSON><PERSON>er", "categoryComponent", "$refs", "created", "mounted"], "sources": ["D:\\2025_down\\project\\shoppingOnline_2025_91\\shoppingOnline_front-2025-91\\src\\views\\Home.vue"], "sourcesContent": ["<template>\r\n  <div class=\"home-container\">\r\n    <!-- 欢迎横幅 -->\r\n    <div class=\"welcome-banner\">\r\n      <h1 class=\"welcome-title\">欢迎来到在线购物商城</h1>\r\n      <p class=\"welcome-subtitle\">发现优质商品，享受购物乐趣</p>\r\n    </div>\r\n\r\n    <!-- 轮播图组件 -->\r\n    <CarouselComponent @carousel-click=\"handleCarouselClick\" />\r\n\r\n    <!-- 搜索框组件 -->\r\n    <div class=\"search-section\">\r\n      <SearchBox\r\n        @search=\"handleSearch\"\r\n        @search-results=\"handleSearchResults\"\r\n        @clear-search=\"handleClearSearch\"\r\n      />\r\n    </div>\r\n\r\n    <!-- 推荐商品区域 -->\r\n    <div class=\"recommended-section\">\r\n      <h2 class=\"section-title\">\r\n        <el-icon><Star /></el-icon>\r\n        推荐商品\r\n      </h2>\r\n\r\n      <!-- 加载状态 -->\r\n      <div v-if=\"loading\" class=\"loading\">\r\n        <el-skeleton :rows=\"2\" animated />\r\n      </div>\r\n\r\n      <!-- 商品列表 -->\r\n      <div v-else class=\"goods-grid\">\r\n        <div\r\n          class=\"goods-card\"\r\n          v-for=\"good in recommendedGoods\"\r\n          :key=\"good.id\"\r\n          @click=\"showProductDetail(good)\"\r\n        >\r\n          <img\r\n            :src=\"good.imgs\"\r\n            :alt=\"good.name\"\r\n            class=\"goods-image\"\r\n            @error=\"handleImageError\"\r\n            @load=\"handleImageLoad\"\r\n          />\r\n          <div class=\"goods-info\">\r\n            <h3 class=\"goods-name\">{{ good.name }}</h3>\r\n            <p class=\"goods-desc\">{{ good.description || '暂无商品描述' }}</p>\r\n            <p class=\"goods-price\">¥{{ good.price }}</p>\r\n            <el-button type=\"primary\" size=\"small\" @click.stop=\"addToCart(good)\">\r\n              加入购物车\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 查看更多按钮 -->\r\n      <div class=\"more-goods\">\r\n        <el-button type=\"primary\" size=\"large\" @click=\"goToGoodsPage\">\r\n          查看更多商品\r\n        </el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 商品详情弹窗 -->\r\n    <ProductDetail\r\n      v-model=\"showDetailDialog\"\r\n      :product=\"selectedProduct\"\r\n      @add-to-cart=\"addToCart\"\r\n      @buy-now=\"buyNow\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CarouselComponent from '@/components/Carousel.vue'\r\nimport SearchBox from '@/components/SearchBox.vue'\r\nimport ProductDetail from '@/components/ProductDetail.vue'\r\n\r\nimport { Star } from '@element-plus/icons-vue'\r\nimport request from '@/utils/request.js'\r\n\r\nexport default {\r\n  name: 'HomeView',\r\n\r\n  components: {\r\n    CarouselComponent,\r\n    SearchBox,\r\n    ProductDetail,\r\n    Star\r\n  },\r\n\r\n  data() {\r\n    return {\r\n      loading: true,\r\n      recommendedGoods: [],\r\n      selectedMainCategory: null, // 选中的主分类\r\n      selectedSubCategory: null, // 选中的子分类\r\n      hasCategoryFilter: false, // 是否有分类筛选\r\n      showDetailDialog: false, // 是否显示商品详情弹窗\r\n      selectedProduct: {} // 选中的商品\r\n    };\r\n  },\r\n\r\n  methods: {\r\n    // 处理分类变化\r\n    handleCategoryChange(category) {\r\n      this.selectedMainCategory = category.mainCategory\r\n      this.selectedSubCategory = category.subCategory\r\n      this.hasCategoryFilter = category.mainCategory !== null || category.subCategory !== null\r\n      this.fetchRecommendedGoods()\r\n    },\r\n\r\n    // 图片加载错误处理\r\n    handleImageError(event) {\r\n      console.error('首页商品图片加载失败:', event.target.src);\r\n      event.target.style.backgroundColor = '#f0f0f0';\r\n      event.target.style.border = '1px solid #ccc';\r\n    },\r\n\r\n    // 图片加载成功处理\r\n    handleImageLoad(event) {\r\n      console.log('首页商品图片加载成功:', event.target.src);\r\n    },\r\n\r\n    // 获取推荐商品\r\n    async fetchRecommendedGoods() {\r\n      try {\r\n        this.loading = true;\r\n        let params = {};\r\n        \r\n        // 如果有分类筛选，添加分类参数\r\n        if (this.hasCategoryFilter) {\r\n          params = {\r\n            mainCategory: this.selectedMainCategory,\r\n            subCategory: this.selectedSubCategory\r\n          };\r\n        }\r\n        \r\n        const response = await request.get('/good', params);\r\n\r\n        if (Array.isArray(response) && response.length > 0) {\r\n          // 使用后端数据，并处理图片URL和确保描述存在\r\n          this.recommendedGoods = response.slice(0, 8).map((item, index) => {\r\n            // 根据商品名称匹配对应的图片\r\n            let imgUrl;\r\n            switch(item.name) {\r\n              case '女上衣':\r\n                imgUrl = require('@/assets/女上衣.png');\r\n                break;\r\n              case '休闲鞋':\r\n                imgUrl = require('@/assets/休闲鞋.png');\r\n                break;\r\n              case '威士忌 大瓶':\r\n                imgUrl = require('@/assets/威士忌 大瓶.png');\r\n                break;\r\n              case '墨镜':\r\n                imgUrl = require('@/assets/墨镜.png');\r\n                break;\r\n              case '桌椅套装':\r\n                imgUrl = require('@/assets/桌椅套装.png');\r\n                break;\r\n              case '儿童简笔画册':\r\n                imgUrl = require('@/assets/儿童简笔画册.png');\r\n                break;\r\n              case '英文版图书':\r\n                imgUrl = require('@/assets/英文版图书.png');\r\n                break;\r\n              case '衬衫':\r\n                imgUrl = require('@/assets/衬衫.png');\r\n                break;\r\n              default: {\r\n                // 默认图片，根据索引循环使用\r\n                const defaultImages = [\r\n                  require('@/assets/女上衣.png'),\r\n                  require('@/assets/休闲鞋.png'),\r\n                  require('@/assets/威士忌 大瓶.png'),\r\n                  require('@/assets/墨镜.png'),\r\n                  require('@/assets/桌椅套装.png')\r\n                ];\r\n                imgUrl = defaultImages[index % defaultImages.length];\r\n                break;\r\n              }\r\n            }\r\n            return {\r\n              ...item,\r\n              imgs: imgUrl,\r\n              description: item.description || this.getDefaultDescription(item.name), // 使用默认描述，优先使用后端数据\r\n              price: item.price || (Math.random() * 200 + 50).toFixed(2) // 确保有价格\r\n            };\r\n          });\r\n          console.log('首页使用后端商品数据，添加图片:', this.recommendedGoods);\r\n        } else {\r\n          // 如果后端没有数据，使用本地图片的模拟数据\r\n          this.recommendedGoods = this.getMockProductsWithLocalImages();\r\n          console.log('首页使用本地图片的模拟商品数据:', this.recommendedGoods);\r\n        }\r\n      } catch (error) {\r\n        console.error('获取推荐商品失败:', error);\r\n        // 使用本地图片的模拟数据作为后备\r\n        this.recommendedGoods = this.getMockProductsWithLocalImages();\r\n        console.log('首页API请求失败，使用本地图片的模拟数据:', this.recommendedGoods);\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n\r\n    // 获取默认商品描述\r\n    getDefaultDescription(productName) {\r\n      const descriptions = {\r\n        '女上衣': '时尚女性上衣，舒适面料，多种颜色可选，适合日常穿着和商务场合，版型修身显瘦',\r\n        '休闲鞋': '舒适透气的休闲运动鞋，适合日常穿着，防滑耐磨，多种尺码可选，时尚百搭',\r\n        '威士忌 大瓶': '优质威士忌，口感醇厚，适合收藏和品鉴，酒精度40%，容量750ml，包装精美',\r\n        '墨镜': '时尚太阳镜，UV400防护，轻量化设计，多种款式可选',\r\n        '桌椅套装': '实木桌椅套装，环保材质，结实耐用，适合家庭使用',\r\n        '儿童简笔画册': '儿童启蒙绘画册，内容丰富，培养孩子创造力和想象力',\r\n        '英文版图书': '原版英文图书，提高英语阅读能力，内容精彩有趣',\r\n        '衬衫': '商务休闲衬衫，免烫面料，版型合身，适合各种场合'\r\n      }\r\n      return descriptions[productName] || '优质商品，品质保证，欢迎选购'\r\n    },\r\n\r\n    // 获取使用本地图片的模拟商品数据\r\n    getMockProductsWithLocalImages() {\r\n      return [\r\n        {\r\n          id: 1,\r\n          name: '女上衣',\r\n          description: '时尚女性上衣，舒适面料，多种颜色可选',\r\n          imgs: require('@/assets/女上衣.png'),\r\n          price: 102.00,\r\n          discount: 0.85,\r\n          sales: 120,\r\n          recommend: true\r\n        },\r\n        {\r\n          id: 2,\r\n          name: '休闲鞋',\r\n          description: '舒适透气的休闲运动鞋，适合日常穿着',\r\n          imgs: require('@/assets/休闲鞋.png'),\r\n          price: 162.00,\r\n          discount: 0.90,\r\n          sales: 85,\r\n          recommend: true\r\n        },\r\n        {\r\n          id: 3,\r\n          name: '威士忌 大瓶',\r\n          description: '优质威士忌，口感醇厚，适合收藏和品鉴',\r\n          imgs: require('@/assets/威士忌 大瓶.png'),\r\n          price: 427.50,\r\n          discount: 0.95,\r\n          sales: 45,\r\n          recommend: true\r\n        },\r\n        {\r\n          id: 4,\r\n          name: '墨镜',\r\n          description: '时尚墨镜，防紫外线，多种款式可选',\r\n          imgs: require('@/assets/墨镜.png'),\r\n          price: 199.00,\r\n          discount: 0.80,\r\n          sales: 65,\r\n          recommend: true\r\n        },\r\n        {\r\n          id: 5,\r\n          name: '桌椅套装',\r\n          description: '舒适桌椅套装，适合家庭使用',\r\n          imgs: require('@/assets/桌椅套装.png'),\r\n          price: 1299.00,\r\n          discount: 0.85,\r\n          sales: 30,\r\n          recommend: true\r\n        }\r\n      ];\r\n    },\r\n\r\n    // 处理轮播图点击\r\n    handleCarouselClick(item) {\r\n      console.log('点击轮播图商品:', item);\r\n      this.$message.success(`查看商品: ${item.goodName}`);\r\n      // 可以跳转到商品详情页\r\n    },\r\n\r\n    // 处理搜索\r\n    handleSearch(keyword) {\r\n      console.log('搜索:', keyword);\r\n      // 跳转到商品页面并传递搜索参数\r\n      this.$router.push({\r\n        path: '/goods',\r\n        query: { search: keyword }\r\n      });\r\n    },\r\n\r\n    // 处理搜索结果\r\n    handleSearchResults(results) {\r\n      console.log('搜索结果:', results);\r\n    },\r\n\r\n    // 清除搜索\r\n    handleClearSearch() {\r\n      console.log('清除搜索');\r\n    },\r\n\r\n    // 添加到购物车\r\n    addToCart(good) {\r\n      console.log('加入购物车:', good.name);\r\n      this.$message.success(`已加入购物车：${good.name}`);\r\n    },\r\n\r\n    // 显示商品详情\r\n    showProductDetail(good) {\r\n      this.selectedProduct = good;\r\n      this.showDetailDialog = true;\r\n      console.log('查看商品详情:', good);\r\n    },\r\n\r\n    // 立即购买\r\n    buyNow(good) {\r\n      console.log('立即购买:', good.name);\r\n      this.$message.info(`立即购买：${good.name}`);\r\n    },\r\n\r\n    // 跳转到商品页面\r\n    goToGoodsPage() {\r\n      // 如果有分类筛选，传递分类参数到商品页面\r\n      if (this.hasCategoryFilter) {\r\n        this.$router.push({\r\n          path: '/goods',\r\n          query: {\r\n            mainCategory: this.selectedMainCategory,\r\n            subCategory: this.selectedSubCategory\r\n          }\r\n        });\r\n      } else {\r\n        this.$router.push('/goods');\r\n      }\r\n    },\r\n    \r\n    // 清除分类筛选\r\n    clearCategoryFilter() {\r\n      // 触发Category组件的clearCategoryFilter方法\r\n      const categoryComponent = this.$refs.categoryComponent;\r\n      if (categoryComponent && typeof categoryComponent.clearCategoryFilter === 'function') {\r\n        categoryComponent.clearCategoryFilter();\r\n      }\r\n    }\r\n  },\r\n\r\n  created() {\r\n    this.fetchRecommendedGoods();\r\n  },\r\n\r\n  mounted() {\r\n    this.fetchRecommendedGoods();\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.home-container {\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  padding: 20px 16px;\r\n}\r\n\r\n.welcome-banner {\r\n  text-align: center;\r\n  margin-bottom: 40px;\r\n  padding: 40px 20px;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border-radius: 16px;\r\n  color: white;\r\n}\r\n\r\n.welcome-title {\r\n  font-size: 36px;\r\n  margin: 0 0 10px;\r\n  font-weight: bold;\r\n}\r\n\r\n.welcome-subtitle {\r\n  font-size: 18px;\r\n  margin: 0;\r\n  opacity: 0.9;\r\n}\r\n\r\n.search-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.search-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 15px;\r\n  margin-bottom: 30px;\r\n  padding: 15px;\r\n  background: #f8f9fa;\r\n  border-radius: 8px;\r\n  border-left: 4px solid #67c23a;\r\n}\r\n\r\n.search-count {\r\n  color: #666;\r\n  font-size: 14px;\r\n}\r\n\r\n.recommended-section {\r\n  margin-top: 50px;\r\n}\r\n\r\n.section-title {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  font-size: 24px;\r\n  color: #333;\r\n  margin-bottom: 30px;\r\n  padding-bottom: 10px;\r\n  border-bottom: 2px solid #409eff;\r\n}\r\n\r\n.loading {\r\n  padding: 40px 0;\r\n  /* 添加最小高度，避免布局跳动 */\r\n  min-height: 300px;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n.goods-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\r\n  gap: 20px;\r\n  margin-bottom: 40px;\r\n  /* 添加最小高度，避免布局跳动 */\r\n  min-height: 300px;\r\n  /* 添加过渡效果 */\r\n  transition: all 0.3s ease-in-out;\r\n}\r\n\r\n.goods-card {\r\n  border: 1px solid #e0e0e0;\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  background: white;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  transition: all 0.3s ease;\r\n  cursor: pointer;\r\n  /* 添加动画效果 */\r\n  animation: fadeIn 0.6s ease-out;\r\n  /* 添加will-change优化性能 */\r\n  will-change: transform, opacity;\r\n}\r\n\r\n@keyframes fadeIn {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(20px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n.goods-card:hover {\r\n  transform: translateY(-5px);\r\n  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);\r\n  border-color: #409eff;\r\n}\r\n\r\n.goods-image {\r\n  width: 100%;\r\n  height: 160px;\r\n  object-fit: cover;\r\n}\r\n\r\n.goods-info {\r\n  padding: 16px;\r\n}\r\n\r\n.goods-name {\r\n  font-size: 16px;\r\n  color: #333;\r\n  margin: 0 0 8px;\r\n  font-weight: bold;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n  overflow: hidden;\r\n}\r\n\r\n.goods-price {\r\n  color: #e60000;\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n  margin: 10px 0 15px;\r\n}\r\n\r\n.more-goods {\r\n  text-align: center;\r\n  margin-top: 40px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .home-container {\r\n    padding: 15px 12px;\r\n  }\r\n\r\n  .welcome-banner {\r\n    padding: 30px 15px;\r\n    margin-bottom: 30px;\r\n  }\r\n\r\n  .welcome-title {\r\n    font-size: 28px;\r\n  }\r\n\r\n  .welcome-subtitle {\r\n    font-size: 16px;\r\n  }\r\n\r\n  .goods-grid {\r\n    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\r\n    gap: 15px;\r\n  }\r\n\r\n  .section-title {\r\n    font-size: 20px;\r\n  }\r\n}\r\n</style>"], "mappings": ";;;AA6EA,OAAOA,iBAAgB,MAAO,2BAA0B;AACxD,OAAOC,SAAQ,MAAO,4BAA2B;AACjD,OAAOC,aAAY,MAAO,gCAA+B;AAEzD,SAASC,IAAG,QAAS,yBAAwB;AAC7C,OAAOC,OAAM,MAAO,oBAAmB;AAEvC,eAAe;EACbC,IAAI,EAAE,UAAU;EAEhBC,UAAU,EAAE;IACVN,iBAAiB;IACjBC,SAAS;IACTC,aAAa;IACbC;EACF,CAAC;EAEDI,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,OAAO,EAAE,IAAI;MACbC,gBAAgB,EAAE,EAAE;MACpBC,oBAAoB,EAAE,IAAI;MAAE;MAC5BC,mBAAmB,EAAE,IAAI;MAAE;MAC3BC,iBAAiB,EAAE,KAAK;MAAE;MAC1BC,gBAAgB,EAAE,KAAK;MAAE;MACzBC,eAAe,EAAE,CAAC,EAAE;IACtB,CAAC;EACH,CAAC;EAEDC,OAAO,EAAE;IACP;IACAC,oBAAoBA,CAACC,QAAQ,EAAE;MAC7B,IAAI,CAACP,oBAAmB,GAAIO,QAAQ,CAACC,YAAW;MAChD,IAAI,CAACP,mBAAkB,GAAIM,QAAQ,CAACE,WAAU;MAC9C,IAAI,CAACP,iBAAgB,GAAIK,QAAQ,CAACC,YAAW,KAAM,IAAG,IAAKD,QAAQ,CAACE,WAAU,KAAM,IAAG;MACvF,IAAI,CAACC,qBAAqB,CAAC;IAC7B,CAAC;IAED;IACAC,gBAAgBA,CAACC,KAAK,EAAE;MACtBC,OAAO,CAACC,KAAK,CAAC,aAAa,EAAEF,KAAK,CAACG,MAAM,CAACC,GAAG,CAAC;MAC9CJ,KAAK,CAACG,MAAM,CAACE,KAAK,CAACC,eAAc,GAAI,SAAS;MAC9CN,KAAK,CAACG,MAAM,CAACE,KAAK,CAACE,MAAK,GAAI,gBAAgB;IAC9C,CAAC;IAED;IACAC,eAAeA,CAACR,KAAK,EAAE;MACrBC,OAAO,CAACQ,GAAG,CAAC,aAAa,EAAET,KAAK,CAACG,MAAM,CAACC,GAAG,CAAC;IAC9C,CAAC;IAED;IACA,MAAMN,qBAAqBA,CAAA,EAAG;MAC5B,IAAI;QACF,IAAI,CAACZ,OAAM,GAAI,IAAI;QACnB,IAAIwB,MAAK,GAAI,CAAC,CAAC;;QAEf;QACA,IAAI,IAAI,CAACpB,iBAAiB,EAAE;UAC1BoB,MAAK,GAAI;YACPd,YAAY,EAAE,IAAI,CAACR,oBAAoB;YACvCS,WAAW,EAAE,IAAI,CAACR;UACpB,CAAC;QACH;QAEA,MAAMsB,QAAO,GAAI,MAAM7B,OAAO,CAAC8B,GAAG,CAAC,OAAO,EAAEF,MAAM,CAAC;QAEnD,IAAIG,KAAK,CAACC,OAAO,CAACH,QAAQ,KAAKA,QAAQ,CAACI,MAAK,GAAI,CAAC,EAAE;UAClD;UACA,IAAI,CAAC5B,gBAAe,GAAIwB,QAAQ,CAACK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;YAChE;YACA,IAAIC,MAAM;YACV,QAAOF,IAAI,CAACnC,IAAI;cACd,KAAK,KAAK;gBACRqC,MAAK,GAAIC,OAAO,CAAC,kBAAkB,CAAC;gBACpC;cACF,KAAK,KAAK;gBACRD,MAAK,GAAIC,OAAO,CAAC,kBAAkB,CAAC;gBACpC;cACF,KAAK,QAAQ;gBACXD,MAAK,GAAIC,OAAO,CAAC,qBAAqB,CAAC;gBACvC;cACF,KAAK,IAAI;gBACPD,MAAK,GAAIC,OAAO,CAAC,iBAAiB,CAAC;gBACnC;cACF,KAAK,MAAM;gBACTD,MAAK,GAAIC,OAAO,CAAC,mBAAmB,CAAC;gBACrC;cACF,KAAK,QAAQ;gBACXD,MAAK,GAAIC,OAAO,CAAC,qBAAqB,CAAC;gBACvC;cACF,KAAK,OAAO;gBACVD,MAAK,GAAIC,OAAO,CAAC,oBAAoB,CAAC;gBACtC;cACF,KAAK,IAAI;gBACPD,MAAK,GAAIC,OAAO,CAAC,iBAAiB,CAAC;gBACnC;cACF;gBAAS;kBACP;kBACA,MAAMC,aAAY,GAAI,CACpBD,OAAO,CAAC,kBAAkB,CAAC,EAC3BA,OAAO,CAAC,kBAAkB,CAAC,EAC3BA,OAAO,CAAC,qBAAqB,CAAC,EAC9BA,OAAO,CAAC,iBAAiB,CAAC,EAC1BA,OAAO,CAAC,mBAAmB,EAC5B;kBACDD,MAAK,GAAIE,aAAa,CAACH,KAAI,GAAIG,aAAa,CAACP,MAAM,CAAC;kBACpD;gBACF;YACF;YACA,OAAO;cACL,GAAGG,IAAI;cACPK,IAAI,EAAEH,MAAM;cACZI,WAAW,EAAEN,IAAI,CAACM,WAAU,IAAK,IAAI,CAACC,qBAAqB,CAACP,IAAI,CAACnC,IAAI,CAAC;cAAE;cACxE2C,KAAK,EAAER,IAAI,CAACQ,KAAI,IAAK,CAACC,IAAI,CAACC,MAAM,CAAC,IAAI,GAAE,GAAI,EAAE,EAAEC,OAAO,CAAC,CAAC,EAAE;YAC7D,CAAC;UACH,CAAC,CAAC;UACF5B,OAAO,CAACQ,GAAG,CAAC,kBAAkB,EAAE,IAAI,CAACtB,gBAAgB,CAAC;QACxD,OAAO;UACL;UACA,IAAI,CAACA,gBAAe,GAAI,IAAI,CAAC2C,8BAA8B,CAAC,CAAC;UAC7D7B,OAAO,CAACQ,GAAG,CAAC,kBAAkB,EAAE,IAAI,CAACtB,gBAAgB,CAAC;QACxD;MACF,EAAE,OAAOe,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjC;QACA,IAAI,CAACf,gBAAe,GAAI,IAAI,CAAC2C,8BAA8B,CAAC,CAAC;QAC7D7B,OAAO,CAACQ,GAAG,CAAC,wBAAwB,EAAE,IAAI,CAACtB,gBAAgB,CAAC;MAC9D,UAAU;QACR,IAAI,CAACD,OAAM,GAAI,KAAK;MACtB;IACF,CAAC;IAED;IACAuC,qBAAqBA,CAACM,WAAW,EAAE;MACjC,MAAMC,YAAW,GAAI;QACnB,KAAK,EAAE,uCAAuC;QAC9C,KAAK,EAAE,oCAAoC;QAC3C,QAAQ,EAAE,wCAAwC;QAClD,IAAI,EAAE,4BAA4B;QAClC,MAAM,EAAE,yBAAyB;QACjC,QAAQ,EAAE,0BAA0B;QACpC,OAAO,EAAE,wBAAwB;QACjC,IAAI,EAAE;MACR;MACA,OAAOA,YAAY,CAACD,WAAW,KAAK,gBAAe;IACrD,CAAC;IAED;IACAD,8BAA8BA,CAAA,EAAG;MAC/B,OAAO,CACL;QACEG,EAAE,EAAE,CAAC;QACLlD,IAAI,EAAE,KAAK;QACXyC,WAAW,EAAE,oBAAoB;QACjCD,IAAI,EAAEF,OAAO,CAAC,kBAAkB,CAAC;QACjCK,KAAK,EAAE,MAAM;QACbQ,QAAQ,EAAE,IAAI;QACdC,KAAK,EAAE,GAAG;QACVC,SAAS,EAAE;MACb,CAAC,EACD;QACEH,EAAE,EAAE,CAAC;QACLlD,IAAI,EAAE,KAAK;QACXyC,WAAW,EAAE,mBAAmB;QAChCD,IAAI,EAAEF,OAAO,CAAC,kBAAkB,CAAC;QACjCK,KAAK,EAAE,MAAM;QACbQ,QAAQ,EAAE,IAAI;QACdC,KAAK,EAAE,EAAE;QACTC,SAAS,EAAE;MACb,CAAC,EACD;QACEH,EAAE,EAAE,CAAC;QACLlD,IAAI,EAAE,QAAQ;QACdyC,WAAW,EAAE,oBAAoB;QACjCD,IAAI,EAAEF,OAAO,CAAC,qBAAqB,CAAC;QACpCK,KAAK,EAAE,MAAM;QACbQ,QAAQ,EAAE,IAAI;QACdC,KAAK,EAAE,EAAE;QACTC,SAAS,EAAE;MACb,CAAC,EACD;QACEH,EAAE,EAAE,CAAC;QACLlD,IAAI,EAAE,IAAI;QACVyC,WAAW,EAAE,kBAAkB;QAC/BD,IAAI,EAAEF,OAAO,CAAC,iBAAiB,CAAC;QAChCK,KAAK,EAAE,MAAM;QACbQ,QAAQ,EAAE,IAAI;QACdC,KAAK,EAAE,EAAE;QACTC,SAAS,EAAE;MACb,CAAC,EACD;QACEH,EAAE,EAAE,CAAC;QACLlD,IAAI,EAAE,MAAM;QACZyC,WAAW,EAAE,eAAe;QAC5BD,IAAI,EAAEF,OAAO,CAAC,mBAAmB,CAAC;QAClCK,KAAK,EAAE,OAAO;QACdQ,QAAQ,EAAE,IAAI;QACdC,KAAK,EAAE,EAAE;QACTC,SAAS,EAAE;MACb,EACD;IACH,CAAC;IAED;IACAC,mBAAmBA,CAACnB,IAAI,EAAE;MACxBjB,OAAO,CAACQ,GAAG,CAAC,UAAU,EAAES,IAAI,CAAC;MAC7B,IAAI,CAACoB,QAAQ,CAACC,OAAO,CAAC,SAASrB,IAAI,CAACsB,QAAQ,EAAE,CAAC;MAC/C;IACF,CAAC;IAED;IACAC,YAAYA,CAACC,OAAO,EAAE;MACpBzC,OAAO,CAACQ,GAAG,CAAC,KAAK,EAAEiC,OAAO,CAAC;MAC3B;MACA,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC;QAChBC,IAAI,EAAE,QAAQ;QACdC,KAAK,EAAE;UAAEC,MAAM,EAAEL;QAAQ;MAC3B,CAAC,CAAC;IACJ,CAAC;IAED;IACAM,mBAAmBA,CAACC,OAAO,EAAE;MAC3BhD,OAAO,CAACQ,GAAG,CAAC,OAAO,EAAEwC,OAAO,CAAC;IAC/B,CAAC;IAED;IACAC,iBAAiBA,CAAA,EAAG;MAClBjD,OAAO,CAACQ,GAAG,CAAC,MAAM,CAAC;IACrB,CAAC;IAED;IACA0C,SAASA,CAACC,IAAI,EAAE;MACdnD,OAAO,CAACQ,GAAG,CAAC,QAAQ,EAAE2C,IAAI,CAACrE,IAAI,CAAC;MAChC,IAAI,CAACuD,QAAQ,CAACC,OAAO,CAAC,UAAUa,IAAI,CAACrE,IAAI,EAAE,CAAC;IAC9C,CAAC;IAED;IACAsE,iBAAiBA,CAACD,IAAI,EAAE;MACtB,IAAI,CAAC5D,eAAc,GAAI4D,IAAI;MAC3B,IAAI,CAAC7D,gBAAe,GAAI,IAAI;MAC5BU,OAAO,CAACQ,GAAG,CAAC,SAAS,EAAE2C,IAAI,CAAC;IAC9B,CAAC;IAED;IACAE,MAAMA,CAACF,IAAI,EAAE;MACXnD,OAAO,CAACQ,GAAG,CAAC,OAAO,EAAE2C,IAAI,CAACrE,IAAI,CAAC;MAC/B,IAAI,CAACuD,QAAQ,CAACiB,IAAI,CAAC,QAAQH,IAAI,CAACrE,IAAI,EAAE,CAAC;IACzC,CAAC;IAED;IACAyE,aAAaA,CAAA,EAAG;MACd;MACA,IAAI,IAAI,CAAClE,iBAAiB,EAAE;QAC1B,IAAI,CAACqD,OAAO,CAACC,IAAI,CAAC;UAChBC,IAAI,EAAE,QAAQ;UACdC,KAAK,EAAE;YACLlD,YAAY,EAAE,IAAI,CAACR,oBAAoB;YACvCS,WAAW,EAAE,IAAI,CAACR;UACpB;QACF,CAAC,CAAC;MACJ,OAAO;QACL,IAAI,CAACsD,OAAO,CAACC,IAAI,CAAC,QAAQ,CAAC;MAC7B;IACF,CAAC;IAED;IACAa,mBAAmBA,CAAA,EAAG;MACpB;MACA,MAAMC,iBAAgB,GAAI,IAAI,CAACC,KAAK,CAACD,iBAAiB;MACtD,IAAIA,iBAAgB,IAAK,OAAOA,iBAAiB,CAACD,mBAAkB,KAAM,UAAU,EAAE;QACpFC,iBAAiB,CAACD,mBAAmB,CAAC,CAAC;MACzC;IACF;EACF,CAAC;EAEDG,OAAOA,CAAA,EAAG;IACR,IAAI,CAAC9D,qBAAqB,CAAC,CAAC;EAC9B,CAAC;EAED+D,OAAOA,CAAA,EAAG;IACR,IAAI,CAAC/D,qBAAqB,CAAC,CAAC;EAC9B;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}