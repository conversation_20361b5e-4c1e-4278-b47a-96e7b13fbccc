{"ast": null, "code": "import { createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, normalizeClass as _normalizeClass, withCtx as _withCtx, createVNode as _createVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, createBlock as _createBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"product-category\"\n};\nconst _hoisted_2 = {\n  class: \"category-list\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_card = _resolveComponent(\"el-card\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_card, {\n    class: \"category-card\"\n  }, {\n    default: _withCtx(() => [_cache[2] || (_cache[2] = _createElementVNode(\"div\", {\n      class: \"category-title\"\n    }, \"商品分类\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_button, {\n      class: _normalizeClass(['category-btn', {\n        'active': $data.selectedCategory === null\n      }]),\n      onClick: _cache[0] || (_cache[0] = $event => $options.handleCategoryClick(null))\n    }, {\n      default: _withCtx(() => [...(_cache[1] || (_cache[1] = [_createTextVNode(\" 全部分类 \", -1 /* CACHED */)]))]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"class\"]), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.categories, category => {\n      return _openBlock(), _createBlock(_component_el_button, {\n        key: category.id,\n        class: _normalizeClass(['category-btn', {\n          'active': $data.selectedCategory === category.id\n        }]),\n        onClick: $event => $options.handleCategoryClick(category)\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString(category.name), 1 /* TEXT */)]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"class\", \"onClick\"]);\n    }), 128 /* KEYED_FRAGMENT */))])]),\n    _: 1 /* STABLE */\n  })]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_card", "_createElementVNode", "_hoisted_2", "_component_el_button", "_normalizeClass", "$data", "selectedCate<PERSON><PERSON>", "onClick", "_cache", "$event", "$options", "handleCategoryClick", "_Fragment", "_renderList", "categories", "category", "_createBlock", "key", "id", "name"], "sources": ["D:\\2025_down\\project\\shoppingOnline_2025_91\\shoppingOnline_front-2025-91\\src\\components\\ProductCategory.vue"], "sourcesContent": ["<template>\n  <div class=\"product-category\">\n    <el-card class=\"category-card\">\n      <div class=\"category-title\">商品分类</div>\n      <div class=\"category-list\">\n        <el-button\n          :class=\"['category-btn', { 'active': selectedCategory === null }]\"\n          @click=\"handleCategoryClick(null)\"\n        >\n          全部分类\n        </el-button>\n        <el-button\n          v-for=\"category in categories\"\n          :key=\"category.id\"\n          :class=\"['category-btn', { 'active': selectedCategory === category.id }]\"\n          @click=\"handleCategoryClick(category)\"\n        >\n          {{ category.name }}\n        </el-button>\n      </div>\n    </el-card>\n  </div>\n</template>\n\n<script>\nimport request from '@/utils/request.js'\n\nexport default {\n  name: 'ProductCategory',\n  data() {\n    return {\n      selectedCategory: null,\n      categories: [],\n      loading: false\n    };\n  },\n\n  created() {\n    this.fetchCategories();\n  },\n\n  methods: {\n    // 获取分类数据\n    async fetchCategories() {\n      try {\n        this.loading = true;\n        const response = await request.get('/categoryAPI');\n\n        if (Array.isArray(response)) {\n          this.categories = response;\n          console.log('获取分类数据成功:', this.categories);\n        } else {\n          // 如果后端没有数据，使用默认分类\n          this.categories = this.getDefaultCategories();\n          console.log('使用默认分类数据');\n        }\n      } catch (error) {\n        console.error('获取分类数据失败:', error);\n        // 使用默认分类作为后备\n        this.categories = this.getDefaultCategories();\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    // 获取默认分类数据\n    getDefaultCategories() {\n      return [\n        { id: 1, name: '服装' },\n        { id: 2, name: '鞋类' },\n        { id: 3, name: '酒类' },\n        { id: 4, name: '日用百货' },\n        { id: 5, name: '家用电器' },\n        { id: 6, name: '数码产品' }\n      ];\n    },\n\n    handleCategoryClick(category) {\n      if (category === null) {\n        this.selectedCategory = null;\n        this.$emit('category-change', { categoryId: null, categoryName: '全部分类' });\n      } else {\n        this.selectedCategory = category.id;\n        this.$emit('category-change', { categoryId: category.id, categoryName: category.name });\n      }\n    },\n\n    clearCategory() {\n      this.selectedCategory = null;\n      this.$emit('category-change', { categoryId: null, categoryName: '全部分类' });\n    }\n  }\n};\n</script>\n\n<style scoped>\n.product-category {\n  margin-bottom: 20px;\n}\n\n.category-card {\n  border-radius: 8px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);\n}\n\n.category-title {\n  font-size: 16px;\n  font-weight: 500;\n  margin-bottom: 15px;\n  color: #303133;\n  padding-bottom: 10px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.category-list {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 10px;\n}\n\n.category-btn {\n  padding: 8px 16px;\n  border-radius: 20px;\n  background: #f5f5f5;\n  color: #606266;\n  border: none;\n  transition: all 0.3s;\n}\n\n.category-btn:hover {\n  background: #e9e9e9;\n  color: #409eff;\n}\n\n.category-btn.active {\n  background: #409eff;\n  color: white;\n}\n</style>"], "mappings": ";;EACOA,KAAK,EAAC;AAAkB;;EAGpBA,KAAK,EAAC;AAAe;;;;uBAH9BC,mBAAA,CAoBM,OApBNC,UAoBM,GAnBJC,YAAA,CAkBUC,kBAAA;IAlBDJ,KAAK,EAAC;EAAe;sBAC5B,MAAsC,C,0BAAtCK,mBAAA,CAAsC;MAAjCL,KAAK,EAAC;IAAgB,GAAC,MAAI,qBAChCK,mBAAA,CAeM,OAfNC,UAeM,GAdJH,YAAA,CAKYI,oBAAA;MAJTP,KAAK,EAAAQ,eAAA;QAAA,UAA+BC,KAAA,CAAAC,gBAAgB;MAAA;MACpDC,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,QAAA,CAAAC,mBAAmB;;wBAC5B,MAED,KAAAH,MAAA,QAAAA,MAAA,O,iBAFC,QAED,mB;;qDACAX,mBAAA,CAOYe,SAAA,QAAAC,WAAA,CANSR,KAAA,CAAAS,UAAU,EAAtBC,QAAQ;2BADjBC,YAAA,CAOYb,oBAAA;QALTc,GAAG,EAAEF,QAAQ,CAACG,EAAE;QAChBtB,KAAK,EAAAQ,eAAA;UAAA,UAA+BC,KAAA,CAAAC,gBAAgB,KAAKS,QAAQ,CAACG;QAAE;QACpEX,OAAK,EAAAE,MAAA,IAAEC,QAAA,CAAAC,mBAAmB,CAACI,QAAQ;;0BAEpC,MAAmB,C,kCAAhBA,QAAQ,CAACI,IAAI,iB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}