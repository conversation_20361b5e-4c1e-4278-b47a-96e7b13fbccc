package com.cn.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cn.constants.Constants;
import com.cn.entity.Good;
import com.cn.entity.GoodStandard;
import com.cn.entity.dto.GoodDTO;
import com.cn.exception.ServiceException;
import com.cn.mapper.GoodMapper;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.cn.constants.RedisConstants.GOOD_TOKEN_KEY;
import static com.cn.constants.RedisConstants.GOOD_TOKEN_TTL;

@Service
public class GoodService extends ServiceImpl<GoodMapper, Good> {

    @Resource
    private GoodMapper goodMapper;
    @Resource
    private RedisTemplate<String, Good> redisTemplate;

    //查询某商品的最低规格价
    public BigDecimal getMinPrice(Long id){
        return goodMapper.getMinPrice(id);
    }

    //分页查询
    public IPage<GoodDTO> findPage(Integer pageNum, Integer pageSize, String searchText, Integer categoryId) {
        LambdaQueryWrapper<Good> query = Wrappers.<Good>lambdaQuery().orderByDesc(Good::getId);
        //对名称和描述进行模糊查询
        if (StrUtil.isNotBlank(searchText)) {
            query.like(Good::getName, searchText).or().like(Good::getDescription,searchText).or().eq(Good::getId,searchText);
        }
        if(categoryId != null){
            query.eq(Good::getCategoryId,categoryId);
        }
        //筛除掉已被删除的商品
        query.eq(Good::getIsDelete,false);
        IPage<Good> page = this.page(new Page<>(pageNum, pageSize), query);
        //把good转为dto
        IPage<GoodDTO> goodDTOPage = page.convert(good -> {
            GoodDTO goodDTO = new GoodDTO();
            BeanUtil.copyProperties(good, goodDTO);
            return goodDTO;
        });
        for (GoodDTO good : goodDTOPage.getRecords()) {
            //附上最低价格
            good.setPrice(getMinPrice(good.getId()));
        }
        return goodDTOPage;
    }


    /**
     * 前端首页推荐商品
     * @return
     */
    public List<GoodDTO> findFrontGoods(){
        return goodMapper.findFrontGoods();
    }

    //保存商品信息
    public Long saveOrUpdateGood(Good good) {
        System.out.println(good);
        if(good.getId()==null){
            DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            good.setCreateTime(df.format(LocalDateTime.now()));
            goodMapper.insertGood(good);
        }else{
            saveOrUpdate(good);
            //redisTemplate.delete(GOOD_TOKEN_KEY + good.getId());
        }
        return good.getId();
    }

    public void update(Good good) {
        updateById(good);
        //redisTemplate.delete(GOOD_TOKEN_KEY + good.getId());
    }
    //假删除
    public void deleteGood(Long id) {
        //redisTemplate.delete(GOOD_TOKEN_KEY+id);
        goodMapper.fakeDelete(id);
    }
    //查询一个商品的信息
    public Good getGoodById(Long id) {
        String redisKey = GOOD_TOKEN_KEY + id;
        //从redis中查，若有则返回
        ValueOperations<String, Good> valueOperations = redisTemplate.opsForValue();
        Good redisGood = valueOperations.get(redisKey);
        if(redisGood!=null){
            redisTemplate.expire(redisKey,GOOD_TOKEN_TTL, TimeUnit.MINUTES);
            return redisGood;
        }
        //若redis中没有则去数据库查
        LambdaQueryWrapper<Good> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Good::getIsDelete,false);
        queryWrapper.eq(Good::getId,id);
        Good dbGood = getOne(queryWrapper);
        if(dbGood!=null){
            //将商品信息存入redis
            valueOperations.set(redisKey,dbGood);
            redisTemplate.expire(redisKey,GOOD_TOKEN_TTL, TimeUnit.MINUTES);
            return dbGood;
        }
        //数据库中没有则返回异常
        throw new ServiceException(Constants.NO_RESULT,"无结果");

    }
    //查询商品的规格
    public String getStandard(int id){
        List<GoodStandard> standards = goodMapper.getStandardById(id);
        if(standards.size()==0){
            throw new ServiceException(Constants.NO_RESULT,"无结果");
        }
        return JSON.toJSONString(standards);
    }
    public List<Good> getSaleRank(int num) {
        return goodMapper.getSaleRank(num);
    }

}
