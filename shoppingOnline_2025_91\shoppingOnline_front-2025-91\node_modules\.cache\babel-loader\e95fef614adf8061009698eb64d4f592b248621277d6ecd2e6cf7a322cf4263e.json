{"ast": null, "code": "import { renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, normalizeStyle as _normalizeStyle, createCommentVNode as _createCommentVNode, normalizeClass as _normalizeClass, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx } from \"vue\";\nconst _hoisted_1 = {\n  class: \"carousel-container\"\n};\nconst _hoisted_2 = {\n  key: 0,\n  class: \"carousel-wrapper\"\n};\nconst _hoisted_3 = [\"onClick\"];\nconst _hoisted_4 = [\"src\", \"alt\"];\nconst _hoisted_5 = {\n  class: \"carousel-overlay\"\n};\nconst _hoisted_6 = {\n  class: \"carousel-title\"\n};\nconst _hoisted_7 = {\n  class: \"carousel-indicators\"\n};\nconst _hoisted_8 = [\"onClick\"];\nconst _hoisted_9 = {\n  class: \"carousel-loading\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_skeleton_item = _resolveComponent(\"el-skeleton-item\");\n  const _component_el_skeleton = _resolveComponent(\"el-skeleton\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [$data.carouselList.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_2, [_createElementVNode(\"div\", {\n    class: \"carousel-content\",\n    style: _normalizeStyle({\n      transform: `translateX(-${$data.currentIndex * 100}%)`\n    })\n  }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.carouselList, item => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      class: \"carousel-item\",\n      key: item.id,\n      onClick: $event => $options.handleCarouselClick(item)\n    }, [_createElementVNode(\"img\", {\n      src: item.img,\n      alt: item.goodName,\n      class: \"carousel-image\"\n    }, null, 8 /* PROPS */, _hoisted_4), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"h3\", _hoisted_6, _toDisplayString(item.goodName), 1 /* TEXT */)])], 8 /* PROPS */, _hoisted_3);\n  }), 128 /* KEYED_FRAGMENT */))], 4 /* STYLE */), _createCommentVNode(\" 指示器 \"), _createElementVNode(\"div\", _hoisted_7, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.carouselList, (item, index) => {\n    return _openBlock(), _createElementBlock(\"span\", {\n      key: index,\n      class: _normalizeClass(['indicator', {\n        active: index === $data.currentIndex\n      }]),\n      onClick: $event => $options.goToSlide(index)\n    }, null, 10 /* CLASS, PROPS */, _hoisted_8);\n  }), 128 /* KEYED_FRAGMENT */))]), _createCommentVNode(\" 导航按钮 \"), $data.carouselList.length > 1 ? (_openBlock(), _createElementBlock(\"button\", {\n    key: 0,\n    class: \"carousel-btn prev-btn\",\n    onClick: _cache[0] || (_cache[0] = (...args) => $options.prevSlide && $options.prevSlide(...args))\n  }, [...(_cache[2] || (_cache[2] = [_createElementVNode(\"i\", {\n    class: \"el-icon-arrow-left\"\n  }, null, -1 /* CACHED */)]))])) : _createCommentVNode(\"v-if\", true), $data.carouselList.length > 1 ? (_openBlock(), _createElementBlock(\"button\", {\n    key: 1,\n    class: \"carousel-btn next-btn\",\n    onClick: _cache[1] || (_cache[1] = (...args) => $options.nextSlide && $options.nextSlide(...args))\n  }, [...(_cache[3] || (_cache[3] = [_createElementVNode(\"i\", {\n    class: \"el-icon-arrow-right\"\n  }, null, -1 /* CACHED */)]))])) : _createCommentVNode(\"v-if\", true)])) : $data.loading ? (_openBlock(), _createElementBlock(_Fragment, {\n    key: 1\n  }, [_createCommentVNode(\" 加载状态 \"), _createElementVNode(\"div\", _hoisted_9, [_createVNode(_component_el_skeleton, {\n    animated: \"\"\n  }, {\n    template: _withCtx(() => [_createVNode(_component_el_skeleton_item, {\n      variant: \"image\",\n      style: {\n        \"width\": \"100%\",\n        \"height\": \"300px\"\n      }\n    })]),\n    _: 1 /* STABLE */\n  })])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)) : _createCommentVNode(\"v-if\", true)]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "$data", "carouselList", "length", "_hoisted_2", "_createElementVNode", "style", "_normalizeStyle", "transform", "currentIndex", "_Fragment", "_renderList", "item", "key", "id", "onClick", "$event", "$options", "handleCarouselClick", "src", "img", "alt", "<PERSON><PERSON><PERSON>", "_hoisted_5", "_hoisted_6", "_toDisplayString", "_createCommentVNode", "_hoisted_7", "index", "_normalizeClass", "active", "goToSlide", "_cache", "args", "prevSlide", "nextSlide", "loading", "_hoisted_9", "_createVNode", "_component_el_skeleton", "animated", "template", "_withCtx", "_component_el_skeleton_item", "variant"], "sources": ["D:\\2025_down\\project\\shoppingOnline_2025_91\\shoppingOnline_front-2025-91\\src\\components\\Carousel.vue"], "sourcesContent": ["<template>\n  <div class=\"carousel-container\">\n    <div class=\"carousel-wrapper\" v-if=\"carouselList.length > 0\">\n      <div class=\"carousel-content\" :style=\"{ transform: `translateX(-${currentIndex * 100}%)` }\">\n        <div\n          class=\"carousel-item\"\n          v-for=\"item in carouselList\"\n          :key=\"item.id\"\n          @click=\"handleCarouselClick(item)\"\n        >\n          <img :src=\"item.img\" :alt=\"item.goodName\" class=\"carousel-image\" />\n          <div class=\"carousel-overlay\">\n            <h3 class=\"carousel-title\">{{ item.goodName }}</h3>\n          </div>\n        </div>\n      </div>\n      \n      <!-- 指示器 -->\n      <div class=\"carousel-indicators\">\n        <span \n          v-for=\"(item, index) in carouselList\" \n          :key=\"index\"\n          :class=\"['indicator', { active: index === currentIndex }]\"\n          @click=\"goToSlide(index)\"\n        ></span>\n      </div>\n      \n      <!-- 导航按钮 -->\n      <button class=\"carousel-btn prev-btn\" @click=\"prevSlide\" v-if=\"carouselList.length > 1\">\n        <i class=\"el-icon-arrow-left\"></i>\n      </button>\n      <button class=\"carousel-btn next-btn\" @click=\"nextSlide\" v-if=\"carouselList.length > 1\">\n        <i class=\"el-icon-arrow-right\"></i>\n      </button>\n    </div>\n    \n    <!-- 加载状态 -->\n    <div v-else-if=\"loading\" class=\"carousel-loading\">\n      <el-skeleton animated>\n        <template #template>\n          <el-skeleton-item variant=\"image\" style=\"width: 100%; height: 300px;\" />\n        </template>\n      </el-skeleton>\n    </div>\n  </div>\n</template>\n\n<script>\nimport request from '@/utils/request.js'\n\nexport default {\n  name: 'CarouselComponent',\n  \n  data() {\n    return {\n      carouselList: [],\n      currentIndex: 0,\n      loading: true,\n      autoPlayTimer: null,\n      autoPlayInterval: 4000 // 4秒自动切换\n    }\n  },\n  \n  mounted() {\n    this.fetchCarouselData()\n\n    // 防止 ResizeObserver 错误\n    this.$nextTick(() => {\n      // 确保DOM完全渲染后再执行相关操作\n    })\n  },\n  \n  beforeUnmount() {\n    this.clearAutoPlay()\n  },\n  \n  methods: {\n    // 获取轮播图数据\n    async fetchCarouselData() {\n      try {\n        this.loading = true\n        const response = await request.get('/carousel')\n        this.carouselList = Array.isArray(response) ? response : []\n\n        if (this.carouselList.length > 0) {\n          this.startAutoPlay()\n        }\n      } catch (error) {\n        console.error('获取轮播图数据失败:', error)\n        this.carouselList = []\n      } finally {\n        this.loading = false\n      }\n    },\n    \n    // 下一张\n    nextSlide() {\n      if (this.carouselList.length === 0) return\n      this.currentIndex = (this.currentIndex + 1) % this.carouselList.length\n    },\n    \n    // 上一张\n    prevSlide() {\n      if (this.carouselList.length === 0) return\n      this.currentIndex = this.currentIndex === 0 ? this.carouselList.length - 1 : this.currentIndex - 1\n    },\n    \n    // 跳转到指定幻灯片\n    goToSlide(index) {\n      this.currentIndex = index\n      this.resetAutoPlay()\n    },\n    \n    // 开始自动播放\n    startAutoPlay() {\n      if (this.carouselList.length <= 1) return\n      this.autoPlayTimer = setInterval(() => {\n        this.nextSlide()\n      }, this.autoPlayInterval)\n    },\n    \n    // 清除自动播放\n    clearAutoPlay() {\n      if (this.autoPlayTimer) {\n        clearInterval(this.autoPlayTimer)\n        this.autoPlayTimer = null\n      }\n    },\n    \n    // 重置自动播放\n    resetAutoPlay() {\n      this.clearAutoPlay()\n      this.startAutoPlay()\n    },\n    \n    // 点击轮播图项目\n    handleCarouselClick(item) {\n      // 可以跳转到商品详情页或触发其他事件\n      this.$emit('carousel-click', item)\n      console.log('点击轮播图商品:', item.goodName)\n    }\n  }\n}\n</script>\n\n<style scoped>\n.carousel-container {\n  width: 100%;\n  margin-bottom: 30px;\n}\n\n.carousel-wrapper {\n  position: relative;\n  width: 100%;\n  height: 300px;\n  overflow: hidden;\n  border-radius: 12px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n}\n\n.carousel-content {\n  display: flex;\n  width: 100%;\n  height: 100%;\n  transition: transform 0.5s ease-in-out;\n  will-change: transform;\n}\n\n.carousel-item {\n  flex: 0 0 100%;\n  position: relative;\n  cursor: pointer;\n}\n\n.carousel-image {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.carousel-overlay {\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));\n  color: white;\n  padding: 20px;\n}\n\n.carousel-title {\n  margin: 0;\n  font-size: 24px;\n  font-weight: bold;\n}\n\n.carousel-indicators {\n  position: absolute;\n  bottom: 15px;\n  left: 50%;\n  transform: translateX(-50%);\n  display: flex;\n  gap: 8px;\n}\n\n.indicator {\n  width: 12px;\n  height: 12px;\n  border-radius: 50%;\n  background: rgba(255, 255, 255, 0.5);\n  cursor: pointer;\n  transition: background 0.3s;\n}\n\n.indicator.active {\n  background: white;\n}\n\n.carousel-btn {\n  position: absolute;\n  top: 50%;\n  transform: translateY(-50%);\n  background: rgba(0, 0, 0, 0.5);\n  color: white;\n  border: none;\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: background 0.3s;\n  font-size: 18px;\n}\n\n.carousel-btn:hover {\n  background: rgba(0, 0, 0, 0.7);\n}\n\n.prev-btn {\n  left: 15px;\n}\n\n.next-btn {\n  right: 15px;\n}\n\n.carousel-loading {\n  width: 100%;\n  height: 300px;\n  border-radius: 12px;\n  overflow: hidden;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .carousel-wrapper {\n    height: 200px;\n  }\n  \n  .carousel-title {\n    font-size: 18px;\n  }\n  \n  .carousel-btn {\n    width: 35px;\n    height: 35px;\n    font-size: 16px;\n  }\n  \n  .prev-btn {\n    left: 10px;\n  }\n  \n  .next-btn {\n    right: 10px;\n  }\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAoB;;;EACxBA,KAAK,EAAC;;;;;EASAA,KAAK,EAAC;AAAkB;;EACvBA,KAAK,EAAC;AAAgB;;EAM3BA,KAAK,EAAC;AAAqB;;;EAmBTA,KAAK,EAAC;AAAkB;;;;uBApCnDC,mBAAA,CA2CM,OA3CNC,UA2CM,GA1CgCC,KAAA,CAAAC,YAAY,CAACC,MAAM,Q,cAAvDJ,mBAAA,CAgCM,OAhCNK,UAgCM,GA/BJC,mBAAA,CAYM;IAZDP,KAAK,EAAC,kBAAkB;IAAEQ,KAAK,EAAAC,eAAA;MAAAC,SAAA,iBAA8BP,KAAA,CAAAQ,YAAY;IAAA;yBAC5EV,mBAAA,CAUMW,SAAA,QAAAC,WAAA,CARWV,KAAA,CAAAC,YAAY,EAApBU,IAAI;yBAFbb,mBAAA,CAUM;MATJD,KAAK,EAAC,eAAe;MAEpBe,GAAG,EAAED,IAAI,CAACE,EAAE;MACZC,OAAK,EAAAC,MAAA,IAAEC,QAAA,CAAAC,mBAAmB,CAACN,IAAI;QAEhCP,mBAAA,CAAmE;MAA7Dc,GAAG,EAAEP,IAAI,CAACQ,GAAG;MAAGC,GAAG,EAAET,IAAI,CAACU,QAAQ;MAAExB,KAAK,EAAC;yCAChDO,mBAAA,CAEM,OAFNkB,UAEM,GADJlB,mBAAA,CAAmD,MAAnDmB,UAAmD,EAAAC,gBAAA,CAArBb,IAAI,CAACU,QAAQ,iB;mDAKjDI,mBAAA,SAAY,EACZrB,mBAAA,CAOM,OAPNsB,UAOM,I,kBANJ5B,mBAAA,CAKQW,SAAA,QAAAC,WAAA,CAJkBV,KAAA,CAAAC,YAAY,GAA5BU,IAAI,EAAEgB,KAAK;yBADrB7B,mBAAA,CAKQ;MAHLc,GAAG,EAAEe,KAAK;MACV9B,KAAK,EAAA+B,eAAA;QAAAC,MAAA,EAA0BF,KAAK,KAAK3B,KAAA,CAAAQ;MAAY;MACrDM,OAAK,EAAAC,MAAA,IAAEC,QAAA,CAAAc,SAAS,CAACH,KAAK;;oCAI3BF,mBAAA,UAAa,EACkDzB,KAAA,CAAAC,YAAY,CAACC,MAAM,Q,cAAlFJ,mBAAA,CAES;;IAFDD,KAAK,EAAC,uBAAuB;IAAEiB,OAAK,EAAAiB,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEhB,QAAA,CAAAiB,SAAA,IAAAjB,QAAA,CAAAiB,SAAA,IAAAD,IAAA,CAAS;qCACrD5B,mBAAA,CAAkC;IAA/BP,KAAK,EAAC;EAAoB,0B,2CAEgCG,KAAA,CAAAC,YAAY,CAACC,MAAM,Q,cAAlFJ,mBAAA,CAES;;IAFDD,KAAK,EAAC,uBAAuB;IAAEiB,OAAK,EAAAiB,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEhB,QAAA,CAAAkB,SAAA,IAAAlB,QAAA,CAAAkB,SAAA,IAAAF,IAAA,CAAS;qCACrD5B,mBAAA,CAAmC;IAAhCP,KAAK,EAAC;EAAqB,0B,+CAKlBG,KAAA,CAAAmC,OAAO,I,cAAvBrC,mBAAA,CAMMW,SAAA;IAAAG,GAAA;EAAA,IAPNa,mBAAA,UAAa,EACbrB,mBAAA,CAMM,OANNgC,UAMM,GALJC,YAAA,CAIcC,sBAAA;IAJDC,QAAQ,EAAR;EAAQ;IACRC,QAAQ,EAAAC,QAAA,CACjB,MAAwE,CAAxEJ,YAAA,CAAwEK,2BAAA;MAAtDC,OAAO,EAAC,OAAO;MAACtC,KAAmC,EAAnC;QAAA;QAAA;MAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}