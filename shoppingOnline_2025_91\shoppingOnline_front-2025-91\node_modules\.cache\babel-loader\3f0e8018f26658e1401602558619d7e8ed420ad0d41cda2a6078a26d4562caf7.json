{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, withCtx as _withCtx, openBlock as _openBlock, createElementBlock as _createElementBlock, renderList as _renderList, Fragment as _Fragment } from \"vue\";\nconst _hoisted_1 = {\n  class: \"goods-container\"\n};\nconst _hoisted_2 = {\n  key: 0,\n  class: \"search-info\"\n};\nconst _hoisted_3 = {\n  class: \"search-count\"\n};\nconst _hoisted_4 = {\n  key: 1,\n  class: \"loading\"\n};\nconst _hoisted_5 = {\n  class: \"goods-list\"\n};\nconst _hoisted_6 = [\"src\", \"alt\"];\nconst _hoisted_7 = {\n  class: \"goods-info\"\n};\nconst _hoisted_8 = {\n  class: \"goods-name\"\n};\nconst _hoisted_9 = {\n  key: 0,\n  class: \"goods-desc\"\n};\nconst _hoisted_10 = {\n  class: \"goods-price\"\n};\nconst _hoisted_11 = [\"onClick\"];\nconst _hoisted_12 = {\n  key: 3,\n  class: \"no-data\"\n};\nconst _hoisted_13 = {\n  key: 4,\n  class: \"pagination-wrapper\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_CarouselComponent = _resolveComponent(\"CarouselComponent\");\n  const _component_SearchBox = _resolveComponent(\"SearchBox\");\n  const _component_el_tag = _resolveComponent(\"el-tag\");\n  const _component_el_skeleton = _resolveComponent(\"el-skeleton\");\n  const _component_el_empty = _resolveComponent(\"el-empty\");\n  const _component_el_pagination = _resolveComponent(\"el-pagination\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" 页面标题 \"), _cache[4] || (_cache[4] = _createElementVNode(\"h1\", {\n    class: \"page-title\"\n  }, \"在线购物商城\", -1 /* CACHED */)), _createCommentVNode(\" 轮播图组件 \"), _createVNode(_component_CarouselComponent, {\n    onCarouselClick: $options.handleCarouselClick\n  }, null, 8 /* PROPS */, [\"onCarouselClick\"]), _createCommentVNode(\" 搜索框组件 \"), _createVNode(_component_SearchBox, {\n    onSearch: $options.handleSearch,\n    onSearchResults: $options.handleSearchResults,\n    onClearSearch: $options.handleClearSearch\n  }, null, 8 /* PROPS */, [\"onSearch\", \"onSearchResults\", \"onClearSearch\"]), _createCommentVNode(\" 搜索结果提示 \"), $data.searchKeyword ? (_openBlock(), _createElementBlock(\"div\", _hoisted_2, [_createVNode(_component_el_tag, {\n    type: \"info\",\n    closable: \"\",\n    onClose: $options.handleClearSearch\n  }, {\n    default: _withCtx(() => [_createTextVNode(\" 搜索结果：\" + _toDisplayString($data.searchKeyword), 1 /* TEXT */)]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClose\"]), _createElementVNode(\"span\", _hoisted_3, \"共找到 \" + _toDisplayString($data.totalCount) + \" 件商品\", 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 加载状态 \"), $data.loading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_4, [_createVNode(_component_el_skeleton, {\n    rows: 3,\n    animated: \"\"\n  })])) : (_openBlock(), _createElementBlock(_Fragment, {\n    key: 2\n  }, [_createCommentVNode(\" 商品列表 \"), _createElementVNode(\"div\", _hoisted_5, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.goodsList, good => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      class: \"goods-card\",\n      key: good.id\n    }, [_createElementVNode(\"img\", {\n      src: good.imgs,\n      alt: good.name,\n      class: \"goods-image\",\n      onError: _cache[0] || (_cache[0] = (...args) => $options.handleImageError && $options.handleImageError(...args)),\n      onLoad: _cache[1] || (_cache[1] = (...args) => $options.handleImageLoad && $options.handleImageLoad(...args))\n    }, null, 40 /* PROPS, NEED_HYDRATION */, _hoisted_6), _createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"h3\", _hoisted_8, _toDisplayString(good.name), 1 /* TEXT */), good.description ? (_openBlock(), _createElementBlock(\"p\", _hoisted_9, _toDisplayString(good.description), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"p\", _hoisted_10, [_createElementVNode(\"strong\", null, \"¥\" + _toDisplayString(good.price), 1 /* TEXT */)]), _createElementVNode(\"button\", {\n      class: \"btn-add-cart\",\n      onClick: $event => $options.addToCart(good)\n    }, \" 加入购物车 \", 8 /* PROPS */, _hoisted_11)])]);\n  }), 128 /* KEYED_FRAGMENT */))])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)), _createCommentVNode(\" 无数据\"), !$data.loading && $data.goodsList.length === 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_12, [_createVNode(_component_el_empty, {\n    description: \"暂无商品\"\n  })])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 分页组件 \"), $data.totalCount > $data.pageSize ? (_openBlock(), _createElementBlock(\"div\", _hoisted_13, [_createVNode(_component_el_pagination, {\n    \"current-page\": $data.currentPage,\n    \"onUpdate:currentPage\": _cache[2] || (_cache[2] = $event => $data.currentPage = $event),\n    \"page-size\": $data.pageSize,\n    \"onUpdate:pageSize\": _cache[3] || (_cache[3] = $event => $data.pageSize = $event),\n    \"page-sizes\": [10, 20, 50, 100],\n    total: $data.totalCount,\n    layout: \"total, sizes, prev, pager, next, jumper\",\n    onSizeChange: $options.handleSizeChange,\n    onCurrentChange: $options.handleCurrentChange\n  }, null, 8 /* PROPS */, [\"current-page\", \"page-size\", \"total\", \"onSizeChange\", \"onCurrentChange\"])])) : _createCommentVNode(\"v-if\", true)]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "_createVNode", "_component_CarouselComponent", "onCarouselClick", "$options", "handleCarouselClick", "_component_SearchBox", "onSearch", "handleSearch", "onSearchResults", "handleSearchResults", "onClearSearch", "handleClearSearch", "$data", "searchKeyword", "_hoisted_2", "_component_el_tag", "type", "closable", "onClose", "_toDisplayString", "_hoisted_3", "totalCount", "loading", "_hoisted_4", "_component_el_skeleton", "rows", "animated", "_Fragment", "key", "_hoisted_5", "_renderList", "goodsList", "good", "id", "src", "imgs", "alt", "name", "onError", "_cache", "args", "handleImageError", "onLoad", "handleImageLoad", "_hoisted_7", "_hoisted_8", "description", "_hoisted_9", "_hoisted_10", "price", "onClick", "$event", "addToCart", "_hoisted_11", "length", "_hoisted_12", "_component_el_empty", "pageSize", "_hoisted_13", "_component_el_pagination", "currentPage", "total", "layout", "onSizeChange", "handleSizeChange", "onCurrentChange", "handleCurrentChange"], "sources": ["D:\\2025_down\\project\\shoppingOnline_2025_91\\shoppingOnline_front-2025-91\\src\\views\\goods.vue"], "sourcesContent": ["<template>\r\n  <div class=\"goods-container\">\r\n    <!-- 页面标题 -->\r\n    <h1 class=\"page-title\">在线购物商城</h1>\r\n\r\n    <!-- 轮播图组件 -->\r\n    <CarouselComponent @carousel-click=\"handleCarouselClick\" />\r\n\r\n    <!-- 搜索框组件 -->\r\n    <SearchBox\r\n      @search=\"handleSearch\"\r\n      @search-results=\"handleSearchResults\"\r\n      @clear-search=\"handleClearSearch\"\r\n    />\r\n\r\n    <!-- 搜索结果提示 -->\r\n    <div v-if=\"searchKeyword\" class=\"search-info\">\r\n      <el-tag type=\"info\" closable @close=\"handleClearSearch\">\r\n        搜索结果：{{ searchKeyword }}\r\n      </el-tag>\r\n      <span class=\"search-count\">共找到 {{ totalCount }} 件商品</span>\r\n    </div>\r\n\r\n    <!-- 加载状态 -->\r\n    <div v-if=\"loading\" class=\"loading\">\r\n      <el-skeleton :rows=\"3\" animated />\r\n    </div>\r\n\r\n    <!-- 商品列表 -->\r\n    <div v-else class=\"goods-list\">\r\n      <div\r\n        class=\"goods-card\"\r\n        v-for=\"good in goodsList\"\r\n        :key=\"good.id\"\r\n      >\r\n        <img\r\n          :src=\"good.imgs\"\r\n          :alt=\"good.name\"\r\n          class=\"goods-image\"\r\n          @error=\"handleImageError\"\r\n          @load=\"handleImageLoad\"\r\n        />\r\n        <div class=\"goods-info\">\r\n          <h3 class=\"goods-name\">{{ good.name }}</h3>\r\n          <p class=\"goods-desc\" v-if=\"good.description\">{{ good.description }}</p>\r\n          <p class=\"goods-price\"><strong>¥{{ good.price }}</strong></p>\r\n          <button class=\"btn-add-cart\" @click=\"addToCart(good)\">\r\n            加入购物车\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 无数据-->\r\n    <div v-if=\"!loading && goodsList.length === 0\" class=\"no-data\">\r\n      <el-empty description=\"暂无商品\" />\r\n    </div>\r\n\r\n    <!-- 分页组件 -->\r\n    <div v-if=\"totalCount > pageSize\" class=\"pagination-wrapper\">\r\n      <el-pagination\r\n        v-model:current-page=\"currentPage\"\r\n        v-model:page-size=\"pageSize\"\r\n        :page-sizes=\"[10, 20, 50, 100]\"\r\n        :total=\"totalCount\"\r\n        layout=\"total, sizes, prev, pager, next, jumper\"\r\n        @size-change=\"handleSizeChange\"\r\n        @current-change=\"handleCurrentChange\"\r\n      />\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport request from '@/utils/request.js'\r\nimport CarouselComponent from '@/components/Carousel.vue'\r\nimport SearchBox from '@/components/SearchBox.vue'\r\n\r\nexport default {\r\n  name: 'GoodsView',\r\n\r\n  components: {\r\n    CarouselComponent,\r\n    SearchBox\r\n  },\r\n\r\n  data() {\r\n    return {\r\n      name: '',\r\n      awesome: false,\r\n      loading: true,\r\n      goodsList: [],\r\n      searchKeyword: '', // 当前搜索关键词\r\n      currentPage: 1,\r\n      pageSize: 20,\r\n      totalCount: 0,\r\n      isSearchMode: false // 是否处于搜索模式\r\n    };\r\n  },\r\n\r\n  computed: {\r\n    // 计算商品总数\r\n    totalGoods() {\r\n      return this.goodsList.length;\r\n    }\r\n  },\r\n\r\n  watch: {\r\n    // 监听路由参数变化\r\n    '$route.query.search': {\r\n      handler(newSearch) {\r\n        if (newSearch) {\r\n          this.handleSearch(newSearch);\r\n        } else {\r\n          this.handleClearSearch();\r\n        }\r\n      },\r\n      immediate: false\r\n    }\r\n  },\r\n\r\n  created() {\r\n    // 检查是否有搜索参数\r\n    const searchQuery = this.$route.query.search;\r\n    if (searchQuery) {\r\n      this.handleSearch(searchQuery);\r\n    } else {\r\n      this.findFrontGoods();\r\n    }\r\n  },\r\n\r\n  mounted() {\r\n    // 调试：输出图片路径信息\r\n    console.log('当前页面URL:', window.location.href);\r\n    console.log('图片路径测试:', './images/women-top.jpg');\r\n    console.log('绝对路径测试:', '/images/women-top.jpg');\r\n\r\n    // 查询推荐商品\r\n    this.findFrontGoods();\r\n  },\r\n\r\n  methods: {\r\n    // 图片加载错误处理\r\n    handleImageError(event) {\r\n      console.error('图片加载失败:', event.target.src);\r\n      console.error('图片元素:', event.target);\r\n      // 设置默认图片或隐藏图片\r\n      event.target.style.display = 'none';\r\n      event.target.style.backgroundColor = '#f0f0f0';\r\n      event.target.style.border = '1px solid #ccc';\r\n    },\r\n\r\n    // 图片加载成功处理\r\n    handleImageLoad(event) {\r\n      console.log('图片加载成功:', event.target.src);\r\n      console.log('图片尺寸:', event.target.naturalWidth, 'x', event.target.naturalHeight);\r\n    },\r\n\r\n    // 添加到购物车\r\n    addToCart(good) {\r\n      console.log('加入购物车:', good.name);\r\n      this.$emit('add-to-cart', good);\r\n      this.$message.success(`已加入购物车：${good.name}`);\r\n    },\r\n\r\n    // 查询推荐商品\r\n    async findFrontGoods() {\r\n      try {\r\n        this.loading = true;\r\n\r\n        // 直接使用本地图片的模拟数据确保图片显示\r\n        this.goodsList = this.getMockProductsWithLocalImages();\r\n        console.log('使用本地图片的模拟商品数据:', this.goodsList);\r\n\r\n        this.totalCount = this.goodsList.length;\r\n        this.isSearchMode = false;\r\n      } catch (error) {\r\n        console.error('请求失败:', error.message || error);\r\n        // 使用本地图片的模拟数据作为后备\r\n        this.goodsList = this.getMockProductsWithLocalImages();\r\n        this.totalCount = this.goodsList.length;\r\n        console.log('API请求失败，使用本地图片的模拟数据:', this.goodsList);\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n\r\n    // 获取使用本地图片的模拟商品数据\r\n    getMockProductsWithLocalImages() {\r\n      return [\r\n        {\r\n          id: 1,\r\n          name: '女上衣',\r\n          description: '时尚女性上衣，舒适面料，多种颜色可选',\r\n          imgs: '/images/women-top.jpg',\r\n          price: 102.00,\r\n          discount: 0.85,\r\n          sales: 120,\r\n          recommend: true\r\n        },\r\n        {\r\n          id: 2,\r\n          name: '休闲鞋',\r\n          description: '舒适透气的休闲运动鞋，适合日常穿着',\r\n          imgs: '/images/casual-shoes.jpg',\r\n          price: 162.00,\r\n          discount: 0.90,\r\n          sales: 85,\r\n          recommend: true\r\n        },\r\n        {\r\n          id: 3,\r\n          name: '威士忌 大瓶',\r\n          description: '优质威士忌，口感醇厚，适合收藏和品鉴',\r\n          imgs: '/images/whiskey.jpg',\r\n          price: 427.50,\r\n          discount: 0.95,\r\n          sales: 45,\r\n          recommend: true\r\n        }\r\n      ];\r\n    },\r\n\r\n    // 处理搜索\r\n    async handleSearch(keyword) {\r\n      this.searchKeyword = keyword;\r\n      this.isSearchMode = true;\r\n      this.currentPage = 1;\r\n      await this.searchGoods();\r\n    },\r\n\r\n    // 搜索商品\r\n    async searchGoods() {\r\n      try {\r\n        this.loading = true;\r\n        const response = await request.get('/good/page', {\r\n          searchText: this.searchKeyword,\r\n          pageNum: this.currentPage,\r\n          pageSize: this.pageSize\r\n        });\r\n\r\n        if (response && response.records) {\r\n          this.goodsList = response.records;\r\n          this.totalCount = response.total || 0;\r\n        } else {\r\n          this.goodsList = [];\r\n          this.totalCount = 0;\r\n        }\r\n      } catch (error) {\r\n        console.error('搜索失败:', error);\r\n        this.goodsList = [];\r\n        this.totalCount = 0;\r\n        this.$message.error('搜索失败，请重试');\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n\r\n    // 处理搜索结果\r\n    handleSearchResults(results) {\r\n      if (results && results.records) {\r\n        this.goodsList = results.records;\r\n        this.totalCount = results.total || 0;\r\n      }\r\n    },\r\n\r\n    // 清除搜索\r\n    handleClearSearch() {\r\n      this.searchKeyword = '';\r\n      this.isSearchMode = false;\r\n      this.currentPage = 1;\r\n      this.findFrontGoods();\r\n    },\r\n\r\n    // 处理轮播图点击\r\n    handleCarouselClick(item) {\r\n      console.log('点击轮播图商品:', item);\r\n      // 可以跳转到商品详情页\r\n      // this.$router.push(`/goods/${item.goodId}`);\r\n    },\r\n\r\n    // 处理页面大小变化\r\n    handleSizeChange(newSize) {\r\n      this.pageSize = newSize;\r\n      this.currentPage = 1;\r\n      if (this.isSearchMode) {\r\n        this.searchGoods();\r\n      } else {\r\n        this.findFrontGoods();\r\n      }\r\n    },\r\n\r\n    // 处理当前页变化\r\n    handleCurrentChange(newPage) {\r\n      this.currentPage = newPage;\r\n      if (this.isSearchMode) {\r\n        this.searchGoods();\r\n      } else {\r\n        this.findFrontGoods();\r\n      }\r\n    }\r\n  },\r\n\r\n\r\n\r\n  beforeUnmount() {\r\n    console.log('商品页面即将卸载');\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.goods-container {\r\n  max-width: 1200px;\r\n  margin: 20px auto;\r\n  padding: 0 16px;\r\n}\r\n\r\n.page-title {\r\n  text-align: center;\r\n  color: #333;\r\n  margin-bottom: 30px;\r\n  font-size: 32px;\r\n  font-weight: bold;\r\n  background: linear-gradient(45deg, #409eff, #67c23a);\r\n  -webkit-background-clip: text;\r\n  -webkit-text-fill-color: transparent;\r\n  background-clip: text;\r\n}\r\n\r\n.search-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 15px;\r\n  margin-bottom: 20px;\r\n  padding: 15px;\r\n  background: #f8f9fa;\r\n  border-radius: 8px;\r\n  border-left: 4px solid #409eff;\r\n}\r\n\r\n.search-count {\r\n  color: #666;\r\n  font-size: 14px;\r\n}\r\n\r\n.loading {\r\n  padding: 40px 0;\r\n}\r\n\r\n.no-data {\r\n  text-align: center;\r\n  padding: 60px 0;\r\n}\r\n\r\n.goods-list {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\r\n  gap: 24px;\r\n  margin-bottom: 30px;\r\n}\r\n\r\n.goods-card {\r\n  border: 1px solid #e0e0e0;\r\n  border-radius: 16px;\r\n  overflow: hidden;\r\n  background: white;\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);\r\n  transition: all 0.3s ease;\r\n  position: relative;\r\n}\r\n\r\n.goods-card:hover {\r\n  transform: translateY(-8px);\r\n  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);\r\n  border-color: #409eff;\r\n}\r\n\r\n.goods-image {\r\n  width: 100%;\r\n  height: 200px;\r\n  object-fit: cover;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.goods-card:hover .goods-image {\r\n  transform: scale(1.05);\r\n}\r\n\r\n.goods-info {\r\n  padding: 20px;\r\n}\r\n\r\n.goods-name {\r\n  font-size: 18px;\r\n  color: #333;\r\n  margin: 0 0 8px;\r\n  font-weight: bold;\r\n  line-height: 1.4;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n  overflow: hidden;\r\n}\r\n\r\n.goods-desc {\r\n  color: #666;\r\n  font-size: 14px;\r\n  margin: 0 0 12px;\r\n  line-height: 1.5;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n  overflow: hidden;\r\n}\r\n\r\n.goods-price {\r\n  color: #e60000;\r\n  margin: 15px 0;\r\n  font-size: 20px;\r\n  font-weight: bold;\r\n}\r\n\r\n.btn-add-cart {\r\n  width: 100%;\r\n  padding: 12px;\r\n  background: linear-gradient(45deg, #409eff, #67c23a);\r\n  color: white;\r\n  border: none;\r\n  border-radius: 8px;\r\n  cursor: pointer;\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  transition: all 0.3s ease;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.btn-add-cart:hover {\r\n  background: linear-gradient(45deg, #337ecc, #5daf34);\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);\r\n}\r\n\r\n.btn-add-cart:active {\r\n  transform: translateY(0);\r\n}\r\n\r\n.pagination-wrapper {\r\n  display: flex;\r\n  justify-content: center;\r\n  margin-top: 40px;\r\n  padding: 20px 0;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .goods-container {\r\n    padding: 0 12px;\r\n  }\r\n\r\n  .page-title {\r\n    font-size: 24px;\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .goods-list {\r\n    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\r\n    gap: 16px;\r\n  }\r\n\r\n  .goods-card {\r\n    border-radius: 12px;\r\n  }\r\n\r\n  .goods-image {\r\n    height: 160px;\r\n  }\r\n\r\n  .goods-info {\r\n    padding: 16px;\r\n  }\r\n\r\n  .goods-name {\r\n    font-size: 16px;\r\n  }\r\n\r\n  .goods-price {\r\n    font-size: 18px;\r\n  }\r\n\r\n  .btn-add-cart {\r\n    padding: 10px;\r\n    font-size: 14px;\r\n  }\r\n\r\n  .search-info {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 8px;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .goods-list {\r\n    grid-template-columns: 1fr;\r\n  }\r\n\r\n  .page-title {\r\n    font-size: 20px;\r\n  }\r\n}\r\n</style>"], "mappings": ";;EACOA,KAAK,EAAC;AAAiB;;;EAeAA,KAAK,EAAC;;;EAIxBA,KAAK,EAAC;AAAc;;;EAIRA,KAAK,EAAC;;;EAKdA,KAAK,EAAC;AAAY;;;EAarBA,KAAK,EAAC;AAAY;;EACjBA,KAAK,EAAC;AAAY;;;EACnBA,KAAK,EAAC;;;EACNA,KAAK,EAAC;AAAa;;;;EASmBA,KAAK,EAAC;;;;EAKnBA,KAAK,EAAC;;;;;;;;;uBA1D1CC,mBAAA,CAqEM,OArENC,UAqEM,GApEJC,mBAAA,UAAa,E,0BACbC,mBAAA,CAAkC;IAA9BJ,KAAK,EAAC;EAAY,GAAC,QAAM,qBAE7BG,mBAAA,WAAc,EACdE,YAAA,CAA2DC,4BAAA;IAAvCC,eAAc,EAAEC,QAAA,CAAAC;EAAmB,8CAEvDN,mBAAA,WAAc,EACdE,YAAA,CAIEK,oBAAA;IAHCC,QAAM,EAAEH,QAAA,CAAAI,YAAY;IACpBC,eAAc,EAAEL,QAAA,CAAAM,mBAAmB;IACnCC,aAAY,EAAEP,QAAA,CAAAQ;6EAGjBb,mBAAA,YAAe,EACJc,KAAA,CAAAC,aAAa,I,cAAxBjB,mBAAA,CAKM,OALNkB,UAKM,GAJJd,YAAA,CAESe,iBAAA;IAFDC,IAAI,EAAC,MAAM;IAACC,QAAQ,EAAR,EAAQ;IAAEC,OAAK,EAAEf,QAAA,CAAAQ;;sBAAmB,MACjD,C,iBADiD,QACjD,GAAAQ,gBAAA,CAAGP,KAAA,CAAAC,aAAa,iB;;kCAEvBd,mBAAA,CAA0D,QAA1DqB,UAA0D,EAA/B,MAAI,GAAAD,gBAAA,CAAGP,KAAA,CAAAS,UAAU,IAAG,MAAI,gB,wCAGrDvB,mBAAA,UAAa,EACFc,KAAA,CAAAU,OAAO,I,cAAlB1B,mBAAA,CAEM,OAFN2B,UAEM,GADJvB,YAAA,CAAkCwB,sBAAA;IAApBC,IAAI,EAAE,CAAC;IAAEC,QAAQ,EAAR;yBAIzB9B,mBAAA,CAsBM+B,SAAA;IAAAC,GAAA;EAAA,IAvBN9B,mBAAA,UAAa,EACbC,mBAAA,CAsBM,OAtBN8B,UAsBM,I,kBArBJjC,mBAAA,CAoBM+B,SAAA,QAAAG,WAAA,CAlBWlB,KAAA,CAAAmB,SAAS,EAAjBC,IAAI;yBAFbpC,mBAAA,CAoBM;MAnBJD,KAAK,EAAC,YAAY;MAEjBiC,GAAG,EAAEI,IAAI,CAACC;QAEXlC,mBAAA,CAME;MALCmC,GAAG,EAAEF,IAAI,CAACG,IAAI;MACdC,GAAG,EAAEJ,IAAI,CAACK,IAAI;MACf1C,KAAK,EAAC,aAAa;MAClB2C,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAErC,QAAA,CAAAsC,gBAAA,IAAAtC,QAAA,CAAAsC,gBAAA,IAAAD,IAAA,CAAgB;MACvBE,MAAI,EAAAH,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAErC,QAAA,CAAAwC,eAAA,IAAAxC,QAAA,CAAAwC,eAAA,IAAAH,IAAA,CAAe;0DAExBzC,mBAAA,CAOM,OAPN6C,UAOM,GANJ7C,mBAAA,CAA2C,MAA3C8C,UAA2C,EAAA1B,gBAAA,CAAjBa,IAAI,CAACK,IAAI,kBACPL,IAAI,CAACc,WAAW,I,cAA5ClD,mBAAA,CAAwE,KAAxEmD,UAAwE,EAAA5B,gBAAA,CAAvBa,IAAI,CAACc,WAAW,oB,mCACjE/C,mBAAA,CAA6D,KAA7DiD,WAA6D,GAAtCjD,mBAAA,CAAkC,gBAA1B,GAAC,GAAAoB,gBAAA,CAAGa,IAAI,CAACiB,KAAK,iB,GAC7ClD,mBAAA,CAES;MAFDJ,KAAK,EAAC,cAAc;MAAEuD,OAAK,EAAAC,MAAA,IAAEhD,QAAA,CAAAiD,SAAS,CAACpB,IAAI;OAAG,SAEtD,iBAAAqB,WAAA,E;sFAKNvD,mBAAA,QAAW,E,CACCc,KAAA,CAAAU,OAAO,IAAIV,KAAA,CAAAmB,SAAS,CAACuB,MAAM,U,cAAvC1D,mBAAA,CAEM,OAFN2D,WAEM,GADJvD,YAAA,CAA+BwD,mBAAA;IAArBV,WAAW,EAAC;EAAM,G,wCAG9BhD,mBAAA,UAAa,EACFc,KAAA,CAAAS,UAAU,GAAGT,KAAA,CAAA6C,QAAQ,I,cAAhC7D,mBAAA,CAUM,OAVN8D,WAUM,GATJ1D,YAAA,CAQE2D,wBAAA;IAPQ,cAAY,EAAE/C,KAAA,CAAAgD,WAAW;gEAAXhD,KAAA,CAAAgD,WAAW,GAAAT,MAAA;IACzB,WAAS,EAAEvC,KAAA,CAAA6C,QAAQ;6DAAR7C,KAAA,CAAA6C,QAAQ,GAAAN,MAAA;IAC1B,YAAU,EAAE,iBAAiB;IAC7BU,KAAK,EAAEjD,KAAA,CAAAS,UAAU;IAClByC,MAAM,EAAC,yCAAyC;IAC/CC,YAAW,EAAE5D,QAAA,CAAA6D,gBAAgB;IAC7BC,eAAc,EAAE9D,QAAA,CAAA+D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}